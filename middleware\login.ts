export default defineNuxtRouteMiddleware(async (to: any) => {
  const token = useCookie("token") as Ref<string>;
  if (to.path === "/login" || to.path === "/") {
    if (token.value) {
      // Check if user has existing org/store context
      const { orgId, storeId, isOrgValid, isStoreValid } = useTabContext();
      const hasCompleteContext = isOrgValid.value && isStoreValid.value;

      console.log('🔍 [LOGIN MIDDLEWARE] User has token, checking context:', {
        orgId: orgId.value,
        storeId: storeId.value,
        hasCompleteContext
      });

      if (hasCompleteContext) {
        // User has complete context, redirect to feature page
        console.log('✅ [LOGIN MIDDLEWARE] Complete context found, redirecting to feature page');
        return navigateTo(`/feature?orgId=${orgId.value}&storeId=${storeId.value}`);
      } else {
        // No complete context, go to org-store to select
        console.log('⚠️ [LOGIN MIDDLEWARE] No complete context, redirecting to org-store');
        return navigateTo("/org-store");
      }
    }
    return;
  }
});
