<template>
  <OtpVerificationForm
    title="<PERSON><PERSON><PERSON> thực OTP"
    :description="`Mã OTP đã được gửi đến số điện thoại <span class='font-semibold text-primary'>${phoneNumber}</span>`"
    :phone-number="phoneNumber"
    :loading="loading"
    :error="errors.otp"
    :sms-countdown="smsCountdown"
    :zalo-countdown="zaloCountdown"
    :otp-expiry="otpExpiry"
    submit-text="Xác thực OTP"
    loading-text="Đang xác thực..."
    @submit="handleOtpVerification"
    @resend="handleResendOtp"
    @resend-zalo="handleResendOtpZalo"
  />
</template>

<script setup lang="ts">
useHead({
  title: "Xác thực OTP",
  meta: [
    { name: "description", content: "Xác thực mã OTP để hoàn tất đăng ký" }
  ]
});

const route = useRoute();
const router = useRouter();

// Extract params from route
const phoneNumber = ref((route.query.phone as string) || "");
const customerName = ref((route.query.customerName as string) || "");
const userLoginId = ref((route.query.userLoginId as string) || "");
const serviceType = ref((route.query.serviceType as string) || "POS");

// Form state
const loading = ref(false);
const smsCountdown = ref(0);
const zaloCountdown = ref(0);
const otpExpiry = ref(300); // 5 minutes default

// Error states
const errors = ref({
  otp: "",
});

// OTP verification handler
const handleOtpVerification = async (otpValue: string) => {
  console.log('🔍 OTP verification started with:', otpValue);
  
  // Clear previous errors
  errors.value.otp = '';
  
  if (!otpValue || otpValue.length !== 6) {
    errors.value.otp = "Vui lòng nhập đầy đủ 6 số";
    return;
  }

  loading.value = true;

  try {
    console.log('📡 Calling SDK validateOTP with:', { otp: otpValue, phone: phoneNumber.value });
    
    const { $sdk } = useNuxtApp();
    const verifyResponse = await $sdk.auth.validateOTP(otpValue, phoneNumber.value);
    
    console.log('📡 SDK validateOTP response:', verifyResponse);

    if (verifyResponse) {
      console.log('✅ OTP verification successful');
      
      // Navigate to create password page
      console.log('🚀 Navigating to create-password page with params:', {
        phone: phoneNumber.value,
        customerName: customerName.value,
        userLoginId: userLoginId.value,
        serviceType: serviceType.value
      });

      router.push({
        path: '/register/create-password',
        query: {
          phone: phoneNumber.value,
          customerName: customerName.value,
          userLoginId: userLoginId.value,
          serviceType: serviceType.value
        }
      });
    } else {
      console.log('❌ OTP verification failed');
      errors.value.otp = "Mã OTP không chính xác. Vui lòng thử lại.";
    }
  } catch (error: any) {
    console.error('❌ OTP verification error:', error);
    errors.value.otp = error?.message || "Có lỗi xảy ra khi xác thực OTP. Vui lòng thử lại.";
  } finally {
    loading.value = false;
  }
};

// Resend OTP handler
const handleResendOtp = async () => {
  if (smsCountdown.value > 0) return;
  
  console.log('🔄 Resending OTP to:', phoneNumber.value);
  
  try {
    const { $sdk } = useNuxtApp();
    const otpResponse = await $sdk.auth.sendOTP(phoneNumber.value);
    
    console.log('📡 SendOTP response:', otpResponse);
    
    useNuxtApp().$toast.success('Mã OTP đã được gửi lại');
    
    // Get countdown from API response
    const resendCountdown = 60; // 1 minute resend cooldown (hardcode for spam prevention)
    const timeExpired = otpResponse?.timeExpired;

    console.log('📡 API Response timeExpired:', timeExpired, 'Current time:', Date.now());

    // Calculate OTP expiry time from API response
    let otpExpirySeconds = 300; // 5 minutes default
    if (timeExpired) {
      const currentTime = Date.now();
      const remainingTime = Math.max(0, Math.floor((timeExpired - currentTime) / 1000));
      otpExpirySeconds = remainingTime;
      console.log('⏰ Calculated OTP expiry from API:', remainingTime, 'seconds');
    }

    // Start SMS resend countdown
    smsCountdown.value = resendCountdown;
    const resendTimer = setInterval(() => {
      smsCountdown.value--;
      if (smsCountdown.value <= 0) {
        clearInterval(resendTimer);
      }
    }, 1000);

    // Restart OTP expiry countdown with API time
    startOtpExpiryCountdown(otpExpirySeconds);

    console.log('⏰ Resend countdown:', resendCountdown, 'seconds, OTP expiry:', otpExpirySeconds, 'seconds');
  } catch (error: any) {
    console.error('❌ Resend OTP error:', error);
    
    // Handle specific error for phone not found
    if (error?.message?.includes('không tồn tại') || error?.message?.includes('not found')) {
      errors.value.otp = 'Số điện thoại không tồn tại trong hệ thống. Vui lòng kiểm tra lại.';
    } else {
      useNuxtApp().$toast.error(error?.message || 'Không thể gửi lại OTP. Vui lòng thử lại.');
    }
  }
};

// Resend OTP via Zalo handler
const handleResendOtpZalo = async () => {
  if (zaloCountdown.value > 0) return;

  console.log('🔄 Resending OTP via Zalo to:', phoneNumber.value);

  try {
    const { $sdk } = useNuxtApp();
    // Call Zalo OTP API
    const otpResponse = await $sdk.auth.sendOTP(phoneNumber.value, 'ZALO');

    console.log('📡 SendOTP via Zalo response:', otpResponse);

    useNuxtApp().$toast.success('Mã OTP đã được gửi qua Zalo');

    // Get countdown from API response
    const resendCountdown = 60; // 1 minute resend cooldown (hardcode for spam prevention)
    const timeExpired = otpResponse?.timeExpired;

    console.log('📡 Zalo API Response timeExpired:', timeExpired, 'Current time:', Date.now());

    // Calculate OTP expiry time from API response
    let otpExpirySeconds = 300; // 5 minutes default
    if (timeExpired) {
      const currentTime = Date.now();
      const remainingTime = Math.max(0, Math.floor((timeExpired - currentTime) / 1000));
      otpExpirySeconds = remainingTime;
      console.log('⏰ Calculated Zalo OTP expiry from API:', remainingTime, 'seconds');
    }

    // Start Zalo resend countdown
    zaloCountdown.value = resendCountdown;
    const resendTimer = setInterval(() => {
      zaloCountdown.value--;
      if (zaloCountdown.value <= 0) {
        clearInterval(resendTimer);
      }
    }, 1000);

    // Restart OTP expiry countdown with API time
    startOtpExpiryCountdown(otpExpirySeconds);

    console.log('⏰ Zalo resend countdown:', resendCountdown, 'seconds, OTP expiry:', otpExpirySeconds, 'seconds');
  } catch (error: any) {
    console.error('❌ Resend OTP via Zalo error:', error);

    // Handle specific error for phone not found
    if (error?.message?.includes('không tồn tại') || error?.message?.includes('not found')) {
      errors.value.otp = 'Số điện thoại không tồn tại trong hệ thống. Vui lòng kiểm tra lại.';
    } else {
      useNuxtApp().$toast.error(error?.message || 'Không thể gửi OTP qua Zalo. Vui lòng thử lại.');
    }
  }
};

// Start OTP expiry countdown
const startOtpExpiryCountdown = (expirySeconds: number) => {
  otpExpiry.value = expirySeconds;
  
  const expiryTimer = setInterval(() => {
    otpExpiry.value--;
    if (otpExpiry.value <= 0) {
      clearInterval(expiryTimer);
      errors.value.otp = 'Mã OTP đã hết hạn. Vui lòng gửi lại mã mới.';
    }
  }, 1000);
};

// Validate required params on mount
onMounted(() => {
  if (!phoneNumber.value || !userLoginId.value) {
    console.error('❌ Missing required params for OTP verification');
    useNuxtApp().$toast.error("Thông tin không hợp lệ");
    router.push('/register');
    return;
  }
  
  console.log('✅ OTP verification page loaded with params:', {
    phone: phoneNumber.value,
    customerName: customerName.value,
    userLoginId: userLoginId.value,
    serviceType: serviceType.value
  });
  
  // Start initial OTP expiry countdown (5 minutes)
  startOtpExpiryCountdown(300);
});
</script>
