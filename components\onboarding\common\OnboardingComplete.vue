<template>
  <div class="onboarding-complete min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-2xl w-full">
      <!-- Success Animation -->
      <div class="text-center mb-8">
        <div class="relative inline-block">
          <!-- Success Circle -->
          <div class="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-bounce-in">
            <svg class="w-12 h-12 text-white animate-check-mark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          
          <!-- Confetti Animation -->
          <div class="absolute inset-0 pointer-events-none">
            <div class="confetti-piece confetti-1"></div>
            <div class="confetti-piece confetti-2"></div>
            <div class="confetti-piece confetti-3"></div>
            <div class="confetti-piece confetti-4"></div>
            <div class="confetti-piece confetti-5"></div>
            <div class="confetti-piece confetti-6"></div>
          </div>
        </div>

        <h1 class="text-4xl font-bold text-gray-900 mb-4 animate-fade-in-up">
          🎉 Chúc mừng!
        </h1>
        <p class="text-xl text-gray-600 animate-fade-in-up animation-delay-200">
          Bạn đã hoàn thành thiết lập hệ thống thành công
        </p>
      </div>

      <!-- Completion Summary -->
      <div class="bg-white rounded-2xl shadow-xl p-8 mb-8 animate-fade-in-up animation-delay-400">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Tóm tắt thiết lập
        </h2>

        <!-- Progress Summary -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900">{{ completedSteps }}</h3>
            <p class="text-sm text-gray-600">Bước hoàn thành</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V5z" clip-rule="evenodd" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900">{{ formatTime(totalTime) }}</h3>
            <p class="text-sm text-gray-600">Thời gian thiết lập</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900">{{ moduleCount }}</h3>
            <p class="text-sm text-gray-600">Module đã cài đặt</p>
          </div>
        </div>

        <!-- Completed Modules -->
        <div class="space-y-4">
          <h3 class="font-semibold text-gray-900 mb-4">Các module đã thiết lập:</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="module in completedModules"
              :key="module.id"
              class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
            >
              <div
                class="w-10 h-10 rounded-lg flex items-center justify-center"
                :class="getModuleIconClass(module.color)"
              >
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">{{ module.name }}</h4>
                <p class="text-sm text-gray-600">{{ module.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Next Steps -->
      <div class="bg-white rounded-2xl shadow-xl p-8 mb-8 animate-fade-in-up animation-delay-600">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Bước tiếp theo
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Start Using -->
          <div class="text-center p-6 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all duration-200">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Bắt đầu sử dụng</h3>
            <p class="text-sm text-gray-600 mb-4">
              Truy cập vào hệ thống và bắt đầu sử dụng các tính năng đã thiết lập
            </p>
          </div>

          <!-- Learn More -->
          <div class="text-center p-6 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all duration-200">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Tìm hiểu thêm</h3>
            <p class="text-sm text-gray-600 mb-4">
              Xem hướng dẫn sử dụng và tips để tận dụng tối đa hệ thống
            </p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="text-center space-y-4 animate-fade-in-up animation-delay-800">
        <button
          @click="goToDashboard"
          class="w-full md:w-auto px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          Vào hệ thống ngay
        </button>
        
        <div class="flex flex-col md:flex-row gap-4 justify-center">
          <button
            @click="viewGuide"
            class="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Xem hướng dẫn
          </button>
          <button
            @click="contactSupport"
            class="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Liên hệ hỗ trợ
          </button>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-center mt-12 text-gray-500 text-sm animate-fade-in-up animation-delay-1000">
        <p>Cảm ơn bạn đã tin tưởng và sử dụng hệ thống của chúng tôi! 🚀</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { OnboardingConfig } from '~/types/onboarding';
import { ONBOARDING_MODULES } from '~/config/onboarding-modules';

interface Props {
  config: OnboardingConfig;
}

const props = defineProps<Props>();
const router = useRouter();

// Computed properties
const completedSteps = computed(() => {
  return props.config.steps.filter(step => step.completed).length;
});

const totalTime = computed(() => {
  if (!props.config.completedAt || !props.config.startedAt) return 0;
  return Math.floor((props.config.completedAt.getTime() - props.config.startedAt.getTime()) / 1000);
});

const moduleCount = computed(() => {
  return props.config.modules.length;
});

const completedModules = computed(() => {
  return props.config.modules.map(moduleId => {
    const moduleConfig = ONBOARDING_MODULES[moduleId];
    return {
      id: moduleId,
      name: moduleConfig?.name || moduleId,
      description: moduleConfig?.description || '',
      color: moduleConfig?.color || 'gray'
    };
  });
});

// Methods
const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
};

const getModuleIconClass = (color: string): string => {
  return `bg-${color}-100 text-${color}-600`;
};

const goToDashboard = () => {
  // Redirect based on service type
  const serviceTypes = props.config.metadata?.serviceTypes as string[];
  if (serviceTypes?.includes('TELESALE')) {
    const portalUrl = `https://portal.dev.longvan.vn/crm/pages/resume-template/detail.xhtml?orgId=${props.config.orgId}&accessToken=${useCookie('token').value}`;
    window.location.href = portalUrl;
  } else {
    router.push('/org-store');
  }
};

const viewGuide = () => {
  // Open help documentation
  window.open('/help', '_blank');
};

const contactSupport = () => {
  // Open support contact
  window.open('mailto:<EMAIL>', '_blank');
};

// Cleanup onboarding data after completion
onMounted(() => {
  // Clean up localStorage after a delay
  setTimeout(() => {
    localStorage.removeItem(`onboarding_${props.config.userId}`);
  }, 5000);
});
</script>

<style scoped>
/* Animations */
@keyframes bounce-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes check-mark {
  0% {
    stroke-dasharray: 0 50;
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dasharray: 50 50;
    stroke-dashoffset: 0;
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

.animate-bounce-in {
  animation: bounce-in 0.8s ease-out;
}

.animate-check-mark {
  animation: check-mark 0.6s ease-out 0.4s both;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out both;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

/* Confetti */
.confetti-piece {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #f59e0b;
  animation: confetti-fall 3s linear infinite;
}

.confetti-1 {
  left: 10%;
  background: #ef4444;
  animation-delay: 0s;
}

.confetti-2 {
  left: 20%;
  background: #10b981;
  animation-delay: 0.5s;
}

.confetti-3 {
  left: 30%;
  background: #3b82f6;
  animation-delay: 1s;
}

.confetti-4 {
  left: 70%;
  background: #8b5cf6;
  animation-delay: 0.3s;
}

.confetti-5 {
  left: 80%;
  background: #f59e0b;
  animation-delay: 0.8s;
}

.confetti-6 {
  left: 90%;
  background: #ec4899;
  animation-delay: 1.2s;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}
</style>
