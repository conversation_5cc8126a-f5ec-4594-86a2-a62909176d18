// Cache for token validation to avoid repeated API calls
const tokenValidationCache = new Map<
  string,
  { isValid: boolean; timestamp: number }
>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export default defineNuxtRouteMiddleware(async (to: any) => {
  const token = useCookie("token") as Ref<string>;

  // Fast path: no token
  if (!token.value) {
    const query: Record<string, string> = {};

    if (to.path !== "/" && to.path !== "/login") {
      query.path = to.path;
    }

    if (to.query.orgId) query.orgId = String(to.query.orgId);
    if (to.query.storeId) query.storeId = String(to.query.storeId);
    if (to.query.orderId) query.orderId = String(to.query.orderId);
    if (to.query.customerId) query.customerId = String(to.query.customerId);
    return navigateTo({ path: "/login", query });
  }

  // Check cache first
  const cacheKey = token.value;
  const cached = tokenValidationCache.get(cacheKey);
  const now = Date.now();

  if (cached && now - cached.timestamp < CACHE_DURATION) {
    if (!cached.isValid) {
      return navigateTo("/login");
    }
    // Token is valid and cached, continue
    return;
  }

  // Only validate token if not cached or expired
  try {
    const { setToken, checkToken } = useAuth();
    const { orgId } = useTabContext();

    setToken(token.value);
    await checkToken(orgId.value === 'N/A' ? 'LONGVAN' : orgId.value, token.value);

    // Cache successful validation
    tokenValidationCache.set(cacheKey, { isValid: true, timestamp: now });
  } catch (error) {
    // Cache failed validation
    tokenValidationCache.set(cacheKey, { isValid: false, timestamp: now });

    const { logout } = useAuth();
    logout();
    return navigateTo("/login");
  }
});
