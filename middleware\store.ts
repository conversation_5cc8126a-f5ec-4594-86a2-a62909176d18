export default defineNuxtRouteMiddleware((to) => {
  console.log('🔍 [STORE MIDDLEWARE] Checking path:', to.path);

  // Skip middleware for test pages and simple test pages
  if (to.path.startsWith('/test-') || to.path.startsWith('/simple-') || to.path.includes('-simple')) {
    console.log('🔍 [STORE MIDDLEWARE] Skipping test pages');
    return;
  }

  // Use tab-isolated context instead of cookies
  const { orgId, storeId, isOrgValid, isStoreValid } = useTabContext();
  console.log('🔍 [STORE MIDDLEWARE] Context:', { orgId: orgId.value, storeId: storeId.value, isOrgValid: isOrgValid.value, isStoreValid: isStoreValid.value });

  // Check if user is authenticated
  const auth = useCookie("auth") as any;
  console.log('🔍 [STORE MIDDLEWARE] Auth check:', { hasAuth: !!auth.value?.user?.id });
  if (!auth.value?.user?.id) {
    // Allow access to org-store without auth for testing
    if (to.path === "/org-store") {
      console.log('🔍 [STORE MIDDLEWARE] Allowing org-store without auth');
      return;
    }
    // For other pages, redirect to login
    if (to.path !== "/login" && to.path !== "/register" && !to.path.startsWith("/auth/")) {
      console.log('🔍 [STORE MIDDLEWARE] Redirecting to login - no auth');
      return navigateTo("/login");
    }
    console.log('🔍 [STORE MIDDLEWARE] Allowing auth-related pages');
    return;
  }

  // Pages that don't need org/store context
  const allowedPages = [
    "/org-store",
    "/login",
    "/register",
    "/onboarding",
    "/profile",
    "/settings"
  ];

  // Allow access to specific pages without context check
  if (allowedPages.includes(to.path) || to.path.startsWith("/auth/")) {
    console.log('🔍 [STORE MIDDLEWARE] Allowing page without context check:', to.path);
    return;
  }

  // For root path, check if user has context first
  if (to.path === "/") {
    const hasCompleteContext = isOrgValid.value && isStoreValid.value;

    console.log('🔍 [STORE MIDDLEWARE] Root path, checking context:', {
      orgId: orgId.value,
      storeId: storeId.value,
      hasCompleteContext
    });

    if (hasCompleteContext) {
      // User has complete context, redirect to feature page
      console.log('✅ [STORE MIDDLEWARE] Complete context found, redirecting to feature page');
      return navigateTo(`/feature?orgId=${orgId.value}&storeId=${storeId.value}`);
    } else {
      // No complete context, go to org-store to select
      console.log('⚠️ [STORE MIDDLEWARE] No complete context, redirecting to org-store');
      return navigateTo("/org-store");
    }
  }

  // Check if we have both org and store context
  const hasCompleteContext = isOrgValid.value && isStoreValid.value;
  console.log('🔍 [STORE MIDDLEWARE] Context check:', { hasCompleteContext, orgValid: isOrgValid.value, storeValid: isStoreValid.value });

  if (!hasCompleteContext) {
    // If missing org or store context, redirect to org-store for selection
    console.log("🚨 [STORE MIDDLEWARE] Missing context - REDIRECTING TO ORG-STORE");
    console.log("Missing context - orgValid:", isOrgValid.value, "storeValid:", isStoreValid.value);
    return navigateTo("/org-store");
  }

  // If we have complete context and trying to access feature pages, allow with params
  if (to.path.startsWith("/feature") && hasCompleteContext) {
    // Ensure URL has the required params
    if (!to.query.orgId || !to.query.storeId) {
      console.log('🔍 [STORE MIDDLEWARE] Adding params to feature page');
      return navigateTo(`${to.path}?orgId=${orgId.value}&storeId=${storeId.value}`);
    }
  }

  console.log('🔍 [STORE MIDDLEWARE] Allowing access to:', to.path);
});
