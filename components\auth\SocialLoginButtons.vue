<template>
  <div class="space-y-4">
    <!-- Social Login <PERSON> -->
   

    <!-- Social Login <PERSON> -->
    <div class="flex gap-2">
      <!-- Google Login -->
      <button
        @click="handleGoogleLogin"
        :disabled="anyLoading"
        class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
      >
        <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
          <path
            fill="#4285F4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34A853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#FBBC05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#EA4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
        <span v-if="!loadingStates.google">Google</span>
        <span v-else class="flex items-center justify-center">
          <svg
            class="animate-spin h-4 w-4 text-gray-700"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
      </button>

      <!-- Facebook Login -->
      <button
        @click="handleFacebookLogin"
        :disabled="anyLoading"
        class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl text-sm font-medium text-white bg-[#1877F2] hover:bg-[#166FE5] focus:outline-none focus:ring-2 focus:ring-[#1877F2] focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
      >
        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
          />
        </svg>
        <span v-if="!loadingStates.facebook">Facebook</span>
        <span v-else class="flex items-center justify-center">
          <svg
            class="animate-spin h-4 w-4 text-white"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
      </button>

      <!-- Zalo Login -->
      <button
        @click="handleZaloLogin"
        :disabled="anyLoading"
        class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl text-sm font-medium text-white bg-[#0068FF] hover:bg-[#0056D6] focus:outline-none focus:ring-2 focus:ring-[#0068FF] focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
      >
        <img
          src="~/assets/images/zalo.svg"
          alt="Zalo"
          class="w-5 h-5 mr-3"
        />

        <span v-if="!loadingStates.zalo">Zalo</span>
        <span v-else class="flex items-center justify-center">
          <svg
            class="animate-spin h-4 w-4 text-white"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
      </button>
    </div>
     <div class="relative">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-300"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span class="px-2 bg-white text-gray-500">{{ headerText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  mode?: "login" | "register";
  showDebug?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  mode: "login",
  showDebug: false,
});

const {
  loginWithGoogle,
  loginWithFacebook,
  loginWithZalo,
} = useSocialLogin();

// Individual loading states for each button
const loadingStates = ref({
  google: false,
  facebook: false,
  zalo: false
});

// Computed properties
const headerText = computed(() => {
  return props.mode === "login" ? "Hoặc đăng nhập bằng" : "Hoặc đăng ký bằng";
});

// Check if any button is loading
const anyLoading = computed(() => {
  return Object.values(loadingStates.value).some(loading => loading);
});

// Event handlers
const handleGoogleLogin = async () => {
  console.log("🔵 Google login clicked");
  loadingStates.value.google = true;
  try {
    await loginWithGoogle();
  } finally {
    loadingStates.value.google = false;
  }
};

const handleFacebookLogin = async () => {
  console.log("🔵 Facebook login clicked");
  loadingStates.value.facebook = true;
  try {
    await loginWithFacebook();
  } finally {
    loadingStates.value.facebook = false;
  }
};

const handleZaloLogin = async () => {
  console.log("🔵 Zalo login clicked");
  loadingStates.value.zalo = true;
  try {
    await loginWithZalo();
  } finally {
    loadingStates.value.zalo = false;
  }
};

// Initialize on mount
onMounted(() => {

});
</script>
