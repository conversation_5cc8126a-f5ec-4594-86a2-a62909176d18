<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">Thông báo</h2>
      <p class="mt-1 text-sm text-gray-600">
        Qu<PERSON>n lý cách bạn nhận thông báo từ hệ thống
      </p>
    </div>
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Thông báo Email</h3>
      <div class="space-y-4">
        <ToggleButton
          v-model="notifications.orderCreated"
          label="Đơn hàng mới"
          description="Thông báo khi có đơn hàng mới được tạo"
          color="primary"
          @change="handleOrderCreatedChange"
        />

        <ToggleButton
          v-model="notifications.paymentSuccess"
          label="Thanh toán thành công"
          description="Thông báo khi thanh to<PERSON> đ<PERSON>ử lý"
          color="primary"
          @change="handlePaymentSuccessChange"
        />

        <ToggleButton
          v-model="notifications.weeklyReport"
          label="Báo cáo hàng tuần"
          description="Nhận báo cáo tổng kết hàng tuần"
          color="primary"
          @change="handleWeeklyReportChange"
        />

        <ToggleButton
          v-model="notifications.lowStock"
          label="Cảnh báo tồn kho"
          description="Thông báo khi sản phẩm sắp hết hàng"
          color="primary"
          @change="handleLowStockChange"
        />

        <ToggleButton
          v-model="notifications.systemMaintenance"
          label="Bảo trì hệ thống"
          description="Thông báo về lịch bảo trì hệ thống"
          color="primary"
          @change="handleSystemMaintenanceChange"
        />
      </div>
    </div>

    <!-- Push Notifications Section -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Thông báo Push</h3>
      <div class="space-y-4">
        <ToggleButton
          v-model="pushNotifications.browserNotifications"
          label="Thông báo trình duyệt"
          description="Hiển thị thông báo trên trình duyệt"
          size="md"
          color="primary"
          @change="handleBrowserNotificationsChange"
        />

        <ToggleButton
          v-model="pushNotifications.soundAlerts"
          label="Âm thanh cảnh báo"
          description="Phát âm thanh khi có thông báo mới"
          color="primary"
          size="md"
          @change="handleSoundAlertsChange"
        />

        <ToggleButton
          v-model="pushNotifications.desktopNotifications"
          label="Thông báo desktop"
          description="Hiển thị thông báo trên màn hình desktop"
          color="primary"
          size="md"
          :disabled="!pushNotifications.browserNotifications"
          @change="handleDesktopNotificationsChange"
        />
      </div>
    </div>

    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">
        Lịch sử thông báo
      </h3>
      <div class="space-y-3">
        <div class="flex items-start space-x-3 p-3 bg-white rounded border">
          <div
            class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"
          ></div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">
              Đơn hàng #12345 đã được thanh toán
            </p>
            <p class="text-xs text-gray-500">2 phút trước</p>
          </div>
        </div>

        <div class="flex items-start space-x-3 p-3 bg-white rounded border">
          <div
            class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"
          ></div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">
              Có đơn hàng mới từ khách hàng ABC
            </p>
            <p class="text-xs text-gray-500">15 phút trước</p>
          </div>
        </div>

        <div class="flex items-start space-x-3 p-3 bg-white rounded border">
          <div
            class="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"
          ></div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">Sản phẩm XYZ sắp hết hàng</p>
            <p class="text-xs text-gray-500">1 giờ trước</p>
          </div>
        </div>
      </div>

      <button
        type="button"
        class="mt-4 text-sm text-blue-600 hover:text-blue-800 font-medium"
      >
        Xem tất cả thông báo
      </button>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Notification settings reactive data
const notifications = reactive({
  orderCreated: true,
  paymentSuccess: true,
  weeklyReport: false,
  lowStock: true,
  systemMaintenance: false,
});

// Push notification settings
const pushNotifications = reactive({
  browserNotifications: true,
  soundAlerts: false,
  desktopNotifications: true,
});

// Event handlers
const handleOrderCreatedChange = (value: boolean) => {
  console.log("Order created notification changed:", value);
  // TODO: Update notification preferences for new orders
};

const handlePaymentSuccessChange = (value: boolean) => {
  console.log("Payment success notification changed:", value);
  // TODO: Update notification preferences for payment success
};

const handleWeeklyReportChange = (value: boolean) => {
  console.log("Weekly report notification changed:", value);
  // TODO: Update notification preferences for weekly reports
};

const handleLowStockChange = (value: boolean) => {
  console.log("Low stock notification changed:", value);
  // TODO: Update notification preferences for low stock alerts
};

const handleSystemMaintenanceChange = (value: boolean) => {
  console.log("System maintenance notification changed:", value);
  // TODO: Update notification preferences for system maintenance
};

// Push notification event handlers
const handleBrowserNotificationsChange = (value: boolean) => {
  console.log("Browser notifications changed:", value);
  // If browser notifications are disabled, also disable desktop notifications
  if (!value) {
    pushNotifications.desktopNotifications = false;
  }
  // TODO: Request browser notification permission if enabled
};

const handleSoundAlertsChange = (value: boolean) => {
  console.log("Sound alerts changed:", value);
  // TODO: Update sound alert preferences
};

const handleDesktopNotificationsChange = (value: boolean) => {
  console.log("Desktop notifications changed:", value);
  // TODO: Update desktop notification preferences
};
</script>
