<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">Thông báo</h2>
      <p class="mt-1 text-sm text-gray-600">
        Qu<PERSON><PERSON> lý cách bạn nhận thông báo từ hệ thống
      </p>
    </div>
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Thông báo Email</h3>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <label class="text-sm font-medium text-gray-700"
              >Đơn hàng mới</label
            >
            <p class="text-xs text-gray-500">Nhận email khi có đơn hàng mới</p>
          </div>
          <button
            type="button"
            class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-primary transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            role="switch"
            aria-checked="true"
          >
            <span
              class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
            ></span>
          </button>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <label class="text-sm font-medium text-gray-700"
              >Thanh toán thành công</label
            >
            <p class="text-xs text-gray-500">
              Thông báo khi thanh toán được xử lý
            </p>
          </div>
          <button
            type="button"
            class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-primary transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            role="switch"
            aria-checked="true"
          >
            <span
              class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
            ></span>
          </button>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <label class="text-sm font-medium text-gray-700"
              >Báo cáo hàng tuần</label
            >
            <p class="text-xs text-gray-500">Nhận báo cáo tổng kết hàng tuần</p>
          </div>
          <button
            type="button"
            class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            role="switch"
            aria-checked="false"
          >
            <span
              class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
            ></span>
          </button>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">
        Lịch sử thông báo
      </h3>
      <div class="space-y-3">
        <div class="flex items-start space-x-3 p-3 bg-white rounded border">
          <div
            class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"
          ></div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">
              Đơn hàng #12345 đã được thanh toán
            </p>
            <p class="text-xs text-gray-500">2 phút trước</p>
          </div>
        </div>

        <div class="flex items-start space-x-3 p-3 bg-white rounded border">
          <div
            class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"
          ></div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">
              Có đơn hàng mới từ khách hàng ABC
            </p>
            <p class="text-xs text-gray-500">15 phút trước</p>
          </div>
        </div>

        <div class="flex items-start space-x-3 p-3 bg-white rounded border">
          <div
            class="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"
          ></div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">Sản phẩm XYZ sắp hết hàng</p>
            <p class="text-xs text-gray-500">1 giờ trước</p>
          </div>
        </div>
      </div>

      <button
        type="button"
        class="mt-4 text-sm text-blue-600 hover:text-blue-800 font-medium"
      >
        Xem tất cả thông báo
      </button>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Component logic for notification settings
</script>
