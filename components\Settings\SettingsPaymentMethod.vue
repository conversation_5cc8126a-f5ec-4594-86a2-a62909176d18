<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">
        <PERSON><PERSON><PERSON> <PERSON>ình phương thức thanh toán
      </h2>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON><PERSON><PERSON><PERSON> lý các phư<PERSON>ng thức thanh toán và tài khoản thụ hưởng
      </p>
    </div>
    <!-- Payment Method List (Toggle Style) -->
    <div class="bg-white rounded border border-gray-200">
      <div
        class="flex items-center justify-between p-4 border-b border-gray-200"
      >
        <h3 class="text-base font-medium text-gray-900">
          Danh sách phương thức thanh toán
        </h3>
        <button
          @click="showAddPaymentModal = true"
          class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Thêm phương thức
        </button>
      </div>

      <!-- Payment Methods List -->
      <div class="divide-y divide-gray-200">
        <div
          v-for="(category, categoryIndex) in paymentTree"
          :key="categoryIndex"
          class="group"
        >
          <!-- Category Row -->
          <div class="flex items-center justify-between p-4 hover:bg-gray-50">
            <!-- Left Side: Toggle + Icon + Name -->
            <div class="flex items-center gap-3">
              <!-- Toggle Switch -->
              <ToggleSwitch
                v-model="category.isActive"
                size="md"
                @change="handleCategoryToggle(categoryIndex, $event)"
              />

              <!-- Category Icon -->
              <div
                class="w-8 h-8 rounded-lg flex items-center justify-center text-white text-xs font-bold"
                :class="category.color"
              >
                {{ category.icon }}
              </div>

              <!-- Category Name -->
              <span class="text-sm font-medium text-gray-900">
                {{ category.name }}
              </span>
            </div>

            <!-- Right Side: Default Badge + Actions -->
            <div class="flex items-center gap-3">
              <span
                v-if="category.hasDefault"
                class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded"
              >
                Mặc định
              </span>

              <!-- Expand/Collapse Button -->
              <button
                @click="toggleCategory(categoryIndex)"
                class="p-1 text-gray-400 hover:text-gray-600"
              >
                <svg
                  class="w-4 h-4 transition-transform duration-200"
                  :class="{ 'rotate-90': category.expanded }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>

              <!-- More Actions -->
              <button class="p-1 text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
                  />
                </svg>
              </button>
            </div>
          </div>

          <!-- Expanded Methods -->
          <div
            v-if="category.expanded"
            class="bg-gray-50 border-t border-gray-200"
          >
            <div
              v-for="(method, methodIndex) in category.methods"
              :key="methodIndex"
              class="flex items-center justify-between py-3 px-4 ml-14 border-l-2 border-blue-200"
            >
              <!-- Left Side: Toggle + Icon + Method Info -->
              <div class="flex items-center gap-3">
                <!-- Method Toggle -->
                <ToggleSwitch
                  v-model="method.isActive"
                  size="sm"
                  @change="
                    handleMethodToggle(categoryIndex, methodIndex, $event)
                  "
                />

                <!-- Method Icon -->
                <div
                  class="w-6 h-6 rounded flex items-center justify-center bg-white border border-gray-200"
                >
                  <svg
                    class="w-3 h-3 text-gray-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                    />
                  </svg>
                </div>

                <!-- Method Info -->
                <div>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-900">{{
                      method.name
                    }}</span>
                    <span
                      v-if="method.isDefault"
                      class="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded"
                    >
                      Mặc định
                    </span>
                  </div>
                  <p class="text-xs text-gray-500">{{ method.description }}</p>
                </div>
              </div>

              <!-- Right Side: Actions -->
              <div class="flex items-center gap-2">
                <button
                  @click="editTreeMethod(categoryIndex, methodIndex)"
                  class="p-1 text-gray-400 hover:text-gray-600"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                </button>

                <button class="p-1 text-gray-400 hover:text-gray-600">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Add Method Button -->
            <div class="px-4 pb-3 ml-14">
              <button
                @click="showAddMethodToCategory(categoryIndex)"
                class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
              >
                <svg
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Thêm phương thức
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Beneficiary Accounts -->
    <div class="bg-gray-50 rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-base font-medium text-gray-900">Tài khoản thụ hưởng</h3>
        <button
          @click="showAddBeneficiaryModal = true"
          class="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90 flex items-center gap-2"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Thêm tài khoản
        </button>
      </div>

      <div class="space-y-3">
        <div
          v-for="(account, index) in beneficiaryAccounts"
          :key="index"
          class="flex items-center justify-between p-4 bg-white rounded-lg border"
          :class="
            account.isDefault
              ? 'border-primary bg-primary/5'
              : 'border-gray-200'
          "
        >
          <div class="flex items-center space-x-4">
            <!-- Bank Logo -->
            <div
              class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center"
            >
              <span class="text-blue-600 text-xs font-bold">{{
                account.bankCode
              }}</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">
                {{ account.accountName }}
              </p>
              <p class="text-xs text-gray-500">{{ account.bankName }}</p>
              <p class="text-xs text-gray-500">
                STK: {{ maskAccountNumber(account.accountNumber) }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <span
              v-if="account.isDefault"
              class="text-xs text-primary font-medium bg-primary/10 px-2 py-1 rounded"
            >
              Mặc định
            </span>
            <span
              class="text-xs font-medium px-2 py-1 rounded"
              :class="
                account.isActive
                  ? 'text-green-700 bg-green-100'
                  : 'text-red-700 bg-red-100'
              "
            >
              {{ account.isActive ? "Hoạt động" : "Tạm dừng" }}
            </span>
            <button
              @click="editBeneficiaryAccount(account)"
              class="text-xs text-blue-600 hover:text-blue-800"
            >
              Chỉnh sửa
            </button>
            <button
              @click="deleteBeneficiaryAccount(index)"
              class="text-xs text-red-600 hover:text-red-800"
            >
              Xóa
            </button>
          </div>
        </div>

        <div v-if="beneficiaryAccounts.length === 0" class="text-center py-8">
          <div
            class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <svg
              class="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>
          <p class="text-gray-500">Chưa có tài khoản thụ hưởng nào</p>
          <button
            @click="showAddBeneficiaryModal = true"
            class="mt-2 text-primary hover:text-primary/80 text-sm font-medium"
          >
            Thêm tài khoản đầu tiên
          </button>
        </div>
      </div>
    </div>

    <!-- Add Payment Method Modal -->
    <Teleport to="body">
      <div
        v-if="showAddPaymentModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Thêm phương thức thanh toán
          </h3>

          <form @submit.prevent="addPaymentMethod" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Loại phương thức</label
              >
              <select
                v-model="newPaymentMethod.type"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                <option value="">Chọn loại</option>
                <option value="bank">Chuyển khoản ngân hàng</option>
                <option value="momo">Ví MoMo</option>
                <option value="zalopay">ZaloPay</option>
                <option value="vnpay">VNPay</option>
                <option value="cash">Tiền mặt</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Tên phương thức</label
              >
              <input
                v-model="newPaymentMethod.name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="VD: Chuyển khoản Vietcombank"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Mô tả</label
              >
              <textarea
                v-model="newPaymentMethod.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Mô tả chi tiết về phương thức thanh toán"
              ></textarea>
            </div>

            <div v-if="newPaymentMethod.type === 'bank'">
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Số tài khoản</label
              >
              <input
                v-model="newPaymentMethod.accountNumber"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Nhập số tài khoản"
              />
            </div>

            <div class="flex items-center">
              <input
                v-model="newPaymentMethod.isActive"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Kích hoạt ngay</label>
            </div>

            <div class="flex items-center">
              <input
                v-model="newPaymentMethod.isDefault"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Đặt làm mặc định</label>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showAddPaymentModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Hủy
              </button>
              <button
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90"
              >
                Thêm
              </button>
            </div>
          </form>
        </div>
      </div>
    </Teleport>

    <!-- Add Beneficiary Account Modal -->
    <Teleport to="body">
      <div
        v-if="showAddBeneficiaryModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Thêm tài khoản thụ hưởng
          </h3>

          <form @submit.prevent="addBeneficiaryAccount" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Ngân hàng</label
              >
              <select
                v-model="newBeneficiaryAccount.bankCode"
                @change="updateBankName"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                <option value="">Chọn ngân hàng</option>
                <option value="VCB">Vietcombank</option>
                <option value="TCB">Techcombank</option>
                <option value="VTB">Vietinbank</option>
                <option value="BIDV">BIDV</option>
                <option value="ACB">ACB</option>
                <option value="MB">MB Bank</option>
                <option value="TPB">TPBank</option>
                <option value="SHB">SHB</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Số tài khoản</label
              >
              <input
                v-model="newBeneficiaryAccount.accountNumber"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Nhập số tài khoản"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Tên chủ tài khoản</label
              >
              <input
                v-model="newBeneficiaryAccount.accountName"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Nhập tên chủ tài khoản"
              />
            </div>

            <div class="flex items-center">
              <input
                v-model="newBeneficiaryAccount.isActive"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Kích hoạt ngay</label>
            </div>

            <div class="flex items-center">
              <input
                v-model="newBeneficiaryAccount.isDefault"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Đặt làm mặc định</label>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showAddBeneficiaryModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Hủy
              </button>
              <button
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90"
              >
                Thêm
              </button>
            </div>
          </form>
        </div>
      </div>
    </Teleport>

    <!-- Add Category Modal -->
    <Teleport to="body">
      <div
        v-if="showAddCategoryModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Thêm danh mục mới
          </h3>

          <form @submit.prevent="addCategory" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Tên danh mục</label
              >
              <input
                v-model="newCategory.name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="VD: Ngân hàng số"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Icon</label
              >
              <input
                v-model="newCategory.icon"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="VD: DIGITAL"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Màu sắc</label
              >
              <select
                v-model="newCategory.color"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                <option value="">Chọn màu</option>
                <option value="bg-blue-600">Xanh dương</option>
                <option value="bg-green-600">Xanh lá</option>
                <option value="bg-purple-600">Tím</option>
                <option value="bg-red-600">Đỏ</option>
                <option value="bg-yellow-600">Vàng</option>
                <option value="bg-indigo-600">Indigo</option>
                <option value="bg-pink-600">Hồng</option>
                <option value="bg-gray-600">Xám</option>
              </select>
            </div>

            <div class="flex items-center">
              <input
                v-model="newCategory.isActive"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Kích hoạt ngay</label>
            </div>

            <div class="flex items-center">
              <input
                v-model="newCategory.expanded"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Mở rộng mặc định</label>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showAddCategoryModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Hủy
              </button>
              <button
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90"
              >
                Thêm
              </button>
            </div>
          </form>
        </div>
      </div>
    </Teleport>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Interfaces
interface PaymentMethod {
  type: string;
  name: string;
  description: string;
  accountNumber?: string;
  isActive: boolean;
  isDefault: boolean;
}

interface BeneficiaryAccount {
  bankCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  isActive: boolean;
  isDefault: boolean;
}

interface TreeMethod {
  name: string;
  description: string;
  isActive: boolean;
  isDefault: boolean;
  details?: Record<string, string>;
}

interface PaymentCategory {
  name: string;
  icon: string;
  color: string;
  isActive: boolean;
  expanded: boolean;
  methods: TreeMethod[];
  hasDefault?: boolean;
}

// Reactive data
const showAddPaymentModal = ref(false);
const showAddBeneficiaryModal = ref(false);
const showAddCategoryModal = ref(false);

// Sample data
const paymentMethods = ref<PaymentMethod[]>([
  {
    type: "bank",
    name: "Chuyển khoản Vietcombank",
    description: "Chuyển khoản qua ngân hàng Vietinbank",
    accountNumber: "************",
    isActive: true,
    isDefault: true,
  },
  {
    type: "momo",
    name: "Ví MoMo",
    description: "Thanh toán qua ví điện tử MoMo",
    isActive: true,
    isDefault: false,
  },
]);

const beneficiaryAccounts = ref<BeneficiaryAccount[]>([
  {
    bankCode: "CTG",
    bankName: "Viettinbank",
    accountNumber: "************",
    accountName: "DO NGOC DUY HUNG",
    isActive: true,
    isDefault: true,
  },
]);

// Payment Tree Structure
const paymentTree = ref<PaymentCategory[]>([
  {
    name: "Tiền mặt",
    icon: "💵",
    color: "bg-green-600",
    isActive: true,
    expanded: false,
    hasDefault: false,
    methods: [],
  },
  {
    name: "Chuyển khoản",
    icon: "🏦",
    color: "bg-blue-600",
    isActive: true,
    expanded: true,
    hasDefault: true,
    methods: [
      {
        name: "Do Ngoc Duy Hung",
        description: "VietinBank - Ngân hàng TMCP Công thương Việt Nam",
        isActive: true,
        isDefault: true,
      },
    ],
  },
  {
    name: "Thanh toán thẻ",
    icon: "💳",
    color: "bg-purple-600",
    isActive: true,
    expanded: false,
    hasDefault: false,
    methods: [],
  },
]);

// Form data
const newPaymentMethod = ref<PaymentMethod>({
  type: "",
  name: "",
  description: "",
  accountNumber: "",
  isActive: true,
  isDefault: false,
});

const newBeneficiaryAccount = ref<BeneficiaryAccount>({
  bankCode: "",
  bankName: "",
  accountNumber: "",
  accountName: "",
  isActive: true,
  isDefault: false,
});

const newCategory = ref<PaymentCategory>({
  name: "",
  icon: "",
  color: "",
  isActive: true,
  expanded: true,
  methods: [],
});

// Bank mapping
const bankNames: Record<string, string> = {
  VCB: "Vietcombank",
  TCB: "Techcombank",
  VTB: "Vietinbank",
  BIDV: "BIDV",
  ACB: "ACB",
  MB: "MB Bank",
  TPB: "TPBank",
  SHB: "SHB",
};

// Methods
const getPaymentMethodColor = (type: string): string => {
  const colors: Record<string, string> = {
    bank: "bg-blue-600",
    momo: "bg-pink-600",
    zalopay: "bg-blue-500",
    vnpay: "bg-red-600",
    cash: "bg-green-600",
  };
  return colors[type] || "bg-gray-600";
};

const getPaymentMethodLabel = (type: string): string => {
  const labels: Record<string, string> = {
    bank: "BANK",
    momo: "MOMO",
    zalopay: "ZALO",
    vnpay: "VNPAY",
    cash: "CASH",
  };
  return labels[type] || "OTHER";
};

const maskAccountNumber = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length < 4) return accountNumber;
  const visiblePart = accountNumber.slice(-4);
  const maskedPart = "*".repeat(accountNumber.length - 4);
  return maskedPart + visiblePart;
};

const updateBankName = () => {
  newBeneficiaryAccount.value.bankName =
    bankNames[newBeneficiaryAccount.value.bankCode] || "";
};

const addPaymentMethod = () => {
  if (newPaymentMethod.value.isDefault) {
    paymentMethods.value.forEach((method) => (method.isDefault = false));
  }

  paymentMethods.value.push({ ...newPaymentMethod.value });

  // Reset form
  newPaymentMethod.value = {
    type: "",
    name: "",
    description: "",
    accountNumber: "",
    isActive: true,
    isDefault: false,
  };

  showAddPaymentModal.value = false;
};

const addBeneficiaryAccount = () => {
  if (newBeneficiaryAccount.value.isDefault) {
    beneficiaryAccounts.value.forEach((account) => (account.isDefault = false));
  }

  beneficiaryAccounts.value.push({ ...newBeneficiaryAccount.value });

  // Reset form
  newBeneficiaryAccount.value = {
    bankCode: "",
    bankName: "",
    accountNumber: "",
    accountName: "",
    isActive: true,
    isDefault: false,
  };

  showAddBeneficiaryModal.value = false;
};

const editPaymentMethod = (method: PaymentMethod) => {
  // TODO: Implement edit functionality
  console.log("Edit payment method:", method);
};

const deletePaymentMethod = (index: number) => {
  if (confirm("Bạn có chắc chắn muốn xóa phương thức thanh toán này?")) {
    paymentMethods.value.splice(index, 1);
  }
};

const editBeneficiaryAccount = (account: BeneficiaryAccount) => {
  // TODO: Implement edit functionality
  console.log("Edit beneficiary account:", account);
};

const deleteBeneficiaryAccount = (index: number) => {
  if (confirm("Bạn có chắc chắn muốn xóa tài khoản thụ hưởng này?")) {
    beneficiaryAccounts.value.splice(index, 1);
  }
};

// Tree Methods
const expandAllNodes = () => {
  paymentTree.value.forEach((category) => {
    category.expanded = true;
  });
};

const collapseAllNodes = () => {
  paymentTree.value.forEach((category) => {
    category.expanded = false;
  });
};

const toggleCategory = (categoryIndex: number) => {
  paymentTree.value[categoryIndex].expanded =
    !paymentTree.value[categoryIndex].expanded;
};

const toggleCategoryStatus = (categoryIndex: number) => {
  paymentTree.value[categoryIndex].isActive =
    !paymentTree.value[categoryIndex].isActive;
};

const toggleMethodStatus = (categoryIndex: number, methodIndex: number) => {
  paymentTree.value[categoryIndex].methods[methodIndex].isActive =
    !paymentTree.value[categoryIndex].methods[methodIndex].isActive;
};

// New toggle handlers for ToggleSwitch component
const handleCategoryToggle = (categoryIndex: number, value: boolean) => {
  paymentTree.value[categoryIndex].isActive = value;
  console.log(
    `Category ${paymentTree.value[categoryIndex].name} toggled to:`,
    value
  );
};

const handleMethodToggle = (
  categoryIndex: number,
  methodIndex: number,
  value: boolean
) => {
  paymentTree.value[categoryIndex].methods[methodIndex].isActive = value;
  const method = paymentTree.value[categoryIndex].methods[methodIndex];
  console.log(`Method ${method.name} toggled to:`, value);
};

const editTreeMethod = (categoryIndex: number, methodIndex: number) => {
  const method = paymentTree.value[categoryIndex].methods[methodIndex];
  console.log("Edit tree method:", method);
  // TODO: Implement edit functionality
};

const deleteTreeMethod = (categoryIndex: number, methodIndex: number) => {
  if (confirm("Bạn có chắc chắn muốn xóa phương thức này?")) {
    paymentTree.value[categoryIndex].methods.splice(methodIndex, 1);
  }
};

const showAddMethodToCategory = (categoryIndex: number) => {
  console.log("Add method to category:", paymentTree.value[categoryIndex].name);
  // TODO: Implement add method to category functionality
};

const addCategory = () => {
  paymentTree.value.push({ ...newCategory.value });

  // Reset form
  newCategory.value = {
    name: "",
    icon: "",
    color: "",
    isActive: true,
    expanded: true,
    methods: [],
  };

  showAddCategoryModal.value = false;
};
</script>
