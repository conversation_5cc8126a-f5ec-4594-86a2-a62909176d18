<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900"><PERSON><PERSON><PERSON> <PERSON>ì<PERSON> ph<PERSON><PERSON>ng thức thanh toán</h2>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON><PERSON><PERSON><PERSON> lý các ph<PERSON><PERSON> thức thanh toán và tài khoản thụ hưởng
      </p>
    </div>

    <!-- Payment Methods List -->
    <div class="bg-gray-50 rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-base font-medium text-gray-900"><PERSON><PERSON><PERSON><PERSON> thức thanh toán</h3>
        <button
          @click="showAddPaymentModal = true"
          class="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90 flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Thêm phư<PERSON>ng thức
        </button>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="(method, index) in paymentMethods" 
          :key="index"
          class="flex items-center justify-between p-4 bg-white rounded-lg border"
          :class="method.isDefault ? 'border-primary bg-primary/5' : 'border-gray-200'"
        >
          <div class="flex items-center space-x-4">
            <!-- Payment Method Icon -->
            <div class="w-12 h-8 rounded flex items-center justify-center text-white text-xs font-bold"
                 :class="getPaymentMethodColor(method.type)">
              {{ getPaymentMethodLabel(method.type) }}
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ method.name }}</p>
              <p class="text-xs text-gray-500">{{ method.description }}</p>
              <p v-if="method.accountNumber" class="text-xs text-gray-500">
                STK: {{ maskAccountNumber(method.accountNumber) }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <span v-if="method.isDefault" class="text-xs text-primary font-medium bg-primary/10 px-2 py-1 rounded">
              Mặc định
            </span>
            <span class="text-xs font-medium px-2 py-1 rounded"
                  :class="method.isActive ? 'text-green-700 bg-green-100' : 'text-red-700 bg-red-100'">
              {{ method.isActive ? 'Hoạt động' : 'Tạm dừng' }}
            </span>
            <button @click="editPaymentMethod(method)" class="text-xs text-blue-600 hover:text-blue-800">
              Chỉnh sửa
            </button>
            <button @click="deletePaymentMethod(index)" class="text-xs text-red-600 hover:text-red-800">
              Xóa
            </button>
          </div>
        </div>
        
        <div v-if="paymentMethods.length === 0" class="text-center py-8">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
          <p class="text-gray-500">Chưa có phương thức thanh toán nào</p>
          <button
            @click="showAddPaymentModal = true"
            class="mt-2 text-primary hover:text-primary/80 text-sm font-medium"
          >
            Thêm phương thức đầu tiên
          </button>
        </div>
      </div>
    </div>

    <!-- Beneficiary Accounts -->
    <div class="bg-gray-50 rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-base font-medium text-gray-900">Tài khoản thụ hưởng</h3>
        <button
          @click="showAddBeneficiaryModal = true"
          class="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90 flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Thêm tài khoản
        </button>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="(account, index) in beneficiaryAccounts" 
          :key="index"
          class="flex items-center justify-between p-4 bg-white rounded-lg border"
          :class="account.isDefault ? 'border-primary bg-primary/5' : 'border-gray-200'"
        >
          <div class="flex items-center space-x-4">
            <!-- Bank Logo -->
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <span class="text-blue-600 text-xs font-bold">{{ account.bankCode }}</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ account.accountName }}</p>
              <p class="text-xs text-gray-500">{{ account.bankName }}</p>
              <p class="text-xs text-gray-500">STK: {{ maskAccountNumber(account.accountNumber) }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <span v-if="account.isDefault" class="text-xs text-primary font-medium bg-primary/10 px-2 py-1 rounded">
              Mặc định
            </span>
            <span class="text-xs font-medium px-2 py-1 rounded"
                  :class="account.isActive ? 'text-green-700 bg-green-100' : 'text-red-700 bg-red-100'">
              {{ account.isActive ? 'Hoạt động' : 'Tạm dừng' }}
            </span>
            <button @click="editBeneficiaryAccount(account)" class="text-xs text-blue-600 hover:text-blue-800">
              Chỉnh sửa
            </button>
            <button @click="deleteBeneficiaryAccount(index)" class="text-xs text-red-600 hover:text-red-800">
              Xóa
            </button>
          </div>
        </div>
        
        <div v-if="beneficiaryAccounts.length === 0" class="text-center py-8">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <p class="text-gray-500">Chưa có tài khoản thụ hưởng nào</p>
          <button
            @click="showAddBeneficiaryModal = true"
            class="mt-2 text-primary hover:text-primary/80 text-sm font-medium"
          >
            Thêm tài khoản đầu tiên
          </button>
        </div>
      </div>
    </div>

    <!-- Add Payment Method Modal -->
    <Teleport to="body">
      <div v-if="showAddPaymentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Thêm phương thức thanh toán</h3>
          
          <form @submit.prevent="addPaymentMethod" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Loại phương thức</label>
              <select v-model="newPaymentMethod.type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                <option value="">Chọn loại</option>
                <option value="bank">Chuyển khoản ngân hàng</option>
                <option value="momo">Ví MoMo</option>
                <option value="zalopay">ZaloPay</option>
                <option value="vnpay">VNPay</option>
                <option value="cash">Tiền mặt</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Tên phương thức</label>
              <input v-model="newPaymentMethod.name" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary" placeholder="VD: Chuyển khoản Vietcombank">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả</label>
              <textarea v-model="newPaymentMethod.description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary" placeholder="Mô tả chi tiết về phương thức thanh toán"></textarea>
            </div>
            
            <div v-if="newPaymentMethod.type === 'bank'">
              <label class="block text-sm font-medium text-gray-700 mb-2">Số tài khoản</label>
              <input v-model="newPaymentMethod.accountNumber" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary" placeholder="Nhập số tài khoản">
            </div>
            
            <div class="flex items-center">
              <input v-model="newPaymentMethod.isActive" type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
              <label class="ml-2 text-sm text-gray-700">Kích hoạt ngay</label>
            </div>
            
            <div class="flex items-center">
              <input v-model="newPaymentMethod.isDefault" type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
              <label class="ml-2 text-sm text-gray-700">Đặt làm mặc định</label>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
              <button type="button" @click="showAddPaymentModal = false" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Hủy
              </button>
              <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90">
                Thêm
              </button>
            </div>
          </form>
        </div>
      </div>
    </Teleport>

    <!-- Add Beneficiary Account Modal -->
    <Teleport to="body">
      <div v-if="showAddBeneficiaryModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Thêm tài khoản thụ hưởng</h3>
          
          <form @submit.prevent="addBeneficiaryAccount" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Ngân hàng</label>
              <select v-model="newBeneficiaryAccount.bankCode" @change="updateBankName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                <option value="">Chọn ngân hàng</option>
                <option value="VCB">Vietcombank</option>
                <option value="TCB">Techcombank</option>
                <option value="VTB">Vietinbank</option>
                <option value="BIDV">BIDV</option>
                <option value="ACB">ACB</option>
                <option value="MB">MB Bank</option>
                <option value="TPB">TPBank</option>
                <option value="SHB">SHB</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Số tài khoản</label>
              <input v-model="newBeneficiaryAccount.accountNumber" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary" placeholder="Nhập số tài khoản">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Tên chủ tài khoản</label>
              <input v-model="newBeneficiaryAccount.accountName" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary" placeholder="Nhập tên chủ tài khoản">
            </div>
            
            <div class="flex items-center">
              <input v-model="newBeneficiaryAccount.isActive" type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
              <label class="ml-2 text-sm text-gray-700">Kích hoạt ngay</label>
            </div>
            
            <div class="flex items-center">
              <input v-model="newBeneficiaryAccount.isDefault" type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
              <label class="ml-2 text-sm text-gray-700">Đặt làm mặc định</label>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
              <button type="button" @click="showAddBeneficiaryModal = false" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Hủy
              </button>
              <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90">
                Thêm
              </button>
            </div>
          </form>
        </div>
      </div>
    </Teleport>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Interfaces
interface PaymentMethod {
  type: string;
  name: string;
  description: string;
  accountNumber?: string;
  isActive: boolean;
  isDefault: boolean;
}

interface BeneficiaryAccount {
  bankCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  isActive: boolean;
  isDefault: boolean;
}

// Reactive data
const showAddPaymentModal = ref(false);
const showAddBeneficiaryModal = ref(false);

// Sample data
const paymentMethods = ref<PaymentMethod[]>([
  {
    type: 'bank',
    name: 'Chuyển khoản Vietcombank',
    description: 'Chuyển khoản qua ngân hàng Vietcombank',
    accountNumber: '**********',
    isActive: true,
    isDefault: true
  },
  {
    type: 'momo',
    name: 'Ví MoMo',
    description: 'Thanh toán qua ví điện tử MoMo',
    isActive: true,
    isDefault: false
  }
]);

const beneficiaryAccounts = ref<BeneficiaryAccount[]>([
  {
    bankCode: 'VCB',
    bankName: 'Vietcombank',
    accountNumber: '**********',
    accountName: 'NGUYEN VAN A',
    isActive: true,
    isDefault: true
  }
]);

// Form data
const newPaymentMethod = ref<PaymentMethod>({
  type: '',
  name: '',
  description: '',
  accountNumber: '',
  isActive: true,
  isDefault: false
});

const newBeneficiaryAccount = ref<BeneficiaryAccount>({
  bankCode: '',
  bankName: '',
  accountNumber: '',
  accountName: '',
  isActive: true,
  isDefault: false
});

// Bank mapping
const bankNames: Record<string, string> = {
  'VCB': 'Vietcombank',
  'TCB': 'Techcombank',
  'VTB': 'Vietinbank',
  'BIDV': 'BIDV',
  'ACB': 'ACB',
  'MB': 'MB Bank',
  'TPB': 'TPBank',
  'SHB': 'SHB'
};

// Methods
const getPaymentMethodColor = (type: string): string => {
  const colors: Record<string, string> = {
    bank: 'bg-blue-600',
    momo: 'bg-pink-600',
    zalopay: 'bg-blue-500',
    vnpay: 'bg-red-600',
    cash: 'bg-green-600'
  };
  return colors[type] || 'bg-gray-600';
};

const getPaymentMethodLabel = (type: string): string => {
  const labels: Record<string, string> = {
    bank: 'BANK',
    momo: 'MOMO',
    zalopay: 'ZALO',
    vnpay: 'VNPAY',
    cash: 'CASH'
  };
  return labels[type] || 'OTHER';
};

const maskAccountNumber = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length < 4) return accountNumber;
  const visiblePart = accountNumber.slice(-4);
  const maskedPart = '*'.repeat(accountNumber.length - 4);
  return maskedPart + visiblePart;
};

const updateBankName = () => {
  newBeneficiaryAccount.value.bankName = bankNames[newBeneficiaryAccount.value.bankCode] || '';
};

const addPaymentMethod = () => {
  if (newPaymentMethod.value.isDefault) {
    paymentMethods.value.forEach(method => method.isDefault = false);
  }
  
  paymentMethods.value.push({ ...newPaymentMethod.value });
  
  // Reset form
  newPaymentMethod.value = {
    type: '',
    name: '',
    description: '',
    accountNumber: '',
    isActive: true,
    isDefault: false
  };
  
  showAddPaymentModal.value = false;
};

const addBeneficiaryAccount = () => {
  if (newBeneficiaryAccount.value.isDefault) {
    beneficiaryAccounts.value.forEach(account => account.isDefault = false);
  }
  
  beneficiaryAccounts.value.push({ ...newBeneficiaryAccount.value });
  
  // Reset form
  newBeneficiaryAccount.value = {
    bankCode: '',
    bankName: '',
    accountNumber: '',
    accountName: '',
    isActive: true,
    isDefault: false
  };
  
  showAddBeneficiaryModal.value = false;
};

const editPaymentMethod = (method: PaymentMethod) => {
  // TODO: Implement edit functionality
  console.log('Edit payment method:', method);
};

const deletePaymentMethod = (index: number) => {
  if (confirm('Bạn có chắc chắn muốn xóa phương thức thanh toán này?')) {
    paymentMethods.value.splice(index, 1);
  }
};

const editBeneficiaryAccount = (account: BeneficiaryAccount) => {
  // TODO: Implement edit functionality
  console.log('Edit beneficiary account:', account);
};

const deleteBeneficiaryAccount = (index: number) => {
  if (confirm('Bạn có chắc chắn muốn xóa tài khoản thụ hưởng này?')) {
    beneficiaryAccounts.value.splice(index, 1);
  }
};
</script>
