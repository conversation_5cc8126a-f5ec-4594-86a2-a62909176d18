<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -inset-10 opacity-30">
        <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="relative w-full max-w-md mx-auto">
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <div class="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-2">Liên Kết Tài Khoản</h2>
          <p class="text-gray-600">
            Nhập mã OTP để liên kết tài khoản Google với số điện thoại
            <span class="font-semibold text-primary">{{ phoneNumber }}</span>
          </p>
          
          <!-- Account Info -->
          <div class="mt-4 space-y-2">
            <div class="px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
              <p class="text-sm text-green-700">
                <span class="font-semibold">Google:</span> {{ customerName }}
              </p>
            </div>
            <div class="px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
              <p class="text-sm text-blue-700">
                <span class="font-semibold">Số điện thoại:</span> {{ phoneNumber }}
              </p>
            </div>
          </div>
        </div>

        <!-- OTP Form -->
        <form @submit.prevent="handleVerifyOTP" class="space-y-6">
          <!-- OTP Input -->
          <div class="space-y-2">
            <label for="otp" class="block text-sm font-semibold text-gray-700">
              Mã xác thực OTP <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <input
                id="otp"
                v-model="otp"
                type="text"
                name="otp"
                maxlength="6"
                autocomplete="one-time-code"
                @blur="validateFieldOnBlur('otp')"
                :class="[
                  'block w-full pl-10 pr-3 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 text-center text-lg tracking-widest',
                  errors.otp
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 bg-white hover:border-gray-400',
                ]"
                placeholder="000000"
              />
            </div>
            <Transition name="slide-down">
              <span
                v-if="errors.otp"
                class="text-sm text-red-600 flex items-center space-x-1"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>{{ errors.otp }}</span>
              </span>
            </Transition>
          </div>

          <!-- Resend OTP -->
          <div class="text-center">
            <p class="text-sm text-gray-600">
              Không nhận được mã?
              <button
                type="button"
                @click="resendOTP"
                :disabled="resendLoading || countdown > 0"
                class="text-primary hover:text-primary/80 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ countdown > 0 ? `Gửi lại sau ${countdown}s` : 'Gửi lại mã' }}
              </button>
            </p>
          </div>

          <!-- Verify Button -->
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg
                class="h-5 w-5 text-primary-light group-hover:text-white transition-colors duration-200"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </span>
            {{ loading ? "Đang xác thực..." : "Liên kết tài khoản" }}
          </button>

          <!-- Cancel Button -->
          <button
            type="button"
            @click="handleCancel"
            :disabled="loading"
            class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-xl text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            Hủy và quay lại
          </button>
        </form>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div v-if="loading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Liên kết tài khoản",
  meta: [
    { name: "description", content: "Xác thực OTP để liên kết tài khoản Google" }
  ]
});

const route = useRoute();
const router = useRouter();

// Extract info from query params
const phoneNumber = ref((route.query.phone as string) || "");
const customerName = ref((route.query.customerName as string) || "");
const accessToken = ref((route.query.accessToken as string) || "");
const registrationType = ref((route.query.type as string) || "NORMAL");

// Form state
const otp = ref("");
const loading = ref(false);
const resendLoading = ref(false);
const countdown = ref(0);

// Error states
const errors = ref({
  otp: "",
});

// Validation
const validateOTP = (otpValue: string): string => {
  if (!otpValue || !otpValue.trim()) {
    return "Mã OTP không được để trống";
  }
  
  if (!/^\d{6}$/.test(otpValue.trim())) {
    return "Mã OTP phải là 6 chữ số";
  }
  
  return "";
};

const validateFieldOnBlur = (fieldName: string) => {
  switch (fieldName) {
    case 'otp':
      errors.value.otp = validateOTP(otp.value);
      break;
  }
};

// Form validation
const isFormValid = computed(() => {
  return otp.value.trim() && !validateOTP(otp.value);
});

// Handle OTP verification for account linking
const handleVerifyOTP = async () => {
  // Validate
  errors.value.otp = validateOTP(otp.value);
  
  if (errors.value.otp) {
    return;
  }
  
  loading.value = true;
  
  try {
    const $sdk = useNuxtApp().$sdk;
    
    console.log('🔗 Verifying OTP for account linking...');
    
    // Verify OTP and link accounts
    const linkResult = await $sdk.auth.linkSocialAccount({
      phone: phoneNumber.value,
      otp: otp.value,
      socialToken: accessToken.value,
      socialProvider: 'google'
    });
    
    console.log('✅ Account linking successful:', linkResult);
    
    useNuxtApp().$toast.success("Tài khoản đã được liên kết thành công!");

    // Redirect to org-store selection
    router.push('/org-store');
    
  } catch (error: any) {
    console.error('❌ Account linking error:', error);
    
    if (error?.message?.includes('OTP')) {
      errors.value.otp = "Mã OTP không chính xác";
    } else {
      useNuxtApp().$toast.error(error?.message || "Có lỗi xảy ra khi liên kết tài khoản");
    }
  } finally {
    loading.value = false;
  }
};

// Resend OTP
const resendOTP = async () => {
  resendLoading.value = true;
  
  try {
    const $sdk = useNuxtApp().$sdk;
    
    await $sdk.auth.sendOTP(phoneNumber.value);
    useNuxtApp().$toast.success("Mã OTP mới đã được gửi");
    
    // Start countdown
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
    
  } catch (error: any) {
    console.error('❌ Resend OTP error:', error);
    useNuxtApp().$toast.error(error?.message || "Có lỗi xảy ra khi gửi lại mã OTP");
  } finally {
    resendLoading.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  router.push({
    path: '/register/update-info',
    query: {
      accessToken: accessToken.value,
      type: registrationType.value
    }
  });
};

// Validate required params on mount
onMounted(() => {
  if (!phoneNumber.value || !accessToken.value) {
    console.error('❌ Missing required params for account linking');
    useNuxtApp().$toast.error("Thông tin không hợp lệ");
    router.push('/register');
  }
});

// Watch OTP for real-time validation
watch(otp, (newValue) => {
  if (errors.value.otp && newValue.trim()) {
    errors.value.otp = validateOTP(newValue);
  }
});
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
