<template>
  <div class="space-y-2 bg-white rounded">
    <div class="flex items-center gap-2 mb-2">
      <div class="w-1/2">
        <label class="text-sm">Đ<PERSON>a chỉ giao hàng</label>
        <input
          type="text"
          placeholder="Số nhà, tên đường"
          class="w-full p-1 rounded outline-none border bg-secondary"
          v-model="address"
          @change="handleSaveShippingAddress"
        />
      </div>
      <div class="w-1/2">
        <label class="text-sm">Số điện thoại</label>
        <input
          type="text"
          placeholder="Số điện thoại"
          class="w-full p-1 rounded outline-none border bg-secondary"
          v-model="phone"
          @change="handleSaveShippingAddress"
        />
      </div>
    </div>
    <div class="flex items-center gap-2">
      <div class="w-1/2">
        <SearchableSelect
          v-model="selectedCity"
          :options="provinces"
          label="Tỉnh/Thành phố"
          placeholder="Chọn thành phố/tỉnh"
          :search-fields="['geoName']"
          value-key="geoId"
          label-key="geoName"
          max-dropdown-height="200px"
          placement="top"
          input-class="w-full p-1 rounded bg-secondary outline-none border text-sm"
          @select="handleProvinceSelect"
          @clear="handleProvinceClear"
        />
      </div>

      <div class="w-1/2">
        <SearchableSelect
          v-model="selectedWard"
          :options="wards"
          label="Phường/xã"
          placeholder="Chọn phường/xã"
          :disabled="!selectedCity"
          :search-fields="['geoName']"
          value-key="geoId"
          label-key="geoName"
          max-dropdown-height="200px"
          input-class="w-full p-1 rounded bg-secondary outline-none border text-sm"
          @select="handleWardSelect"
          @clear="handleWardClear"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchableSelect from "~/components/ui/forms/SearchableSelect.vue";

const provinces = ref<any>([]);
const wards = ref<any>([]);

const selectedCity = ref("");
const selectedWard = ref("");

const address = ref("");
const phone = ref("");

const isInitializing = ref(true); // Ngăn cập nhật khi trang load lần đầu

const props = defineProps(["order", "dataFFM", "shippingAddress", "customer"]);
const emit = defineEmits(["fetchFFMStatus"]);

const { $sdk } = useNuxtApp();
const auth = useCookie("auth") as any;
const orderStore = useOrderStore();
const customerStore = useCustomerStore();
const { updateOrderCustomer } = useOrder();
const { getProvinces, getWardsByProvinceId } = useUser();
const getProvincesV2 = async () => {
  try {
    provinces.value = await getProvinces();
  } catch (error) {
    throw error;
  }
};

const fetchWards = async () => {
  if (!selectedCity.value) return;
  try {
    wards.value = await getWardsByProvinceId(selectedCity.value);
  } catch (error) {
    console.error("Error fetching wards:", error);
    useNuxtApp().$toast?.error("Không thể tải danh sách phường/xã");
  }
};

// New event handlers for SearchableSelect components
const handleProvinceSelect = async (province: any) => {
  selectedCity.value = province.geoId;
  await fetchWards();
  // Auto-save after province selection
  if (!isInitializing.value) {
    await handleSaveShippingAddress();
  }
};

const handleProvinceClear = () => {
  selectedCity.value = "";
  selectedWard.value = "";
  wards.value = [];
};

const handleWardSelect = async (ward: any) => {
  selectedWard.value = ward.geoId;
  // Auto-save after ward selection
  if (!isInitializing.value) {
    await handleSaveShippingAddress();
  }
};

const handleWardClear = () => {
  selectedWard.value = "";
};

onMounted(async () => {
  isInitializing.value = true;

  phone.value = props.order.order.shippingAddress.phone;
  address.value = props.order.order.shippingAddress.address1;

  await getProvincesV2();
  selectedCity.value = props.order.order.shippingAddress?.provinceCode;
  await fetchWards();
  selectedWard.value = props.order.order.shippingAddress?.wardCode;

  isInitializing.value = false;
});

const handleSaveShippingAddress = async () => {
  const res = $sdk.order.validatePhoneNumber(phone.value);
  if (!res) {
    useNuxtApp().$toast.warning("Số điện thoại không hợp lệ");
    return;
  }

  const data = {
    name: props.order.order.shippingAddress.name,
    phone: phone.value,
    address: address.value,
    province_code: selectedCity.value,
    ward_code: selectedWard.value,
    address_default: true,
  };

  const response = await orderStore.updateShippingAddress(
    props.customer.id,
    props.shippingAddress.id,
    auth.value.user.id,
    data
  );

  await customerStore.handleUpdateShippingAddress(
    response?.data,
    props.customer.id
  );

  await updateOrderCustomer(
    orderStore.orderDetail?.id,
    props.customer.id,
    props.shippingAddress.id
  );

  await orderStore.updateOrder(orderStore.orderDetail?.id);
};

// Note: Auto-save logic is now handled in SearchableSelect event handlers
</script>
