/**
 * Extract meaningful error message from GraphQL error response
 */
export const extractGraphQLError = (error: any): string => {
  console.log('🔍 Extracting error from:', error);
  
  // Case 1: Array of GraphQL errors (like the user's example)
  if (Array.isArray(error) && error.length > 0) {
    const firstError = error[0];
    if (firstError.message) {
      return extractMeaningfulMessage(firstError.message);
    }
  }
  
  // Case 2: Error object with graphQLErrors array
  if (error?.graphQLErrors && Array.isArray(error.graphQLErrors) && error.graphQLErrors.length > 0) {
    const firstError = error.graphQLErrors[0];
    if (firstError.message) {
      return extractMeaningfulMessage(firstError.message);
    }
  }
  
  // Case 3: Error object with response.errors
  if (error?.response?.errors && Array.isArray(error.response.errors) && error.response.errors.length > 0) {
    const firstError = error.response.errors[0];
    if (firstError.message) {
      return extractMeaningfulMessage(firstError.message);
    }
  }
  
  // Case 4: Direct error message
  if (error?.message) {
    return extractMeaningfulMessage(error.message);
  }
  
  // Case 5: String error
  if (typeof error === 'string') {
    return extractMeaningfulMessage(error);
  }
  
  // Fallback
  return 'Có lỗi xảy ra. Vui lòng thử lại.';
};

/**
 * Extract meaningful message from exception string
 * Input: "Exception while fetching data (/createOrg) : vn.longvan.core.exeption.ControllerException: Tên tổ chức đã tồn tại."
 * Output: "Tên tổ chức đã tồn tại"
 */
const extractMeaningfulMessage = (message: string): string => {
  if (!message) return 'Có lỗi xảy ra. Vui lòng thử lại.';
  
  console.log('🔍 Extracting from message:', message);
  
  // Pattern 1: Extract after "ControllerException: "
  const controllerExceptionMatch = message.match(/ControllerException:\s*(.+?)\.?$/);
  if (controllerExceptionMatch && controllerExceptionMatch[1]) {
    const extracted = controllerExceptionMatch[1].trim();
    console.log('✅ Extracted via ControllerException pattern:', extracted);
    return extracted;
  }
  
  // Pattern 2: Extract after last ": "
  const lastColonMatch = message.match(/:\s*([^:]+?)\.?$/);
  if (lastColonMatch && lastColonMatch[1]) {
    const extracted = lastColonMatch[1].trim();
    // Skip if it's just technical info
    if (!extracted.includes('Exception') && !extracted.includes('fetching data')) {
      console.log('✅ Extracted via last colon pattern:', extracted);
      return extracted;
    }
  }
  
  // Pattern 3: Common Vietnamese error messages
  const vietnamesePatterns = [
    /đã tồn tại/i,
    /không tồn tại/i,
    /không hợp lệ/i,
    /không được phép/i,
    /đã được sử dụng/i,
    /không đủ quyền/i,
    /hết hạn/i,
    /không tìm thấy/i
  ];
  
  for (const pattern of vietnamesePatterns) {
    const match = message.match(new RegExp(`([^.]*${pattern.source}[^.]*)`, 'i'));
    if (match && match[1]) {
      const extracted = match[1].trim();
      console.log('✅ Extracted via Vietnamese pattern:', extracted);
      return extracted;
    }
  }
  
  // Fallback: return original message if it's short enough
  if (message.length <= 100) {
    console.log('✅ Using original message (short):', message);
    return message;
  }
  
  // Last resort
  console.log('❌ Could not extract meaningful message, using fallback');
  return 'Có lỗi xảy ra. Vui lòng thử lại.';
};

/**
 * Common error messages mapping
 */
export const ERROR_MESSAGES = {
  ORGANIZATION_EXISTS: 'Tên tổ chức đã tồn tại',
  ORGANIZATION_INVALID: 'Tên tổ chức không hợp lệ',
  PHONE_EXISTS: 'Số điện thoại đã được sử dụng',
  PHONE_INVALID: 'Số điện thoại không hợp lệ',
  EMAIL_EXISTS: 'Email đã được sử dụng',
  EMAIL_INVALID: 'Email không hợp lệ',
  UNAUTHORIZED: 'Không có quyền thực hiện thao tác này',
  TOKEN_EXPIRED: 'Phiên đăng nhập đã hết hạn',
  NETWORK_ERROR: 'Lỗi kết nối mạng. Vui lòng thử lại.',
  UNKNOWN_ERROR: 'Có lỗi xảy ra. Vui lòng thử lại.'
} as const;
