<template>
  <div class="create-organization-step">
    <!-- Welcome Message -->
    <div class="text-center mb-8">
      <div class="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd" />
        </svg>
      </div>
      <h2 class="text-2xl font-bold text-gray-800 mb-2">T<PERSON><PERSON></h2>
      <p class="text-gray-600 max-w-md mx-auto">
        T<PERSON><PERSON><PERSON> lậ<PERSON> thông tin cơ bản về tổ chức của bạn để bắt đầu sử dụng hệ thống
      </p>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="max-w-2xl mx-auto space-y-6">
      <!-- Organization Name -->
      <div class="space-y-2">
        <label for="orgName" class="block text-sm font-semibold text-gray-700">
          Tên tổ chức <span class="text-red-500">*</span>
        </label>
        <div class="relative group">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
            <svg
              :class="[
                'h-5 w-5 transition-colors duration-200',
                errors.orgName
                  ? 'text-red-400'
                  : 'text-gray-400 group-focus-within:text-primary'
              ]"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            id="orgName"
            v-model="formData.orgName"
            type="text"
            :class="[
              'block w-full pl-10 pr-4 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
              errors.orgName ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white hover:border-gray-400'
            ]"
            placeholder="Nhập tên tổ chức của bạn"
            @blur="validateField('orgName')"
          />
        </div>
        <Transition name="slide-down">
          <span
            v-if="errors.orgName"
            class="text-sm text-red-600 flex items-center space-x-1"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <span>{{ errors.orgName }}</span>
          </span>
        </Transition>
      </div>

      <!-- Industry -->
      <div class="space-y-2">
        <label for="industry" class="block text-sm font-semibold text-gray-700">
          Ngành nghề
        </label>
        <select
          id="industry"
          v-model="formData.industry"
          class="block w-full px-4 py-3 border border-gray-300 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white hover:border-gray-400"
        >
          <option value="">Chọn ngành nghề</option>
          <option value="retail">Bán lẻ</option>
          <option value="restaurant">Nhà hàng</option>
          <option value="services">Dịch vụ</option>
          <option value="manufacturing">Sản xuất</option>
          <option value="technology">Công nghệ</option>
          <option value="healthcare">Y tế</option>
          <option value="education">Giáo dục</option>
          <option value="other">Khác</option>
        </select>
      </div>

      <!-- Company Size -->
      <div class="space-y-2">
        <label class="block text-sm font-semibold text-gray-700">
          Quy mô công ty
        </label>
        <div class="grid grid-cols-2 gap-3">
          <label
            v-for="size in companySizes"
            :key="size.value"
            class="relative flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200"
            :class="formData.companySize === size.value ? 'border-blue-500 bg-blue-50' : 'border-gray-300'"
          >
            <input
              v-model="formData.companySize"
              type="radio"
              :value="size.value"
              class="sr-only"
            />
            <div class="flex items-center">
              <div
                class="w-4 h-4 rounded-full border-2 flex items-center justify-center"
                :class="formData.companySize === size.value ? 'border-blue-500' : 'border-gray-300'"
              >
                <div
                  v-if="formData.companySize === size.value"
                  class="w-2 h-2 bg-blue-500 rounded-full"
                ></div>
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900">{{ size.label }}</div>
                <div class="text-xs text-gray-500">{{ size.description }}</div>
              </div>
            </div>
          </label>
        </div>
      </div>



      <!-- Progress Indicator -->
      <div 
        v-if="loading"
        class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200"
      >
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          <span class="text-sm font-medium text-blue-800">
            Đang tạo tổ chức...
          </span>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import type { StepComponentProps, StepResult } from '~/types/onboarding';
import { extractGraphQLError } from '~/utils/error';

// Props
const props = defineProps<StepComponentProps>();

// Emits
const emit = defineEmits<{
  'update:canProceed': [value: boolean];
}>();

// Form data
const formData = reactive({
  orgName: '',
  industry: '',
  companySize: ''
});

// Form validation
const errors = reactive({
  orgName: ''
});

// Loading state
const loading = ref(false);

// Company sizes
const companySizes = [
  { value: 'small', label: '1-10 nhân viên', description: 'Doanh nghiệp nhỏ' },
  { value: 'medium', label: '11-50 nhân viên', description: 'Doanh nghiệp vừa' },
  { value: 'large', label: '51-200 nhân viên', description: 'Doanh nghiệp lớn' },
  { value: 'enterprise', label: '200+ nhân viên', description: 'Tập đoàn' }
];

// Validation functions
const validateField = (field: string) => {
  switch (field) {
    case 'orgName':
      if (!formData.orgName.trim()) {
        errors.orgName = 'Tên tổ chức không được để trống';
      } else if (formData.orgName.trim().length < 2) {
        errors.orgName = 'Tên tổ chức phải có ít nhất 2 ký tự';
      } else {
        errors.orgName = '';
      }
      break;
  }
  updateCanProceed();
};

const validateForm = (): boolean => {
  validateField('orgName');

  return !errors.orgName;
};

const updateCanProceed = () => {
  const isValid = formData.orgName.trim().length >= 2;
  emit('update:canProceed', isValid);
};

// Submit handler
const handleSubmit = async () => {
  console.log('🚀 handleSubmit called!');

  if (!validateForm()) {
    console.log('❌ Form validation failed');
    return;
  }

  console.log('✅ Form validation passed');
  loading.value = true;

  try {
    const $sdk = useNuxtApp().$sdk;
    console.log('🔧 SDK instance:', $sdk);

    console.log('🏢 Creating organization...');

    // Create organization
    console.log('📞 Calling $sdk.auth.createOrg with:', formData.orgName);
    const orgResult = await $sdk.auth.createOrg(formData.orgName);
    console.log('📦 SDK response:', orgResult);
    const orgId = orgResult?.id;

    if (!orgId) {
      throw new Error('Không thể tạo tổ chức. Vui lòng thử lại.');
    }

    console.log('✅ Organization created with ID:', orgId);

    // Set organization context
    $sdk.setOrgId(orgId);

    // Set orgId in multiple places to ensure persistence
    const orgIdCookie = useCookie("orgId", {
      default: () => null,
      maxAge: 60 * 60 * 24 * 7, // 7 days
      secure: false,
      sameSite: 'lax'
    });
    orgIdCookie.value = orgId;

    console.log('🍪 Set orgId cookie:', orgId);
    console.log('🍪 Cookie value after set:', orgIdCookie.value);

    // Also set in tab context if available
    const { setOrgId: setTabOrgId } = useTabContext();
    setTabOrgId(orgId);
    console.log('📋 Set tab context orgId:', orgId);


    // Assign user roles in organization
    console.log('👤 Assigning user roles...');

    // Try to get userId from multiple sources
    const authStore = useAuthStore();
    const userDetail = authStore.user;
    const userId = userDetail?.id || props.context?.userData?.userLoginId || useCookie('userLoginId').value;

    console.log('🆔 User ID found:', userId);
    console.log('👤 Auth store user:', userDetail);
    console.log('📋 Context userData:', props.context?.userData);
    console.log('🍪 Cookie userLoginId:', useCookie('userLoginId').value);

    if (userId) {
      console.log('✅ User ID exists, proceeding with role assignment...');
      try {
        // Assign EMPLOYEE role (PERMISSION type)
        console.log('📞 Calling addRoleUser: EMPLOYEE, userId:', userId, 'type: PERMISSION');
        await $sdk.auth.addRoleUser('EMPLOYEE', userId, 'PERMISSION');
        console.log('✅ EMPLOYEE role assigned successfully');

        // Assign ORG_ADMIN role (POSITION type)
        console.log('📞 Calling addRoleUser: ORG_ADMIN, userId:', userId, 'type: POSITION');
        await $sdk.auth.addRoleUser('ORG_ADMIN', userId, 'POSITION');
        console.log('✅ ORG_ADMIN role assigned successfully');

        // Fetch updated userDetail to get new roles and orgPositionsMap
        console.log('🔄 Fetching updated userDetail after role assignment...');
        try {
          const currentToken = useCookie('token').value;
          if (currentToken) {
            const updatedUserDetail = await $sdk.auth.getUserDetail(currentToken);
            console.log('📦 Updated userDetail:', updatedUserDetail);

            if (updatedUserDetail) {
              // Update auth store with new user data including roles
              const authStore = useAuthStore();
              const updatedUser = {
                id: updatedUserDetail.partyId,
                name: updatedUserDetail.fullName,
                email: updatedUserDetail.email,
                phone: updatedUserDetail.phone,
                avatar: updatedUserDetail.avatarUrl,
                birthDate: updatedUserDetail.birthDate,
                roles: updatedUserDetail.orgPositionsMap?.[orgId] || []
              };

              authStore.setUser(updatedUser);
              console.log('✅ Auth store updated with new roles:', updatedUser.roles);

              // Update auth cookie with complete user data
              const authCookie = useCookie("auth") as any;
              authCookie.value = {
                user: updatedUser,
                token: currentToken
              };
              console.log('✅ Updated auth cookie with new user data');

              // Update org data cookie
              if (updatedUserDetail.orgPositionsMap) {
                useCookie("dataOrg").value = JSON.stringify(updatedUserDetail.orgPositionsMap);
                console.log('✅ Updated dataOrg cookie with new orgPositionsMap');

                // Update org store
                try {
                  const { useOrgStore } = await import('@/stores/org');
                  const orgStore = useOrgStore();
                  orgStore.setOrg(updatedUserDetail);
                  console.log('✅ Updated org store with new data');
                } catch (orgStoreError) {
                  console.error('⚠️ Error updating org store:', orgStoreError);
                }
              }
            }
          }
        } catch (fetchError: any) {
          console.error('⚠️ Error fetching updated userDetail:', fetchError);
          // Don't fail the whole process for this error
        }

      } catch (roleError: any) {
        console.error('⚠️ Error assigning roles:', roleError);
        console.error('⚠️ Role error details:', {
          message: roleError?.message,
          stack: roleError?.stack,
          response: roleError?.response
        });
        // Don't fail the whole process for role assignment errors
      }
    } else {
      console.warn('⚠️ No userId found, skipping role assignment');
      console.warn('⚠️ Available data:', {
        contextUserData: props.context?.userData,
        cookieUserLoginId: useCookie('userLoginId').value,
        allCookies: document.cookie
      });
    }

    // Complete step AFTER all operations are done
    const result: StepResult = {
      stepId: props.step.id,
      success: true,
      data: {
        orgId,
        orgResult,
        formData: { ...formData },
        rolesAssigned: !!userId
      }
    };

    console.log('🎯 All operations completed, finishing step...');
    useNuxtApp().$toast.success('Tạo tổ chức thành công!');
    props.onComplete(result);

  } catch (error: any) {
    console.error('❌ Error creating organization:', error);
    console.error('❌ Error details:', {
      message: error?.message,
      stack: error?.stack,
      response: error?.response,
      graphQLErrors: error?.graphQLErrors
    });

    // Extract meaningful error message
    const meaningfulError = extractGraphQLError(error);
    console.log('📝 Extracted error message:', meaningfulError);

    useNuxtApp().$toast.error(meaningfulError);
  } finally {
    console.log('🏁 handleSubmit finished, setting loading = false');
    loading.value = false;
  }
};

// Watch form changes
watch(formData, updateCanProceed, { deep: true });

// Initialize
onMounted(async () => {
  console.log('🏢 [CREATE ORGANIZATION] Component mounted!');
  console.log('🏢 [CREATE ORGANIZATION] Step data:', props.step);

  // Only run logic if this is actually the create-organization step
  if (props.step.id !== 'create-organization') {
    console.log('🏢 [CREATE ORGANIZATION] Not create-organization step, skipping logic');
    return;
  }

  // Ensure SDK has token from cookie
  const $sdk = useNuxtApp().$sdk;
  const cookieToken = useCookie('token').value;

  if (cookieToken && !$sdk.token) {
    console.log('🔄 Syncing token from cookie to SDK on mount...');
    $sdk.setToken(cookieToken);
    console.log('✅ Token synced:', $sdk.token);
  }

  // Check if user already has ORG_ADMIN role (only on first load, not during onboarding flow)
  // Skip this check if user is already in onboarding flow
  const isInOnboardingFlow = props.context?.config?.progress?.completedSteps > 0;

  if (!isInOnboardingFlow) {
    try {
      const dataOrgCookie = useCookie("dataOrg").value;

      if (dataOrgCookie) {
      let orgPositionsMap;

      // Handle different cookie value types
      if (typeof dataOrgCookie === 'object') {
        // Cookie is already an object
        orgPositionsMap = dataOrgCookie;
      } else if (typeof dataOrgCookie === 'string') {
        // Check for invalid serialization
        if (dataOrgCookie === '[object Object]' || dataOrgCookie.trim() === '') {
          console.warn('⚠️ Invalid dataOrg cookie value, skipping organization check');
          return;
        }

        // Try to parse JSON
        try {
          orgPositionsMap = JSON.parse(dataOrgCookie);
        } catch (parseError) {
          console.warn('⚠️ Failed to parse dataOrg cookie:', parseError);
          return;
        }
      } else {
        console.warn('⚠️ Unexpected dataOrg cookie type:', typeof dataOrgCookie);
        return;
      }

      console.log('🔍 Checking existing organizations:', orgPositionsMap);

      // Check if user has ORG_ADMIN role in any organization
      const hasOrgAdminRole = Object.values(orgPositionsMap).some((roles: any) =>
        Array.isArray(roles) && roles.includes('ORG_ADMIN')
      );

      if (hasOrgAdminRole) {
        console.log('✅ User already has ORG_ADMIN role - skipping organization creation');

        // Complete step immediately
        const result: StepResult = {
          stepId: props.step.id,
          success: true,
          data: {
            skipped: true,
            reason: 'User already has ORG_ADMIN role',
            orgPositionsMap
          }
        };

        useNuxtApp().$toast.success('Bạn đã có quyền quản trị tổ chức!');
        props.onComplete(result);
        return;
      }
      }
    } catch (error) {
      console.error('❌ Error checking existing organizations:', error);
    }
  }

  // Load existing data if available
  if (props.step.data) {
    Object.assign(formData, props.step.data.formData || {});
  }
  updateCanProceed();
});
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Enhanced focus styles */
.focus\:ring-primary:focus {
  --tw-ring-color: #3f51b5;
}

/* Form transitions */
.create-organization-step input,
.create-organization-step textarea,
.create-organization-step select {
  transition: all 0.2s ease-in-out;
}

.create-organization-step input:focus,
.create-organization-step textarea:focus,
.create-organization-step select:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Icon visibility fix */
.create-organization-step .relative .absolute {
  z-index: 10;
}
</style>
