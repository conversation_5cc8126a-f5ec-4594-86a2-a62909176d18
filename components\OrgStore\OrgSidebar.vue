<template>
  <div class="w-80 bg-white shadow-xl border-r border-gray-200 flex flex-col">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-xl font-bold text-white">{{ title }}</h1>
          <p class="text-blue-100 text-sm mt-1">{{ subtitle }}</p>
        </div>
        <div class="flex space-x-2">
          <button
            v-if="showSeparateButton"
            @click="$emit('separate')"
            class="px-3 py-1.5 text-xs bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors"
            title="Chế độ riêng biệt"
          >
            Tách riêng
          </button>
          <button
            @click="$emit('test-api')"
            class="px-3 py-1.5 text-xs bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors"
            title="Test API"
          >
            Test API
          </button>
        </div>
      </div>
    </div>

    <!-- Organizations List -->
    <div class="flex-1 overflow-y-auto p-4">
      <div v-if="loading" class="flex items-center justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
      
      <div v-else-if="organizations?.length > 0" class="space-y-2">
        <button
          v-for="(org, index) in organizations"
          :key="`org-${org.name}-${index}`"
          @click="$emit('select', org)"
          class="w-full text-left p-4 rounded-lg border transition-all duration-200 hover:shadow-md"
          :class="{
            'bg-blue-50 border-blue-200 shadow-sm': selectedOrg?.name === org.name,
            'bg-white border-gray-200 hover:border-blue-300': selectedOrg?.name !== org.name
          }"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="font-semibold text-gray-900">{{ org.name }}</h3>
              <p class="text-sm text-gray-500 mt-1">{{ getRoleDisplayText(org.role) }}</p>
            </div>
            <div v-if="selectedOrg?.name === org.name" class="text-blue-600">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </button>
      </div>

      <div v-else class="text-center py-8">
        <div class="text-gray-400 mb-2">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <p class="text-gray-500">{{ emptyMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  organizations?: any[];
  selectedOrg?: any;
  loading?: boolean;
  title?: string;
  subtitle?: string;
  emptyMessage?: string;
  showSeparateButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  organizations: () => [],
  selectedOrg: null,
  loading: false,
  title: "Chọn Tổ chức",
  subtitle: "Chọn tổ chức để xem cửa hàng",
  emptyMessage: "Không có tổ chức nào",
  showSeparateButton: true
});

const emit = defineEmits<{
  select: [org: any];
  separate: [];
}>();

// Utility functions
const getRoleDisplayText = (roles: string[]) => {
  if (!roles || roles.length === 0) return "Không có quyền";
  const primaryRole = roles[0];
  return getRoleLabel(primaryRole);
};

const getRoleLabel = (role: string) => {
  const roleLabels: { [key: string]: string } = {
    ORG_ADMIN: "Quản trị viên",
    EMPLOYEE: "Nhân viên",
    MANAGER: "Quản lý",
    OWNER: "Chủ sở hữu",
  };
  return roleLabels[role] || role;
};
</script>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
