import { defineStore } from "pinia";
import { ref, computed } from "vue";
import useOrder from "@/composables/useOrder";
import useCustomer from "@/composables/useCustomer";
import useLogger from "@/composables/useLogger";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import type { ShippingAddress } from "~/types/ShippingAddress";
import type { Auth } from "~/types/Auth";

export const useOrderStore = defineStore("order", () => {
  const {
    getOrderDetail,
    addOrderLineItems,
    updateQuantityProductInOrder,
    updateOrderCustomer,
    updateOrderDescription,
    disableOrderItem,
    updateStatusOpen,
    openOrderById,
    updateStatusApproved,
    openPaymentByOrderId,
    getShippingInfo,
    createShippingInfo,
    updateShippingInfo,
    deleteShippingInfo,
    removeProductInOrder,
    getListShippingCarrier,
    getShippingService,
    updateOrderType,
    updateShippingOrder,
    updateShippingService,
    updateShippingFee,
    searchEmployes,
    updateSaleEmployee,
    updateWareHouseToOrder,
    fetchListSellOrder,
    createOrderTemp,
    CreateOrderV1,
    fetchOrderReturnDetails,
    enableProductDiary,
    updateEmployee,
    updatePriceInOrder,
    removeShippingAddress,
    removeShippingInfo,
    updateVatInorder,
  } = useOrder();
  const { createSellingDiary } = useDiary();
  const { getCustomerById } = useCustomer();
  const { getInventory, getInventoryV2 } = useWarehouse();
  const {
    fetchCampaignActionActiveNow,
    getCampaignActiveNow,
    searchProductGiftPromotionResponse,
  } = useCampaign();
  const { getDynamicForm, updateDynamicForm } = usePortal();
  const app = useNuxtApp();

  const { writeLog } = useLogger();
  const router = useRouter();
  const route = useRoute();
  const authStore = useAuthStore();
  const user = computed(() => authStore.user);

  // Use tab-isolated context instead of cookies
  const { orgId, storeId } = useTabContext();
  const orderDetail = ref<any>(null);
  const isLoadingOrder = ref(false);
  const loadingCreateOrder = ref();
  const isLoadingPayment = ref<boolean>(false);
  const customerInOrder = ref<any>(null);
  const isAlert = ref<boolean>(false);
  const isAlertWareHouse = ref<boolean>(false);
  const dataInventory = ref<any>([]);
  const warehouseId = useCookie("warehouseId").value;
  // lưu danh sách id sản phẩm disible khi tạo order từ nhật ký
  const disabledProducts = ref<string[]>([]);
  const dataShippingAddress = ref<any>();
  const dataShippingAddressDefault = ref<any>();
  const dataDefaultAddress = ref<any>();
  const valueShippingAddress = ref<string>("at-counter");
  const dataShippingCarrier = ref<any>([]);
  const dataShippingServices = ref<any>([]);
  const idShippingCarrier = ref<string>("");
  const idShippingService = ref<string>("");
  const shippingFee = ref<number>(0);
  const campaign = ref<any>();
  const paymentAmount = ref();
  const dataDynamicForm = ref();
  //
  const isOpenGiftProduct = ref(false);
  const dataProductGift = ref<any>([]);
  //
  const isOpenShippingAddress = ref(false);
  const isNotDraft = ref();
  //
  const dataInvoice = ref([]);
  const { getInvoicesOfOrder } = useInvoice();
  const handleGetInvoiceOfOrder = async () => {
    try {
      const response = await getInvoicesOfOrder(orderDetail.value?.id);
      dataInvoice.value = response[0]?.attributes?.find(
        (item: any) => item.name === "INV_NO"
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const listOrder = ref<any>([]);
  // get danh sách tab order
  const getListOrder = async (data: any) => {
    listOrder.value = [];
    try {
      const response = await fetchListSellOrder(data);
      listOrder.value = response.data?.data;
    } catch (error) {
      throw error;
    }
  };
  const updateOrder = async (orderId: string) => {
    try {
      const response = await getOrderDetail(orderId);
      orderDetail.value = response;
      response?.order?.shippingAddress
        ? (isOpenShippingAddress.value = true)
        : (isOpenShippingAddress.value = false);
    } catch (error) {
      throw error;
    }
  };
  const handleAddOrderToListOrder = (order: any) => {
    listOrder.value.unshift({
      id: order,
    });
    console.log("listOrder", listOrder.value);
  };
  const handleChangeOrderId = (orderIdPre: string, orderId: string) => {
    const order = listOrder.value.find((order: any) => order.id === orderIdPre);
    if (order) {
      order.id = orderId;
    }
  };

  const loading = ref(false);
  // tạo nhật ký
  const createOrder = async () => {
    loading.value = true;
    customerInOrder.value = null;
    localStorage.setItem("paymentAmount", "0");

    try {
      const response = await createSellingDiary();
      // await getOrderById(response.orderId);
      loading.value = false;
      return response;
    } catch (error) {
      console.error("Error creating order:", error);
      throw error;
    }
  };
  const customerStore = useCustomerStore();

  const getOrderById = async (orderId: string) => {
    isLoadingOrder.value = true;
    disabledProducts.value = [];
    localStorage.setItem("paymentAmount", "0");
    try {
      const response = await getOrderDetail(orderId);

      orderDetail.value = response;
      dataInventory.value = [];
      handleGetProductDiary();
      await handleAddInventory();
      /// nếu có khách hàng
      response?.order?.shippingAddress
        ? (isOpenShippingAddress.value = true)
        : (isOpenShippingAddress.value = false);
      if (!response?.order?.ownerPartyId) {
        customerInOrder.value = null;
      } else if (
        !customerInOrder.value ||
        customerInOrder.value.id !== response?.order?.ownerPartyId
      ) {
        await getCustomerId(response?.order?.ownerPartyId);
        // await customerStore.handleGetTopic(response?.order?.ownerPartyId);
      }
    } catch (error) {
      throw error;
    } finally {
      isLoadingOrder.value = false;
    }
  };
  // get by order
  const getBuyOrderById = async (orderId: string) => {
    const auth = useCookie("auth").value as unknown as Auth;
    isLoadingOrder.value = true;
    disabledProducts.value = [];

    localStorage.setItem("paymentAmount", "0");
    try {
      const response = await getOrderDetail(orderId);

      orderDetail.value = response;
      dataInventory.value = [];
      console.log("auth", auth?.user);
      if (!response.order.ownerPartyId) {
        await addCustomerToOrder(auth?.user, "");
      } else if (
        !customerInOrder.value ||
        customerInOrder.value.id !== response.order.ownerPartyId
      ) {
        await getCustomerId(response.order.ownerPartyId);
      }
    } catch (error) {
      console.error("Error getting order by id:", error);
    } finally {
      isLoadingOrder.value = false;
    }
  };

  // test ware housse
  const handleAddInventory = async () => {
    const data: any = [];
    if (
      orderDetail.value?.activeOrderItemProfiles?.length > 0 &&
      orderDetail.value?.order?.customAttribute?.facilityId
    ) {
      orderDetail.value?.activeOrderItemProfiles.map(async (item: any) => {
        const sku = item.orderLineItem.variant.sku;
        const test = {
          productId: item.orderLineItem.variant?.product?.id,
          variantId:
            item.orderLineItem.variant?.id ===
            item.orderLineItem.variant?.product?.id
              ? ""
              : item.orderLineItem.variant?.id,
          sku: item.orderLineItem.variant?.sku,
        };
        data.push(test);
      });

      const res = await getInventoryV2(
        orderDetail.value?.order?.customAttribute?.facilityId,
        data
      );
      res.forEach((inventoryItem: any) => {
        const matchingProduct = orderDetail.value?.activeOrderItemProfiles.find(
          (item: any) => {
            const productId = item.orderLineItem.variant?.product?.id;
            const variantId = item.orderLineItem.variant?.id;
            const sku = item.orderLineItem.variant?.sku;
            if (productId === inventoryItem.productId) {
              if (
                !inventoryItem?.variantId ||
                inventoryItem.variantId === variantId ||
                inventoryItem.sku === sku
              ) {
                return true;
              }
            }
            return false;
          }
        );

        if (matchingProduct) {
          matchingProduct.orderLineItem.inventory = inventoryItem;
        }
      });

      // sản phẩm nào có tồn kho = 0 thì thêm vào mảng
      orderDetail.value?.activeOrderItemProfiles.forEach((item: any) => {
        if (!(item?.orderLineItem?.inventory?.orderAble > 0)) {
          dataInventory.value.push(item);
        }
      });
    }
  };

  //

  const dataListProductDiary = ref<any>([]);
  // hàm kiểm tra sản phẩm nào chưa tồn kho sản phẩm nào đã tồn kho
  const handleGetProductDiary = () => {
    const activeIds = orderDetail.value?.activeOrderItemProfiles?.map(
      (item: any) => item.id
    );
    const response = orderDetail.value?.orderItemProfiles?.filter(
      (item: any) => !activeIds.includes(item.id)
    );
    dataListProductDiary.value = response;
  };

  //
  const handleCheckInventory = async (product: any) => {
    try {
      const data = [
        {
          productId: product?.variant?.product?.id,
          variantId:
            product?.variant?.id === product?.variant?.product?.id
              ? ""
              : product?.variant?.id,
          sku: product?.variant?.sku,
        },
      ];
      if (orderDetail.value?.order?.customAttribute?.facilityId) {
        const res = await getInventoryV2(
          orderDetail.value?.order?.customAttribute?.facilityId,
          data
        );
        return res[0];
      }
    } catch (error) {
      throw error;
    }
  };

  //

  // V2 get order
  const getOrderByIdV2 = async (orderId: string, warehouseId: string) => {
    isLoadingOrder.value = true;
    dataInventory.value = [];
    try {
      const response = await getOrderDetail(orderId);
      orderDetail.value = response;
      const data: any = [];
      // nếu có sản phẩm mới get tồn kho
      if (orderDetail.value?.activeOrderItemProfiles.length > 0) {
        orderDetail.value?.activeOrderItemProfiles.map(async (item: any) => {
          const sku = item.orderLineItem.variant.sku;
          const test = {
            productId: item.orderLineItem.variant?.product?.id,
            variantId:
              item.orderLineItem.variant?.id ===
              item.orderLineItem.variant?.product?.id
                ? ""
                : item.orderLineItem.variant?.id,
            sku: item.orderLineItem.variant?.sku,
          };
          data.push(test);
        });
        const res = await getInventoryV2(warehouseId || "", data);
        res.forEach((inventoryItem: any) => {
          const matchingProduct =
            orderDetail.value?.activeOrderItemProfiles.find((item: any) => {
              const productId = item.orderLineItem.variant?.product?.id;
              const variantId = item.orderLineItem.variant?.id;

              if (productId === inventoryItem.productId) {
                if (
                  !inventoryItem?.variantId ||
                  inventoryItem.variantId === variantId
                ) {
                  return true;
                }
              }
              return false;
            });

          if (matchingProduct) {
            matchingProduct.orderLineItem.inventory = inventoryItem;
          }
        });

        // sản phẩm nào có tồn kho = 0 thì thêm vào mảng
        orderDetail.value?.activeOrderItemProfiles.forEach((item: any) => {
          if (!(item?.orderLineItem?.inventory?.orderAble > 0)) {
            dataInventory.value.push(item);
          }
        });
      }
      response?.order?.shippingAddress
        ? (isOpenShippingAddress.value = true)
        : (isOpenShippingAddress.value = false);
      // tiếp tục kiểm tra khách hàng
      if (!response.order.ownerPartyId) {
        customerInOrder.value = null;
        // await handleGetCampaignActiveNow("", "");
      } else if (
        !customerInOrder.value ||
        customerInOrder.value.id !== response.order.ownerPartyId
      ) {
        await getCustomerId(response.order.ownerPartyId);
        // await handleGetCampaignActiveNow("", response.order.ownerPartyId);
      }
    } catch (error) {
      console.error("Error getting order by id:", error);
    } finally {
      isLoadingOrder.value = false;
    }
  };
  // lấy thông tin chiến dịch giảm giá theo hạng thành viê
  const getCampaignActionActiveNow = async (
    ownerId: string,
    campaignType: string
  ) => {
    campaign.value = [];
    try {
      const response = await fetchCampaignActionActiveNow(
        ownerId,
        campaignType
      );
      // campaign.value = response;
      console.log("campagin action", response);
    } catch (error) {
      throw error;
    }
  };
  const campaignLevel = ref();
  const handleGetCampaignActiveNow = async (
    campaignActionType?: string,
    customerId?: string
  ) => {
    campaignLevel.value = {};
    campaign.value = [];
    try {
      const response = await getCampaignActiveNow(
        campaignActionType,
        customerId
      );
      const result = response?.find((campaigns: any) => {
        const res = campaigns?.campaignActions?.find(
          (campaign: any) => campaign?.type === "PROMOTION_ORDER_MEMBER"
        );

        return res !== undefined;
      });
      campaignLevel.value = result;
      campaign.value = response;
    } catch (error) {
      throw error;
    }
  };

  // Toggle danh sách id sản phẩm disable khi tạo order từ nhật ký
  const toggleDisabledProduct = (productId: any) => {
    const index = disabledProducts.value.indexOf(productId);
    if (index !== -1) {
      disabledProducts.value.splice(index, 1);
    } else {
      disabledProducts.value.push(productId);
    }
  };
  // disable sản phẩm khi tạo order từ danh sách disabledProducts
  const handleDisibleOrderItems = async () => {
    try {
      //lấy từ danh sách disabledProducts
      const productIds = disabledProducts.value;
      if (!orderDetail.value) return;
      for (const productId of productIds) {
        await disableOrderItem(orderDetail.value.id, productId);
      }
    } catch (error: any) {
      console.error("Error disabling order items:", error);
      throw error;
    }
  };
  // tạo đơn từ nhật ký
  const createOrderFromDiary = async () => {
    loadingCreateOrder.value = true;
    try {
      if (orderDetail.value?.activeOrderItemProfiles.length > 0) {
        localStorage.setItem("paymentAmount", "0");
        // xử lý disable sản phẩm
        await handleDisibleOrderItems();
        // await getOrderById(orderDetail.value.id);

        //chuyển trạng thái đơn hàng từ nháp sang mới tạo
        const data = {
          date_create_from: Date.now().toString(),
          currentPage: 1,
          maxResult: 5,
          status: [10],
        };
        await updateStatusOpen(orderDetail.value.id);
        await getListOrder(data);

        openOrderById(orderDetail.value.id);
      } else {
        app.$toast.warning(
          "Chưa có sản phẩm trong giỏ hàng. Vui lòng thêm sản phẩm trước khi thanh toán."
        );
      }

      loadingCreateOrder.value = false;
    } catch (error) {
      console.error("Error creating order:", error);
      throw error;
    }
  };

  //
  const handleRemoveProduct = async (orderId: string, productId: string) => {
    try {
      if (orderDetail.value?.activeOrderItemProfiles?.length > 1) {
        const response = await disableOrderItem(orderId, productId);
        await getOrderById(orderId);
        return response;
      } else {
        app.$toast.warning("Không thể xóa sản phẩm cuối cùng ");
      }
    } catch (error) {
      throw error;
    }
  };
  // Room orders functionality has been removed with SQLite
  // hàm tạo đơn mua
  const handleCreateBuyOrder = async () => {
    try {
      // kiểm tra nếu có khách hàng - > mới cho tạo đơn
      if (customerInOrder.value) {
        // kiểm tra nếu có sản phẩm - > mới cho tạo đơn

        if (orderDetail.value?.activeOrderItemProfiles.length) {
          // kiểm tra vận chuyển hay tại quầy
          if (valueShippingAddress.value === "delivery") {
            if (dataDefaultAddress.value) {
              // kiểm tra đã chọn ĐVVC hay chưa
              if (idShippingCarrier.value === "") {
                app.$toast.warning("Vui lòng chọn đơn vị vận chuyển");
              } else {
                // Kiểm tra đã chọn hình thức vận chuyển hay chưa
                if (idShippingService.value === "") {
                  app.$toast.warning("Vui lòng chọn hình thức vận chuyển");
                } else {
                  isLoadingPayment.value = true;
                  await Promise.all([
                    addCustomerToOrder(
                      customerInOrder.value,
                      dataDefaultAddress.value?.id
                    ),
                    updateOrderType(orderDetail.value.id, "SHIPMENT", ""),
                  ]);
                  if (orderDetail.value.status !== "APPROVED") {
                    updateStatusApproved(orderDetail.value.id);
                  }
                  await updateShippingOrder(
                    orderDetail.value.id,
                    idShippingCarrier.value
                  );
                  await updateShippingService(
                    orderDetail.value.id,
                    idShippingService.value,
                    ""
                  );
                  await updateShippingFee(
                    orderDetail.value.id,
                    shippingFee.value
                  );
                  isLoadingPayment.value = false;
                  openPaymentByOrderId(orderDetail.value.id);
                }
              }
            } else {
              app.$toast.warning("Chưa có địa chỉ nhận hàng vui lòng thêm mới");
            }
          } else {
            isLoadingPayment.value = true;
            if (orderDetail.value.status !== "APPROVED") {
              updateStatusApproved(orderDetail.value.id);
            }
            isLoadingPayment.value = false;
            openPaymentByOrderId(orderDetail.value.id);
          }
        } else {
          app.$toast.warning(
            "Chưa có sản phẩm trong giỏ hàng. Vui lòng thêm sản phẩm trước khi thanh toán."
          );
        }
      } else {
        app.$toast.warning("Vui lòng thêm khách hàng trước khi tạo đơn");
      }
    } catch (error) {
      console.error("Error placing order:", error);
      throw error;
    }
  };

  // hàm tạo đơn bán
  const handlePlaceOrder = async () => {
    try {
      if (dataDefaultAddress.value?.version !== "v2") {
        return app.$toast.warning("Vui lòng cập nhật địa chỉ giao hàng ");
      }
      if (!customerInOrder.value) {
        return app.$toast.warning("Vui lòng thêm khách hàng trước khi tạo đơn");
      }

      if (!orderDetail.value?.activeOrderItemProfiles.length) {
        return app.$toast.warning(
          "Chưa có sản phẩm trong giỏ hàng. Vui lòng thêm sản phẩm trước khi thanh toán."
        );
      }
      const { data } = await useFetch("/data/setting.json");
      const auth = useCookie("auth").value as unknown as Auth;

      const res = await handleCheckWarehouse(data.value);
      if (!res) {
        const dataSettingOrg = data.value as any;
        const arrRoles = dataSettingOrg?.find(
          (org: any) => org?.storeId === orgId.value
        );
        if (arrRoles?.rolesExportInvoice?.length) {
          const arrRolesDefault = ["SALE_ADMIN", "SALE_MANAGER"];
          const isRoleSaleAdmin = auth?.user?.roles?.filter((role: any) =>
            arrRolesDefault?.includes(role)
          );
          if (!isRoleSaleAdmin?.length) {
            isAlertWareHouse.value = true;
            return;
          }
        }
      }
      if (dataInventory.value.length !== 0) {
        isAlert.value = true;
      }

      if (isAlert.value) return;

      await processPayment(orderDetail.value.id);
    } catch (error) {
      throw error;
    }
  };
  // hàm a
  const handleCheckWarehouse = async (dataSettingOrg: any) => {
    const result = dataSettingOrg?.find(
      (org: any) => org?.storeId === orgId.value
    );
    if (result?.isExportInvoiceForProduct) {
      const data = <any>[];
      orderDetail.value?.activeOrderItemProfiles?.map(async (item: any) => {
        const sku = item.orderLineItem.variant.sku;
        const test = {
          productId: item?.orderLineItem.variant?.product?.id,
          variantId:
            item?.orderLineItem?.variant?.id ===
            item.orderLineItem.variant?.product?.id
              ? ""
              : item.orderLineItem.variant?.id,
          sku: item.orderLineItem.variant?.sku,
        };
        data.push(test);
      });
      const res = await getInventoryV2(
        orderDetail.value?.order?.customAttribute?.facilityId,
        data
      );

      if (res?.length) {
        for (const item of res) {
          if (item?.orderAble < 5) {
            return false;
          }
        }
      }
      return true;
    } else {
      return true;
    }
  }; // hàm bên FOX yêu cầu
  const processPayment = async (orderId: string) => {
    if (isOpenShippingAddress.value) {
      if (!dataDefaultAddress.value) {
        return app.$toast.warning(
          "Chưa có địa chỉ nhận hàng vui lòng thêm mới"
        );
      }
      if (!idShippingCarrier.value) {
        return app.$toast.warning("Vui lòng chọn đơn vị vận chuyển");
      }

      if (!idShippingService.value) {
        return app.$toast.warning("Vui lòng chọn hình thức vận chuyển");
      }
    }

    isLoadingPayment.value = true;

    if (orderDetail.value.status !== "APPROVED") {
      await updateStatusApproved(orderId);
    }

    isLoadingPayment.value = false;
    openPaymentByOrderId(orderId);
  };

  // hàm tiếp tục tạo đơn
  const handleContinueCreateOrder = async () => {
    try {
      // if (valueShippingAddress.value === "delivery") {
      //   return handleDeliveryOrders();
      // }
      return handlePickupOrder();
    } catch (error) {
      console.error("Error continuing order creation:", error);
      throw error;
    }
  };

  const handleDeliveryOrders = async () => {
    if (!dataDefaultAddress.value) {
      return app.$toast.warning("Chưa có địa chỉ nhận hàng vui lòng thêm mới");
    }

    if (!idShippingCarrier.value) {
      return app.$toast.warning("Vui lòng chọn đơn vị vận chuyển");
    }

    if (!idShippingService.value) {
      return app.$toast.warning("Vui lòng chọn hình thức vận chuyển");
    }

    if (
      disabledProducts.value?.length >=
      orderDetail.value?.activeOrderItemProfiles.length
    ) {
      return app.$toast.warning("Vui lòng để lại 1 sản phẩm trước khi tạo đơn");
    }

    isLoadingPayment.value = true;

    await processDisabledProducts();

    await Promise.all([
      addCustomerToOrder(customerInOrder.value, dataDefaultAddress.value?.id),
      updateOrderType(orderDetail.value.id, "SHIPMENT", ""),
    ]);

    if (orderDetail.value.status !== "APPROVED") {
      await updateStatusApproved(orderDetail.value.id);
    }
    await updateShippingOrder(orderDetail.value.id, idShippingCarrier.value);
    await updateShippingService(
      orderDetail.value.id,
      idShippingService.value,
      ""
    );
    await updateShippingFee(orderDetail.value.id, shippingFee.value);
    finalizeOrder();
  };

  const handlePickupOrder = async () => {
    if (isOpenShippingAddress.value) {
      if (!dataDefaultAddress.value) {
        return app.$toast.warning(
          "Chưa có địa chỉ nhận hàng vui lòng thêm mới"
        );
      }
      if (!idShippingCarrier.value) {
        return app.$toast.warning("Vui lòng chọn đơn vị vận chuyển");
      }

      if (!idShippingService.value) {
        return app.$toast.warning("Vui lòng chọn hình thức vận chuyển");
      }
    }

    if (
      disabledProducts.value?.length >=
      orderDetail.value?.activeOrderItemProfiles.length
    ) {
      return app.$toast.warning("Vui lòng để lại 1 sản phẩm trước khi tạo đơn");
    }
    isLoadingPayment.value = true;

    await processDisabledProducts();

    if (orderDetail.value.status !== "APPROVED") {
      await updateStatusApproved(orderDetail.value.id);
    }

    finalizeOrder();
  };

  const processDisabledProducts = async () => {
    if (disabledProducts.value?.length > 0) {
      await Promise.all(
        disabledProducts.value.map((item) =>
          disableOrderItem(orderDetail.value.id, item)
        )
      );
    }
  };

  const finalizeOrder = () => {
    isLoadingPayment.value = false;
    isAlert.value = false;
    dataInventory.value = [];
    openPaymentByOrderId(orderDetail.value.id);
  };

  // hàm tạo khi loại bỏ các sản phẩm đã hết hàng
  const handleCreateOrderWithInventory = async () => {
    if (
      dataInventory.value.length <
      orderDetail.value?.activeOrderItemProfiles.length
    ) {
      for (const item of dataInventory.value) {
        await disableOrderItem(orderDetail.value.id, item.id);
      }
      handleContinueCreateOrder();
    } else {
      app.$toast.warning("Vui lòng để lại 1 sản phẩm trước khi tạo đơn");
    }
  };
  // check if product is already in order
  const checkProductInOrder = (product: any) => {
    if (!orderDetail.value) return false;
    const orderLines = orderDetail.value.orderItemProfiles;
    // trả về sản phẩm nếu đã tồn tại trong order
    return orderLines?.find(
      (line: any) => line.orderLineItem?.variant?.id === product?.id
    );
  };
  const updateQuantity = async (productId: any, quantity: number) => {
    try {
      await updateQuantityProductInOrder(
        orderDetail.value.id,
        productId,
        quantity
      );
      await updateOrder(orderDetail.value?.id);
    } catch (error: any) {
      throw error;
    }
  };
  // const router = useRouter()
  const returnStore = returnOrderStore();
  const addProductToOrder = async (product: any) => {
    if (typeof product?.price === "undefined" || product?.price === null) {
      app.$toast.error("Sản phẩm chưa có giá, không thêm vào được đơn hàng");
      return;
    }
    if (orderDetail.value?.status === "CANCELLED") {
      app.$toast.error(
        "Đơn hàng đang trạng thái hủy không thể thêm sản phẩm vào"
      );
      return;
    }
    const warehouseId = useCookie("warehouseId").value;
    if (orderDetail.value) {
      const existingProduct = checkProductInOrder(product);

      if (existingProduct) {
        await handleExistingProduct(existingProduct, product);
        return;
      }
      const lineItems = [
        {
          quantity: 1,
          product_id: product?.id,
          discount_amount: 0,
          input_price: product?.price,
          parent_id: "",
        },
      ];
      try {
        const [responseAddOrderLineItems, responseProductGift]: any =
          await Promise.allSettled([
            addOrderLineItems(orderDetail.value.id, lineItems),
            handleSearchProductGift(product),
          ]);
        console.log(
          "responseAddOrderLineItems",
          responseAddOrderLineItems.value?.data[0]
        );
        await handleProductGift(
          product,
          orderDetail.value?.id,
          responseAddOrderLineItems.value?.data[0]
        );
        console.log("responseAddOrderLineItems", responseAddOrderLineItems);
        if (product?.vat) {
          const res = await updateVatInorder(
            orderDetail.value.id,
            responseAddOrderLineItems.value?.data[0],
            product?.vat
          );
        }
      } catch (error: any) {
        throw error;
      } finally {
        await getOrderById(orderDetail.value.id);
      }
    } else {
      const lineItems = [
        {
          quantity: 1,
          product_id: product?.id,
          discount_amount: 0,
          input_price: product?.price,
          parent_id: "",
        },
      ];
      const routee = useRoute();
      const auth = useCookie("auth").value as unknown as Auth;

      //flow tạo đơn trả
      if (routee.path === "/order/return") {
        const data = {
          exchange_for_order_id: returnStore.orderReturn?.id,
          customer: {
            id: returnStore.orderReturn?.order?.ownerPartyId,
            email: returnStore.orderReturn?.order?.ownerEmail,
            name: returnStore.orderReturn?.order?.ownerName,
            phone: returnStore.orderReturn?.order?.ownerPhone,
          },
          customer_id: returnStore.orderReturn?.order?.ownerPartyId,
          line_items: lineItems,
          warehouse_id: warehouseId,
          source: "DRAFT",
          // referral_code: "string",
          exchange_order: true,
          orderType: "POS_SALE",
          note: returnStore.noteOrderReturn,
          sale_id: returnStore.employeeCreateOrderReturn?.id,
        };
        const res = await CreateOrderV1(data, "WEB", true, "");
        console.log("res", res);
        await router.push({
          query: {
            ...router.currentRoute.value.query,
            orderId: res?.data?.order_id,
          },
        });
        await getOrderById(res?.data?.order_id);
        return;
      }

      // flow không có đơn -> tạo đơn -> update status + add product -> navigate
      const response = await createOrderTemp({
        status: "DRAFT",
        orderType: "POS_SALE",
        platform: "WEB",
        // source: "cart",
        time: new Date().getTime(),
        warehouseId: warehouseId,
      });
      // return;
      const [res1, res2, res3, res4] = await Promise.all([
        updateEmployee(response.data.orderId, auth?.user?.id, auth?.user?.id),
        updateStatusOpen(response.data.orderId),
        addOrderLineItems(response.data.orderId, lineItems),
        handleSearchProductGift(product),
      ]);
      console.log("res", res3);
      await handleProductGift(product, response.data.orderId, res3?.data[0]);
      if (product?.vat) {
        const res = await updateVatInorder(
          response.data.orderId,
          res3?.data[0],
          product?.vat
        );
      }
      if (listOrder.value) {
        handleChangeOrderId(
          routee.query.orderId as string,
          response.data.orderId
        );
      }

      router.push({
        query: {
          ...router.currentRoute.value.query,
          orderId: response.data.orderId,
        },
      });
      return response;
    }
  };
  const handleSearchProductGift = async (product: any) => {
    dataProductGift.value = [];
    try {
      const response = await searchProductGiftPromotionResponse(
        [product?.id],
        ""
      );
      dataProductGift.value = response?.content;

      return response;
    } catch (error) {
      throw error;
    }
  };
  // Hàm sử tìm kiếm sản phẩm quà tặng
  const handleProductGift = async (
    product: any,
    orderId: string,
    orderLineItem: string
  ) => {
    // const response = await handleSearchProductGift(product);
    if (dataProductGift.value?.length > 0) {
      // th có 1 chiến dịch - th có 2 chiến dịch
      if (dataProductGift.value?.length === 1) {
        // th có 1 chiến dịch
        // chiến dịch đó đc tất cả sp hay 1 sp
        if (dataProductGift.value[0]?.quantityLimit === 1) {
          // chiến dịch cho chọn 1 thì hiện popup
          isOpenGiftProduct.value = true;
          return;
        } else {
          // chiến dịch cho chọn nhiều thì tự thêm
          const data = <any>[];
          //
          if (!orderDetail.value) {
            // nếu không có orderDetail
            dataProductGift.value[0]?.giftPromotions?.forEach((item: any) => {
              if (item.fromQuantity === 1) {
                data.push({
                  quantity: item?.toQuantity,
                  gift: true,
                  product_id: item?.toProductId,
                  gift_condition_product_id:
                    item?.toProductId === orderLineItem ? "" : orderLineItem,
                  gift_campaign_id: dataProductGift.value[0]?.campaignId,
                  gift_campaign_action_id:
                    dataProductGift.value[0]?.campaignActionId,
                });
              }
            });
            console.log("data3", data);
          } else {
            // nếu có orderDetail
            //
            const res = orderDetail.value?.activeOrderItemProfiles.find(
              (item: any) => {
                return (
                  item.orderLineItem.variant?.id ===
                  dataProductGift.value[0]?.productId
                );
              }
            );
            console.log("res", res);
            if (res) {
              dataProductGift.value[0]?.giftPromotions?.forEach((item: any) => {
                if (
                  item.fromQuantity <=
                  res.orderLineItem?.currentQuantity + 1
                ) {
                  data.push({
                    quantity: item?.toQuantity,
                    gift: true,
                    product_id: item?.toProductId,
                    gift_condition_product_id:
                      item?.toProductId === orderLineItem ? "" : orderLineItem,
                    gift_campaign_id: dataProductGift.value[0]?.campaignId,
                    gift_campaign_action_id:
                      dataProductGift.value[0]?.campaignActionId,
                  });
                }
              });
              console.log("data2", data);
            } else {
              dataProductGift.value[0]?.giftPromotions?.forEach(
                (itemNon: any) => {
                  if (itemNon.fromQuantity === 1) {
                    data.push({
                      quantity: itemNon?.toQuantity || 1,
                      gift: true,
                      product_id: itemNon?.toProductId,
                      gift_condition_product_id:
                        itemNon?.toProductId ===
                        dataProductGift.value[0]?.productId
                          ? ""
                          : dataProductGift.value[0]?.productId,
                      gift_campaign_id: dataProductGift.value[0]?.campaignId,
                      gift_campaign_action_id:
                        dataProductGift.value[0]?.campaignActionId,
                    });
                    console.log("data sau", data);
                  }
                }
              );
              console.log("data1", data);
            }
          }
          await addOrderLineItems(orderId, data);
        }
      } else {
        // th có 2 chiến dịch
        isOpenGiftProduct.value = true;
        return;
        // mặc định cho chọn sản phẩm không tự thêm
      }
    }
  };
  // Hàm xử lý sản phẩm đã tồn tại
  const handleExistingProduct = async (product: any, productAdd: any) => {
    if (product.orderLineItem?.orderStatus === 90) {
      await enableProductDiary(orderDetail.value.id, product?.id);
    }
    if (product?.orderLineItem?.variant?.price?.amount !== productAdd?.price) {
      await updatePriceInOrder(
        orderDetail.value?.id,
        product?.id,
        productAdd?.price,
        ""
      );
    }
    const newQuantity = product?.orderLineItem?.currentQuantity + 1;
    await handleSearchProductGift(productAdd);
    await handleProductGift(productAdd, orderDetail.value?.id, product?.id);
    await updateQuantity(product.id, newQuantity);
    handleGetProductDiary();
    // getOrderById(orderDetail.value.id);
    handleGetProductDiary();
  };
  // hàm thêm sản phẩm quà tặng vào đơn
  const addProductGiftToOrder = async (
    orderId: string,
    lineItems: any[],
    fromQuantity: number
  ) => {
    // kiểm tra số lượng có được thêm vào hay không
    const res = orderDetail.value?.activeOrderItemProfiles.find((item: any) => {
      return item.orderLineItem?.id === lineItems[0]?.gift_condition_product_id;
    });

    // nếu có trong đơn
    if (res) {
      if (res?.orderLineItem?.currentQuantity < fromQuantity) {
        app.$toast.warning(
          "Chưa đủ sản phẩm điều kiện, không thể thêm sản phẩm quà tặng"
        );
        return;
      }

      await addOrderLineItems(orderId, lineItems);
    }
  };
  //
  const getCustomerId = async (id: any) => {
    try {
      const customer = await getCustomerById(id);
      customerInOrder.value = customer;
    } catch (error) {
      console.error("Error fetching customer info:", error);
      throw error;
    }
  };

  const addCustomerToOrder = async (customer: any, id: string) => {
    if (orderDetail.value?.status === "CANCELLED") {
      app.$toast.error(
        "Đơn hàng đang trạng thái hủy không thể thêm khách hàng vào"
      );
      return;
    }
    if (orderDetail.value) {
      try {
        const response = await updateOrderCustomer(
          orderDetail.value.id,
          customer?.id,
          id
        );
        await Promise.all([
          getDataShippingAddress(customer.id),
          handleGetCampaignActiveNow("", customer?.id),
        ]);
        await getOrderById(orderDetail.value.id);
      } catch (error) {
        console.error("Error adding customer to order:", error);
        throw error;
      }
    } else {
      // flow k có detail order -> tạo đơn nháp -> updateStatus, thêm khách hàng
      const response = await createOrderTemp({
        status: "DRAFT",
        orderType: "POS_SALE",
        platform: "WEB",
        // source: "cart",
        time: new Date().getTime(),
        warehouseId: warehouseId,
      });
      const auth = useCookie("auth").value as unknown as Auth;

      await updateOrderCustomer(response.data.orderId, customer?.id, id);
      await Promise.all([
        updateEmployee(response.data.orderId, auth?.user?.id, auth?.user?.id),
        getDataShippingAddress(customer.id),
        handleGetCampaignActiveNow("", customer?.id),
        updateStatusOpen(response.data.orderId),
      ]);

      const routee = useRoute();

      if (listOrder.value) {
        handleChangeOrderId(
          routee.query.orderId as string,
          response.data.orderId
        );
      }
      router.push({
        query: {
          ...router.currentRoute.value.query,
          orderId: response.data.orderId,
        },
      });
      // await getOrderById(response.data.orderId);
    }
  };
  const clearCustomer = async () => {
    customerInOrder.value = null;
    dataDefaultAddress.value = null;
    if (orderDetail.value.order?.shippingAddress) {
      await Promise.allSettled([
        removeShippingAddress(orderDetail.value?.id, ""),
        removeShippingInfo(orderDetail.value?.id, ""),
        updateShippingFee(orderDetail.value?.id, 0),
      ]);
      await updateOrder(orderDetail.value.id);
    }

    orderDetail.value.order.shippingAddress = null;
    //
  };
  const updateDescriptionToOder = async (description: string) => {
    try {
      await updateOrderDescription(orderDetail.value.id, description);
      getOrderById(orderDetail.value.id);
    } catch (error) {
      console.error("Error updating order description:", error);
      throw error;
    }
  };

  const logSuccess = (product: any, response: any) => {
    writeLog({
      timestamp: new Date(),
      user_id: user.value?.id,
      action: `Thêm sản phẩm ${product.productId} vào giỏ hàng`,
      status: "success",
      data: product,
      output: response,
      org_id: orgId.value,
      store_id: storeId.value,
      module_from: "WEB_POS",
      module_to: "orderService",
      type: "ADD",
    });
  };

  // Hàm ghi log khi có lỗi
  const logError = (product: any, error: any) => {
    writeLog({
      timestamp: new Date(),
      user_id: user.value?.id,
      action: `Lỗi khi thêm sản phẩm ${product.productId} vào giỏ hàng`,
      status: "error",
      data: product,
      output: error,
      org_id: orgId.value,
      store_id: storeId.value,
      module_from: "WEB_POS",
      module_to: "orderService",
      type: "ADD",
    });
  };
  const getDataShippingAddress = async (ownerId: string) => {
    dataShippingAddress.value = [];
    try {
      const response = await getShippingInfo(ownerId);
      dataShippingAddress.value = response.data;

      dataDefaultAddress.value = dataShippingAddress.value?.find(
        (item: any) => item.address_default === true
      );
    } catch (error) {
      throw error;
    }
  };
  const createShippingAddress = async (
    ownerId: string,
    dataRequest: ShippingAddress
  ) => {
    try {
      const response = await createShippingInfo(ownerId, dataRequest);
      await getDataShippingAddress(ownerId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateShippingAddress = async (
    ownerId: string,
    receiverId: string,
    updatedBy: string,
    dataRequest: ShippingAddress
  ) => {
    try {
      const response = await updateShippingInfo(
        ownerId,
        receiverId,
        updatedBy,
        dataRequest
      );
      if (response.status === 1) {
        app.$toast.success(`Cập nhật địa chỉ giao hàng thành công`);
      } else {
        app.$toast.warning(`Cập nhật địa chỉ giao hàng thất bại`);
      }
      console.log("response", response);
      await getDataShippingAddress(ownerId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const deleteShippingAddress = async (
    ownerId: string,
    receiverId: string,
    deletedBy: string
  ) => {
    try {
      const response = await deleteShippingInfo(ownerId, receiverId, deletedBy);
      await getDataShippingAddress(ownerId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getShippingCarrier = async () => {
    try {
      const response = await getListShippingCarrier();
      dataShippingCarrier.value = response.data;
    } catch (error) {
      throw error;
    }
  };
  const handleDataShippingCarrier = async (ShippingCarrier: string) => {
    idShippingCarrier.value = ShippingCarrier;
    await getShippingServices(idShippingCarrier.value);
  };
  const getShippingServices = async (shippingCarrierId: string) => {
    try {
      const response = await getShippingService(shippingCarrierId);
      dataShippingServices.value = response.data;
      return response;
    } catch (error) {
      throw error;
    }
  };
  const handleShippingService = (shippingService: string) => {
    idShippingService.value = shippingService;
  };
  const handleUpdateSaleEmployee = async (
    orderId: string,
    saleId: string,
    updatedBy: string
  ) => {
    try {
      await updateSaleEmployee(orderId, saleId, updatedBy);
      await getOrderById(orderId);
    } catch (error) {
      throw error;
    }
  };
  const handleUpdateWarehouse = async (
    orderId: string,
    warehouseId: string
  ) => {
    try {
      const response = await updateWareHouseToOrder(orderId, warehouseId);
      if (response.status === 1) {
        app.$toast.success(`${response?.message}`);
      } else {
        app.$toast.warning(`${response?.message}`);
      }
    } catch (error) {
      throw error;
    }
  };
  //
  const updateStatusOrderDetail = (reason: string) => {
    orderDetail.value.status = "CANCELLED";
    orderDetail.value.statusDescription = "Đã hủy";
    orderDetail.value.order.note = reason;
    console.log("orderDetail", orderDetail.value);
  };
  const updateQuantityPrintOrder = () => {
    console.log(
      "orderDetail.value",
      orderDetail.value?.order?.customAttributes
    );

    const res = orderDetail.value?.order?.customAttributes?.find(
      (item: any) => item?.key === "printTimes"
    );

    if (res) {
      res.value = (parseInt(res.value) || 0) + 1;
    } else {
      orderDetail.value?.order?.customAttributes?.push({
        key: "printTimes",
        value: "1",
      });
    }
  };
  const setShippingAddressInOrderDetail = () => {
    orderDetail.value.order.shippingAddress = null;
  };
  return {
    customerInOrder,
    orderDetail,
    isLoadingOrder,
    disabledProducts,
    getOrderById,
    addProductToOrder,
    updateQuantity,
    createOrder,
    addCustomerToOrder,
    clearCustomer,
    updateDescriptionToOder,
    toggleDisabledProduct,
    handleDisibleOrderItems,
    createOrderFromDiary,
    handlePlaceOrder,
    getDataShippingAddress,
    dataShippingAddress,
    createShippingAddress,
    updateShippingAddress,
    deleteShippingAddress,
    dataDefaultAddress,
    valueShippingAddress,
    isAlert,
    handleRemoveProduct,
    handleContinueCreateOrder,
    handleCreateOrderWithInventory,
    dataInventory,
    getCustomerId,
    getShippingCarrier,
    getShippingServices,
    handleDataShippingCarrier,
    handleShippingService,
    dataShippingCarrier,
    dataShippingServices,
    shippingFee,
    isLoadingPayment,
    getCampaignActionActiveNow,
    campaign,
    handleUpdateSaleEmployee,
    handleUpdateWarehouse,
    getOrderByIdV2,
    paymentAmount,
    handleCheckInventory,
    dataDynamicForm,
    getListOrder,
    listOrder,
    loading,
    isNotDraft,
    loadingCreateOrder,
    dataListProductDiary,
    campaignLevel,
    handleGetCampaignActiveNow,
    handleAddInventory,
    handleCreateBuyOrder,
    getBuyOrderById,
    updateStatusOrderDetail,
    updateQuantityPrintOrder,
    handleAddOrderToListOrder,
    updateOrder,
    setShippingAddressInOrderDetail,
    idShippingCarrier,
    idShippingService,
    isOpenShippingAddress,
    isAlertWareHouse,
    handleGetInvoiceOfOrder,
    dataInvoice,
    isOpenGiftProduct,
    dataProductGift,
    addProductGiftToOrder,
  };
});
