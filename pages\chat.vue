<template>
  <div class="'h-screen w-full md:flex antialiased bg-white overflow-y-auto'">
    <div class="md:flex-1 md:flex flex-col h-screen overflow-y-auto">
      <main class="md:flex-grow md:flex md:flex-row md:min-h-0 overflow-y-auto">
        <SidebarChat
          v-if="!isNavigateRoomMobile"
          :userLogin="userLogin"
          :topics="topics"
          :selectedTopicId="selectedRoom?.id"
          :hasMoreData="hasMoreData"
          @select-topic="navigateToRoom"
          @search="handleSeach"
          @clear-search="clearKeyWord"
          @set-status="handleSetStatus"
          @scroll="handleScroll"
          @change-app-id="
            (newId) => {
              selectedAppId = newId;
              handleChangeAppId();
            }
          "
        />
        <div class="md:flex-grow md:flex md:flex-col">
          <!-- Chat desktop -->
          <div class="md:block hidden">
            <ChatDetail
              v-if="isJoinRoom && matrixToken"
              :selectedRoom="selectedRoom"
              @closeTopic="handleCloseTopic"
              @addAccountableId="addAccountableId"
            />
            <!-- Chat First Detail -->
            <ChatFirstDetail
              v-if="!isJoinRoom"
              :selectedRoom="selectedRoom"
              @joinRoom="handleJoinRoom"
              @closeTopicFirst="handleCloseTopicFirst"
            ></ChatFirstDetail>
          </div>

          <div v-if="isNavigateRoomMobile" class="block md:hidden">
            <ChatDetail
              v-if="isJoinRoom && matrixToken"
              :selectedRoom="selectedRoom"
              @closeTopic="handleCloseTopic"
              @back="handleClickBack"
            />
            <ChatFirstDetail
              v-if="!isJoinRoom"
              :selectedRoom="selectedRoom"
              @joinRoom="handleJoinRoom"
              @back="handleClickBack"
              @closeTopicFirst="handleCloseTopicFirst"
            ></ChatFirstDetail>
          </div>
        </div>
        <ModalSetTokenMatrix :isOpen="showTokenModal" @close="closeModal" />
      </main>
    </div>
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
    <!-- Create Topic Dialog -->
    <CreateTopicDialog
      v-if="isCreateTopicPopup"
      @cancel="ToogleCreateTopicPopup"
      @confirm="handleCreateTopic"
    ></CreateTopicDialog>
  </div>
</template>

<script setup>
const CreateTopicDialog = defineAsyncComponent(() =>
  import("~/components/Chat/CreateTopicDialog.vue")
);
const LoadingSpinner = defineAsyncComponent(() =>
  import("~/components/common/LoadingSpinner.vue")
);
const ChatFirstDetail = defineAsyncComponent(() =>
  import("~/components/Chat/ChatFirstDetail.vue")
);
const ChatDetail = defineAsyncComponent(() =>
  import("~/components/Chat/ChatDetail.vue")
);
const SidebarChat = defineAsyncComponent(() =>
  import("~/components/Chat/SidebarChat.vue")
);
useHead({
  title: "Trao đổi khách hàng",
  meta: [
    {
      name: "description",
      content: "Trao đổi khách hàng",
    },
  ],
});
definePageMeta({
  layout: "dashboard",
  name: "Chat",
});

const { getCustomerById } = useCustomer();
const route = useRoute();
const router = useRouter();
let matrixToken =
  typeof window !== "undefined" ? localStorage.getItem("matrixToken") : null;

const customerData = ref(null);
const topics = ref([]);
const isNavigateRoomMobile = ref(false);

const showTokenModal = ref(false);

const fetchCustomerData = async (customerId) => {
  if (customerId && !customerId.includes("customerId")) {
    try {
      customerData.value = await getCustomerById(customerId);
    } catch (error) {
      console.error("Error fetching customer by ID:", error);
    }
  }
};

const selectedRoom = ref();
const navigateToRoom = (room) => {
  selectedRoom.value = room;
  //
  const res = handleGetIdUserMatrix();

  if (
    res === selectedRoom.value?.accountableId &&
    selectedRoom.value?.status !== "CLOSED"
  ) {
    isJoinRoom.value = true;
  } else {
    isJoinRoom.value = false;
  }

  const query = {
    ...route.query,
    roomId: room.roomId,
    threadId: room.threadId,
    topicId: room.id,
  };
  if (isMobile.value) {
    isNavigateRoomMobile.value = true;
  }
  if (room.customerId) {
    query.customerId = room.customerId;
  }
  // if (!matrixToken) {
  //   showTokenModal.value = true;
  // }

  router.push({ path: route.path, query });
};
const closeModal = () => {
  showTokenModal.value = false;
};
const userLogin = ref();
const handleGetInfoUserLogin = async () => {
  const matrixUserId = localStorage.getItem("matrixUserId") || "";

  if (!matrixUserId) {
    console.warn("No matrixUserId found in localStorage.");
    return;
  }

  // Dùng RegEx để tìm phần chuỗi giữa @ và :
  const userId = matrixUserId.match(/@([^:]+):/);

  if (userId && userId[1]) {
    try {
      userLogin.value = await getCustomerById(userId[1]);
    } catch (error) {
      throw error;
    }
  } else {
    return null;
  }
};
///
const { getTopics, closeTopic } = useChat();
const filterTopicRequest = reactive({
  keyword: "",
  accountableId: "",
  status: "OPENED",
  communicationChannel: "",
  getNotAccountable: true,
});
const handleSeach = async (keyword) => {
  filterTopicRequest.keyword = keyword;
  await handleCheckRoleSearch();
  if (!isMobile.value) {
    if (topics.value.length > 0) {
      navigateToRoom(topics.value[0]);
    }
  }
  hasMoreData.value = true;
};
const clearKeyWord = async () => {
  keyword.value = "";
  filterTopicRequest.keyword = "";
  await handleCheckRoleSearch();
  if (!isMobile.value) {
    if (topics.value.length > 0) {
      navigateToRoom(topics.value[0]);
    }
  }
  hasMoreData.value = true;
};
const updateTopicsSearch = async (userId) => {
  filterTopicRequest.accountableId = userId;
  try {
    topics.value = await getTopics(filterTopicRequest, 10, 1);
  } catch (error) {
    throw error;
  }
};
//
const isLoading = ref(false);
const handleCheckRoleSearch = async () => {
  // kiểm tra role rồi truyền query tương ứng
  const auth = useCookie("auth").value;
  const isAdmin = auth?.user?.roles.find(
    (role) => role === "SALE_MANAGER" || role === "ORG_ADMIN"
  );

  let loadingTimeout = null;

  // Chỉ hiển thị loading nếu vượt quá 200ms
  loadingTimeout = setTimeout(() => {
    isLoading.value = true;
  }, 200);

  if (isAdmin) {
    await updateTopicsSearch("");
  } else {
    if (filterTopicRequest.status === "OPENED") {
      filterTopicRequest.getNotAccountable = true;
    } else {
      filterTopicRequest.getNotAccountable = false;
    }
    await updateTopicsSearch(auth?.user?.id);
  }

  // Hủy timeout và đặt isLoading về false khi hoàn thành
  clearTimeout(loadingTimeout);
  isLoading.value = false;
};

const updateTopics = async (userId) => {
  filterTopicRequest.accountableId = userId;
  try {
    const response = await getTopics(filterTopicRequest, 10, 1);
    if (response) {
      const res = response.filter(
        (topicA) => !topics.value.some((topicB) => topicB.id === topicA.id)
      );
      if (res) {
        addTopicToListTopic(res);
      }
    }
  } catch (error) {
    throw error;
  }
};
//
const handleCheckRoleFirst = async () => {
  // kiểm tra role rồi truyền query tương ứng
  const auth = useCookie("auth").value;
  const isAdmin = auth?.user?.roles.find(
    (role) => role === "SALE_MANAGER" || role === "ORG_ADMIN"
  );
  if (isAdmin) {
    await updateTopicsFirst("");
  } else {
    await updateTopicsFirst(auth?.user?.id);
  }
};
//
const updateTopicsFirst = async (userId) => {
  filterTopicRequest.accountableId = userId;
  try {
    const response = await getTopics(filterTopicRequest, 10, 1);
    if (response) {
      const res = response.filter(
        (topicA) => !topics.value.some((topicB) => topicB.id === topicA.id)
      );
      if (res) {
        addTopicToHeadListTopic(res);
      }
    }
  } catch (error) {
    throw error;
  }
};
//

const handleCheckRole = async () => {
  // kiểm tra role rồi truyền query tương ứng
  const auth = useCookie("auth").value;
  const isAdmin = auth?.user?.roles.find(
    (role) => role === "SALE_MANAGER" || role === "ORG_ADMIN"
  );
  if (isAdmin) {
    await updateTopics("");
  } else {
    await updateTopics(auth?.user?.id);
  }
};
const listAppId = ref();
const selectedAppId = ref();
const isRoleAdmin = ref(false);
const handleCheckRoleUI = () => {
  const auth = useCookie("auth").value;
  const isAdmin = auth?.user?.roles.find(
    (role) => role === "SALE_MANAGER" || role === "ORG_ADMIN"
  );
  if (isAdmin) {
    isRoleAdmin.value = true;
  } else {
    isRoleAdmin.value = false;
  }
};
const handleGetIdUserMatrix = () => {
  if (localStorage.getItem("matrixUserId")) {
    const extractedId = localStorage
      .getItem("matrixUserId")
      .match(/(?<=@)[^:]+/);
    return extractedId[0];
  }
};
onMounted(async () => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");
  listAppId.value = appId;
  const zaloApp = listAppId.value.find((app) => app?.name === "ZALO MESSAGE");
  if (zaloApp) {
    selectedAppId.value = zaloApp?.id;
  } else {
    selectedAppId.value = listAppId.value[0]?.id;
  }
  filterTopicRequest.communicationChannel = selectedAppId.value;
  await checkMobile();
  window.addEventListener("resize", checkMobile);
  let matrixToken =
    typeof window !== "undefined" ? localStorage.getItem("matrixToken") : null;

  handleCheckRoleUI();
  const customerId = route.query.customerId;
  await fetchCustomerData(customerId);
  await handleCheckRole();
  //
  await handleGetInfoUserLogin();

  const interval = setInterval(handleCheckRoleFirst, 3000);

  // nếu có topic và có danh sách topic
  if (route.query.roomId && topics.value.length > 0) {
    const res = topics.value.find((topic) => topic.id === route.query.topicId);
    if (res) {
      selectedRoom.value = res;
      if (isMobile.value) {
        isNavigateRoomMobile.value = true;
      }
    }
  }
  if (!isMobile.value) {
    //  nếu chưa có phòng và topic thì lấy topic đầu tiên trong danh sách topic
    if (!route.query.roomId && topics.value.length > 0) {
      selectedRoom.value = topics.value[0];
      if (isMobile.value) {
        isNavigateRoomMobile.value = true;
      }
      router.push({
        path: router.currentRoute.value.path,
        query: {
          ...router.currentRoute.value.query,
          roomId: topics.value[0].roomId,
          threadId: topics.value[0].threadId,
          topicId: topics.value[0].id,
          customerId: topics.value[0].customerId || null,
        },
      });
    }
  }
  //
  if (matrixToken) {
    const res = handleGetIdUserMatrix();
    if (
      res === selectedRoom.value?.accountableId &&
      selectedRoom.value?.status !== "CLOSED"
    ) {
      isJoinRoom.value = true;
    }
    //
  }
  onUnmounted(() => {
    window.removeEventListener("resize", checkMobile);
    clearInterval(interval);
  });
});

const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};
watch(
  () => route.query.roomId,
  async (newRoomId) => {
    if (!isMobile.value) {
      if (!newRoomId && topics.value.length > 0) {
        // navigateToRoom(topics.value[0]);
      } else {
        const newCustomerId = route.query.customerId;
        await fetchCustomerData(newCustomerId);
      }
    } else {
      const newCustomerId = route.query.customerId;
      await fetchCustomerData(newCustomerId);
    }
  }
);
watch(
  () => route.query.topicId,
  async (newTopicId) => {
    if (!newTopicId) {
      if (!isMobile.value) {
        navigateToRoom(topics.value[0]);
      } else {
        isNavigateRoomMobile.value = false;
      }
    }
  }
);
///
router.beforeEach(async (to, from, next) => {
  if (to.from === `/chat`) {
    window.removeEventListener("resize", checkMobile);
    clearInterval(interval);
  }
  next();
});
//
const isCreateTopicPopup = ref(false);
const ToogleCreateTopicPopup = () => {
  isCreateTopicPopup.value = !isCreateTopicPopup.value;
};
// tạo topic
const { createTopic } = useCustomer();

const handleCreateTopic = async (nameTopic, appId, customer) => {
  // gọi hàm tạo topic -> get lại danh sách -> select qua cái topic đó
  const res = await createTopic(appId, customer?.id, nameTopic);
  // đóng popup'
  ToogleCreateTopicPopup();
};
const isJoinRoom = ref(false);
const handleJoinRoom = async () => {
  let matrixToken =
    typeof window !== "undefined" ? localStorage.getItem("matrixToken") : null;
  if (!matrixToken) {
    showTokenModal.value = true;
    return;
  }
  isJoinRoom.value = true;
};
const handleChangeAppId = async () => {
  console.log("selectedAppId.value", selectedAppId.value);
  filterTopicRequest.communicationChannel = selectedAppId.value;
  await handleCheckRoleSearch();
  if (!isMobile.value) {
    if (topics.value.length > 0) {
      navigateToRoom(topics.value[0]);
    } else {
      router.push({
        path: router.currentRoute.value.path,
        query: {
          orgId: route.query.orgId,
          storeId: route.query.storeId,
        },
      });
    }
  }
  hasMoreData.value = true;
};
//

let hasMoreData = ref(true);

const option = reactive({
  pageSize: 10,
  pageNumber: 2,
});

const searchTopic = async (userId) => {
  filterTopicRequest.accountableId = userId;
  try {
    const response = await getTopics(
      filterTopicRequest,
      option.pageSize,
      option.pageNumber
    );
    return response;
  } catch (error) {
    throw error;
  }
};

const handleScroll = async () => {
  // Kiểm tra role rồi truyền query tương ứng
  const auth = useCookie("auth").value;
  const isAdmin = auth?.user?.roles.find(
    (role) => role === "SALE_MANAGER" || role === "ORG_ADMIN"
  );

  let response;
  if (isAdmin) {
    response = await searchTopic("");
  } else {
    response = await searchTopic(auth?.user?.id);
  }
  //
  if (response && response.length > 0) {
    const res = response.filter((topic) => !topics.value.includes(topic));
    if (res) {
      addTopicToListTopic(res);
    }
    option.pageNumber += 1;
  } else {
    hasMoreData.value = false;
  }
};

const addTopicToListTopic = (ListTopic) => {
  ListTopic?.forEach((topic) => {
    topics.value.push(topic);
  });
};
const addTopicToHeadListTopic = (ListTopic) => {
  ListTopic?.forEach((topic) => {
    topics.value.unshift(topic);
  });
};
//
const handleCloseTopic = async () => {
  try {
    const response = await closeTopic(selectedRoom.value?.id);
    await handleCheckRoleSearch();
    if (topics.value.length > 0) {
      const nextTopic = topics.value[0];
      selectedRoom.value = topics.value[0];
      router.push({
        path: router.currentRoute.value.path,
        query: {
          ...router.currentRoute.value.query,
          roomId: nextTopic.roomId,
          threadId: nextTopic.threadId,
          topicId: nextTopic.id,
          customerId: nextTopic.customerId || null,
        },
      });
    } else {
      router.push({
        path: router.currentRoute.value.path,
        query: {
          orgId: route.query.orgId,
          storeId: route.query.storeId,
        },
      });
      selectedRoom.value = null;
    }
  } catch (error) {
    throw error;
  }
};
//
const selectedStatus = ref("OPENED");
const handleSetStatus = async (status) => {
  // if(status ==='OPENED'){

  // }
  selectedStatus.value = status;
  filterTopicRequest.status = status;
  topics.value = [];
  await handleCheckRoleSearch();
  if (!isMobile.value) {
    if (topics.value.length > 0) {
      navigateToRoom(topics.value[0]);
    }
  }
  hasMoreData.value = true;
};
const handleClickBack = () => {
  isNavigateRoomMobile.value = false;
  router.push({
    path: router.currentRoute.value.path,
    query: {
      ...router.currentRoute.value.query,
      roomId: undefined,
      threadId: undefined,
      topicId: undefined,
      customerId: undefined,
    },
  });
};
const handleCloseTopicFirst = async () => {
  try {
    const response = await closeTopic(selectedRoom.value?.id);
    await handleCheckRoleSearch();
    if (!isMobile.value) {
      if (topics.value.length > 0) {
        const nextTopic = topics.value[0];
        selectedRoom.value = topics.value[0];
        router.push({
          path: router.currentRoute.value.path,
          query: {
            ...router.currentRoute.value.query,
            roomId: nextTopic.roomId,
            threadId: nextTopic.threadId,
            topicId: nextTopic.id,
            customerId: nextTopic.customerId || null,
          },
        });
      } else {
        router.push({
          path: router.currentRoute.value.path,
          query: {
            orgId: route.query.orgId,
            storeId: route.query.storeId,
          },
        });
      }
    } else {
      handleClickBack();
    }
  } catch (error) {
    throw error;
  }
};
const addAccountableId = async () => {
  const response = await getTopics(filterTopicRequest, 10, 1);
  if (response) {
    const res = response.find((topic) => topic?.id === selectedRoom.value?.id);
    const resTopicIndex = topics.value.findIndex(
      (topic) => topic?.id === res?.id
    );
    const auth = useCookie("auth").value;

    const idSale = handleGetIdUserMatrix();
    if (resTopicIndex !== -1) {
      // Gán giá trị mới cho phần tử tương ứng
      topics.value[resTopicIndex] = {
        ...topics.value[resTopicIndex],
        accountableId: idSale,
        accountableName: auth?.user?.name,
      };
    }
  }
};
</script>

<style scoped>
.messages {
  transition: all 0.3s ease;
  padding: 16px;
  border-radius: 12px;
  max-width: 80%;
}

.messages:hover {
  background-color: #edf2f7;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.06);
}

.chat-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px;
}

.thread-container {
  padding: 12px;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid #ddd;
}

.chat-footer {
  background-color: #f8fafc;
  padding: 16px;
  border-top: 1px solid #e2e8f0;
}

input.input-message {
  border-radius: 24px;
  padding: 10px 16px;
  border: 1px solid #cbd5e1;
  background-color: #f9fafb;
  transition: border 0.3s ease;
}

input.input-message:focus {
  border-color: #3182ce;
  background-color: #edf2f7;
}

.contacts {
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}
.bg-blue-100 {
  background-color: #ebf8ff;
}

.bg-gray-100 {
  background-color: #f7fafc;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.avatar {
  border-radius: 50%;
  overflow: hidden;
}

button {
  background-color: #3182ce;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #2b6cb0;
}
@media (max-width: 768px) {
  .chat-body,
  .chat-footer,
  .chat-header {
    display: none;
  }

  .is-room-selected .chat-body,
  .is-room-selected .chat-footer,
  .is-room-selected .chat-header {
    display: block;
  }

  .group {
    width: 100%; /* Full width room list for mobile */
  }

  .is-room-selected .group {
    display: none; /* Hide room list once a chat is selected */
  }

  .back-button {
    display: block;
    cursor: pointer;
    padding-right: 16px;
  }

  input.input-message {
    padding: 10px 12px;
    font-size: 16px;
  }

  button {
    padding: 10px 14px;
    font-size: 14px;
  }

  .thread-section {
    width: 100%;
    position: fixed;
    bottom: 0;
    z-index: 50;
    background-color: white;
    padding: 20px;
  }

  .room-list {
    display: none;
  }

  .contact-list {
    display: block;
  }

  /* Ensure proper spacing between messages */
  .messages {
    max-width: 90%; /* Better readability on mobile */
    margin-bottom: 10px;
  }

  .chat-footer {
    padding: 10px 20px; /* Larger padding for better touch experience */
  }
}

.thread-section {
  position: fixed;
  top: 0;
  right: 0;
  background-color: #f9fafb;
  height: 100%;
  width: 33%;
  z-index: 50;
  transition: transform 0.3s ease;
}
</style>
