export const validateName = (name: string): string => {
  const nameRegex = /^[a-zA-Z\s]+$/;
  if (!name) {
    return "Không để trống tên.";
  }
  if (!nameRegex.test(name)) {
    return "Họ và tên không được chứa ký tự đặc biệt.";
  }
  return "";
};

export const validatePhone = (phone: string): string => {
  // Vietnamese phone number: exactly 10 digits, starts with 0, valid prefixes
  const phoneRegex = /^0[3|5|7|8|9][0-9]{8}$/;
  if (!phoneRegex.test(phone)) {
    return "Số điện thoại không hợp lệ. Vui lòng nhập số điện thoại Việt Nam 10 số (VD: 0901234567)";
  }
  return "";
};

// Validate address
export const validateAddress = (address: string): string => {
  if (!address) {
    return "Không để trống địa chỉ.";
  }
  return "";
};

export const validateBirthDate = (birthDate: string): string => {
  if (!birthDate) {
    return "Không để trống ngày sinh.";
  }
  return "";
};

export const validateEmail = (email: string): string => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email pattern
  if (email && !emailRegex.test(email)) {
    return "Email không hợp lệ. Vui lòng nhập lại.";
  }
  return "";
};
