import pkg from "./package.json";
import { createResolver } from "@nuxt/kit";

const { resolve } = createResolver(import.meta.url);
export default defineNuxtConfig({
  //enable ssr
  ssr: false,

  app: {
    head: {
      titleTemplate: `%s | ${process.env.SITE_TITLE ?? "POS System"}`,
      htmlAttrs: { lang: "vi" },
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1" },
        { name: "description", content: "Hệ thống POS tối ưu cho bán hàng" },
      ],
      link: [{ rel: "icon", href: "/favicon.png", type: "image/svg+xml" }],
    },

    // Optimized page transitions
    pageTransition: false,
  },

  components: [{ path: resolve("./components"), pathPrefix: false }],

  modules: [
    "@nuxtjs/tailwindcss",
    "nuxt-icon",
    "@nuxt/image",
    "@vueuse/nuxt",
    "@pinia/nuxt",
    "@pinia-plugin-persistedstate/nuxt",
    "nuxt-swiper",
    "@vite-pwa/nuxt",
  ],
  pwa: {
    mode: "development",
    strategies: "generateSW",
    registerType: "autoUpdate",
    injectRegister: "auto",
    manifest: {
      name: "SalePoint",
      short_name: "SalePoint",
      description: "salePoint",
      icons: [
        {
          src: "/images/1024.png",
          sizes: "64x64",
          type: "image/png",
        },
        {
          src: "/images/1024.png",
          sizes: "144x144",
          type: "image/png",
        },
        {
          src: "/images/1024.png",
          sizes: "512x512",
          type: "image/png",
        },
      ],
    },
    workbox: {
      globPatterns: ["**/*.{js,css,html,png,svg,ico}"],
      maximumFileSizeToCacheInBytes: 10 * 1024 * 1024, // S
    },
    client: {
      installPrompt: true,
    },
    devOptions: {
      enabled: true,
      suppressWarnings: true,
      navigateFallback: "/",
      navigateFallbackAllowlist: [/^\/$/],
    },
  },

  runtimeConfig: {
    public: {
      version: pkg.version || "0.0.0",
      LOGO: "/images/logo-lvs.png",
      ENV_NAME: process.env.ENV_NAME,
      MATRIX_SSO: process.env.MATRIX_SSO,
      MATRIX_LINK: process.env.MATRIX_LINK,
    },
  },

  css: ["/assets/css/main.css"],
  plugins: [
    { src: "~/plugins/sdk.ts" }, // chạy cả server + client
    { src: "~/plugins/toastify.ts", mode: "client" },
    { src: "~/plugins/tippy.ts", mode: "client" },
    { src: "~/plugins/matrix.client.ts", mode: "client" }, // nếu chỉ dùng cho UI chat
    { src: "~/plugins/onboarding-preloader.client.ts", mode: "client" }, // onboarding performance optimization
  ],
  devtools: { enabled: true },
  vite: {
    define: {
      global: "window",
    },

    build: {
      minify: "esbuild",
      rollupOptions: {
        output: {
          manualChunks: {
            // Core vendor libraries
            'vendor-core': ['vue', 'pinia', 'nuxt'],

            // UI libraries
            'vendor-ui': ['@headlessui/vue', 'tailwindcss'],

            // Business logic
            'business-logic': [
              './composables/useAuth',
              './composables/useRegister',
              './composables/useOrganization'
            ],

            // Onboarding system
            'onboarding-core': [
              './composables/useOnboarding',
              './config/onboarding-modules'
            ],

            // Onboarding components
            'onboarding-components': [
              './components/onboarding/common/ProgressBar.vue',
              './components/onboarding/common/StepperNavigation.vue',
              './components/onboarding/common/OnboardingLayout.vue'
            ],

            // Module components (simplified)
            'onboarding-modules': [
              './components/onboarding/modules/organization/CreateOrganization.vue',
              './components/onboarding/modules/pos/CreateStore.vue',
              './components/onboarding/modules/crm/ImportCustomers.vue'
            ]
          },
        },
      },
    },
  },
  // Build optimization
  nitro: {
    compressPublicAssets: true,
    minify: true,
    experimental: {
      wasm: true,
    },
    // Prerender onboarding pages for better performance
    prerender: {
      routes: ['/onboarding', '/onboarding/complete']
    },
    // Enable compression
    compression: 'gzip',
  },

  // Experimental features for performance
  experimental: {
    // Enable payload extraction for better caching
    payloadExtraction: false,
    // Enable inline route rules
    inlineSSRStyles: false,
  },

  // Route rules for caching and performance
  routeRules: {
    // Homepage pre-rendered at build time
    '/': { prerender: true },

    // Login page cached for 1 hour
    '/login': { headers: { 'cache-control': 's-maxage=3600' } },

    // Registration pages cached for 30 minutes
    '/register/**': { headers: { 'cache-control': 's-maxage=1800' } },

    // Onboarding pages - SPA mode for better interactivity
    '/onboarding/**': { ssr: false },

    // API routes cached for 5 minutes
    '/api/**': { headers: { 'cache-control': 's-maxage=300' } },

    // Static assets cached for 1 year
    '/images/**': { headers: { 'cache-control': 's-maxage=31536000' } },
    '/icons/**': { headers: { 'cache-control': 's-maxage=31536000' } },
  },
  image: {
    format: ["webp", "avif"],
  },
});
