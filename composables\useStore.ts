export default function useCheckin() {
  const $sdk = useNuxtApp().$sdk;
  const getStore = async () => {
    try {
      const response = await $sdk.product.getStores("pos");
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getDetailStore = async () => {
    try {
      const response = await $sdk.product.getDetailStores();
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getStoreChannelIdsByEmployeeId = async (employeeId: string) => {
    try {
      const response = await $sdk.user.getStoreChannelIdsByEmployeeId(
        employeeId
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getDetailStoreV2 = async (storeId: any) => {
    try {
      const response = await $sdk.product.getDetailStoresV2(storeId);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const getAvailableStoreChannels = async (userId: string) => {
    try {
      // Ensure orgId is set before API call
      if (!$sdk.orgId || $sdk.orgId === "N/A") {
        console.log(
          "🔧 Setting default orgId for getAvailableStoreChannels..."
        );
        $sdk.setOrgId("LONGVAN");
        console.log("✅ OrgId set to:", $sdk.orgId);
      }

      const response = await $sdk.store.getAvailableStoreChannels(userId);
      return response;
    } catch (error) {
      throw error;
    }
  };

  return {
    getStore,
    getDetailStore,
    getStoreChannelIdsByEmployeeId,
    getDetailStoreV2,
    getAvailableStoreChannels,
  };
}
