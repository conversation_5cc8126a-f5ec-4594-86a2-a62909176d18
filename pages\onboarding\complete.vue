<template>
  <div>
    <OnboardingComplete
      v-if="config"
      :config="config"
    />
    
    <!-- Loading state -->
    <div 
      v-else
      class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center"
    >
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600"><PERSON>ang tải thông tin hoàn thành...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Meta
definePageMeta({
  middleware: 'auth',
  layout: false
});

// Composables
const { config, loadOnboardingConfig } = useOnboarding();
const route = useRoute();
const router = useRouter();

// Load onboarding config
onMounted(async () => {
  try {
    const userId = route.query.userId as string || useCookie('userId').value;
    
    if (!userId) {
      // Try to get from current config
      if (!config.value) {
        router.push('/login');
        return;
      }
    } else {
      await loadOnboardingConfig(userId);
    }

    // Verify onboarding is actually completed
    if (!config.value?.completedAt) {
      router.push('/onboarding');
      return;
    }
  } catch (error) {
    console.error('Error loading completion data:', error);
    router.push('/onboarding');
  }
});
</script>

<style scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
