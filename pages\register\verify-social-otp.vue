<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <div class="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-2"><PERSON><PERSON><PERSON>ệ<PERSON></h2>
          <p class="text-gray-600">
            Nh<PERSON>p mã OTP đã được gửi đến số <strong>{{ phoneNumber }}</strong>
          </p>
        </div>

        <!-- OTP Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- OTP Input -->
          <div class="space-y-2">
            <label class="block text-sm font-semibold text-gray-700 text-center">
              Mã xác thực (OTP)
            </label>
            <div class="flex justify-center space-x-3">
              <input
                v-for="(digit, index) in otpDigits"
                :key="index"
                :ref="(el) => setOtpInputRef(el as HTMLInputElement, index)"
                v-model="otpDigits[index]"
                type="text"
                maxlength="1"
                class="w-12 h-12 text-center text-lg font-semibold border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                @input="handleOtpInput(index, $event)"
                @keydown="handleOtpKeydown(index, $event)"
              />
            </div>
            <div v-if="errors.otp" class="text-sm text-red-600 text-center">
              {{ errors.otp }}
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="loading || otp.length !== 6"
            class="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <span v-if="loading">Đang xác thực...</span>
            <span v-else>Xác Thực</span>
          </button>

          <!-- Resend OTP -->
          <div class="text-center">
            <button
              type="button"
              @click="resendOTP"
              :disabled="countdown > 0"
              class="text-sm text-blue-600 hover:text-blue-800 font-medium disabled:text-gray-400"
            >
              <span v-if="countdown > 0">Gửi lại sau {{ countdown }}s</span>
              <span v-else>Gửi lại mã OTP</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Xác thực OTP - Social Login",
});

const route = useRoute();
const router = useRouter();

// Extract params
const phoneNumber = ref((route.query.phone as string) || "");
const customerName = ref((route.query.customerName as string) || "");
const accessToken = ref((route.query.accessToken as string) || "");
const registrationType = ref((route.query.type as string) || "");
const serviceType = ref((route.query.serviceType as string) || "POS");

// Form state
const otp = ref("");
const otpDigits = ref(['', '', '', '', '', '']);
const otpInputRefs = ref<HTMLInputElement[]>([]);
const loading = ref(false);
const countdown = ref(0);

// Errors
const errors = ref({
  otp: "",
});

// OTP input handling
const setOtpInputRef = (el: HTMLInputElement | null, index: number) => {
  if (el) {
    otpInputRefs.value[index] = el;
  }
};

const handleOtpInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value;

  if (!/^\d*$/.test(value)) {
    target.value = otpDigits.value[index];
    return;
  }

  otpDigits.value[index] = value;
  otp.value = otpDigits.value.join('');

  // Auto focus next input
  if (value && index < 5) {
    otpInputRefs.value[index + 1]?.focus();
  }
};

const handleOtpKeydown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    otpInputRefs.value[index - 1]?.focus();
  }
};

// Validation
const validateOTP = (otpValue: string): string => {
  if (!otpValue || otpValue.length !== 6) {
    return "Vui lòng nhập đầy đủ 6 số";
  }
  if (!/^\d{6}$/.test(otpValue)) {
    return "Mã OTP chỉ được chứa số";
  }
  return "";
};

// Submit handler
const handleSubmit = async () => {
  errors.value.otp = validateOTP(otp.value);
  
  if (errors.value.otp) {
    return;
  }
  
  loading.value = true;
  
  try {
    const $sdk = useNuxtApp().$sdk;

    // Ensure orgId context is set
    if (!$sdk.orgId || $sdk.orgId === 'N/A') {
      console.log('🔧 Setting default orgId context for social login...');
      $sdk.setOrgId('LONGVAN');
      useCookie("orgId").value = 'LONGVAN';
      console.log('✅ OrgId context set:', $sdk.orgId);
    }

    console.log('🔐 Verifying OTP for social login phone update...');
    
    // Step 1: Verify OTP
    const otpResult = await $sdk.auth.validateOTP(otp.value,phoneNumber.value);
    
    if (otpResult) {
      console.log('✅ OTP verified successfully');
      
      // Step 2: Update userDetail with phone number
      console.log('📱 Updating userDetail with phone number...');
      
      // Get current userDetail
      const currentUserDetail = await $sdk.auth.getUserDetail(accessToken.value);
      console.log('🔍 Full currentUserDetail object:', currentUserDetail);
      console.log('🔍 UserDetail structure:', {
        partyId: currentUserDetail?.partyId,
        id: currentUserDetail?.id,
        userId: currentUserDetail?.userId,
        userLoginId: currentUserDetail?.userLoginId,
        fullName: currentUserDetail?.fullName,
        phone: currentUserDetail?.phone,
        email: currentUserDetail?.email
      });

      if (currentUserDetail) {
        // Update phone in userDetail

        const updateResult = await $sdk.auth.updateInfo({
          phone: phoneNumber.value,
          fullName: customerName.value || currentUserDetail.fullName
        }, 'oauth'); // Use 'oauth' type for social login

        console.log('✅ UserDetail updated with phone:', updateResult);

        // Get updated userDetail after updateInfo
        console.log('🔄 Getting updated userDetail after updateInfo...');
        const updatedUserDetail = await $sdk.auth.getUserDetail(accessToken.value);
        console.log('📦 Updated userDetail:', updatedUserDetail);

        // Use updated userDetail for auth setup
        const finalUserDetail = updatedUserDetail || currentUserDetail;

        // Set auth state
        const authStore = useAuthStore();

        // Try different ID properties from updated userDetail
        const userId = finalUserDetail.partyId || finalUserDetail.id || finalUserDetail.userId || finalUserDetail.userLoginId;
        console.log('🆔 Extracted userId from updated userDetail:', userId);
        console.log('🔍 ID properties check (updated):', {
          partyId: finalUserDetail.partyId,
          id: finalUserDetail.id,
          userId: finalUserDetail.userId,
          userLoginId: finalUserDetail.userLoginId
        });

        if (!userId) {
          console.error('❌ No valid userId found in userDetail');
          throw new Error('Không thể lấy ID người dùng');
        }

        const user = {
          id: userId,
          name: customerName.value || finalUserDetail.fullName,
          email: finalUserDetail.email || "",
          phone: phoneNumber.value, // Use verified phone from form
          avatar: finalUserDetail.avatarUrl || "",
          birthDate: finalUserDetail.birthDate || "",
        };
        authStore.setUser(user);
        console.log('✅ Auth store updated with user:', user);

        // Set token and org data
        useCookie('token').value = accessToken.value;
        if (finalUserDetail.orgPositionsMap) {
          useCookie("dataOrg").value = JSON.stringify(finalUserDetail.orgPositionsMap);
          console.log('✅ DataOrg cookie updated');
        }

        useNuxtApp().$toast.success("Xác thực thành công!");

        // Redirect to onboarding
        console.log('🚀 Redirecting to onboarding with userId:', userId);
        router.push(`/onboarding?userId=${userId}&serviceType=${serviceType.value}`);
      } else {
        throw new Error('Không thể lấy thông tin người dùng');
      }
    } else {
      throw new Error('Mã OTP không chính xác');
    }
    
  } catch (error: any) {
    console.error('❌ Error verifying OTP:', error);
    errors.value.otp = error?.message || 'Mã OTP không chính xác';
  } finally {
    loading.value = false;
  }
};

// Resend OTP
const resendOTP = async () => {
  try {
    const $sdk = useNuxtApp().$sdk;
    await $sdk.auth.sendOTP(phoneNumber.value);
    
    useNuxtApp().$toast.success("Mã OTP đã được gửi lại");
    
    // Start countdown
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
    
  } catch (error) {
    console.error('❌ Error resending OTP:', error);
    useNuxtApp().$toast.error("Không thể gửi lại mã OTP");
  }
};

// Initialize
onMounted(() => {
  if (!phoneNumber.value || !accessToken.value) {
    console.error('❌ Missing required params');
    router.push('/register');
    return;
  }
  
  // Focus first input
  nextTick(() => {
    otpInputRefs.value[0]?.focus();
  });
  
  // Start initial countdown
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
});
</script>
