export default function usePortal() {
  const $sdk = useNuxtApp().$sdk;
  const getProvinces = async () => {
    try {
      const response = await $sdk.user.getProvinces(2); // version = 2
      console.log("response", response);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getWardsByProvinceId = async (provinceId: string) => {
    try {
      const response = await $sdk.user.getWardsByProvinceId(provinceId);
      console.log("response", response);
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    getProvinces,
    getWardsByProvinceId,
  };
}
