/**
 * Onboarding Composable
 * 
 * Q<PERSON>ản lý logic onboarding, progress tracking, step navigation
 */

import { ref, computed, reactive } from 'vue';
import type { 
  OnboardingConfig, 
  OnboardingStep, 
  OnboardingModule, 
  StepResult, 
  OnboardingContext,
  ServiceType,
  OnboardingEvent,
  NavigationOptions
} from '~/types/onboarding';
import {
  ONBOARDING_MODULES,
  SERVICE_MODULE_MAP as SERVICE_MODULE_MAPPING,
  COMPONENT_MAP as STEP_COMPONENTS,
  generateSteps
} from '~/config/onboarding-modules';

export const useOnboarding = () => {
  const $sdk = useNuxtApp().$sdk;
  const router = useRouter();
  const route = useRoute();

  // State
  const config = ref<OnboardingConfig | null>(null);
  const currentStep = ref<OnboardingStep | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const events = ref<OnboardingEvent[]>([]);

  // Context data
  const context = reactive<OnboardingContext>({
    config: null as any,
    currentStep: undefined,
    availableModules: [],
    userData: {},
    organizationData: {}
  });

  // Computed properties
  const progress = computed(() => {
    if (!config.value) return { percentage: 0, current: 0, total: 0 };
    
    const total = config.value.steps.length;
    const completed = config.value.steps.filter(step => step.completed).length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    return { percentage, current: completed, total };
  });

  const navigationOptions = computed((): NavigationOptions => {
    if (!config.value || !currentStep.value) {
      return {
        canGoBack: false,
        canGoNext: false,
        canSkip: false,
        showProgress: true,
        showEstimatedTime: true
      };
    }

    const currentIndex = config.value.steps.findIndex(s => s.id === currentStep.value?.id);
    const canGoBack = currentIndex > 0;
    const canGoNext = currentStep.value.completed || !currentStep.value.required;
    const canSkip = !currentStep.value.required;

    return {
      canGoBack,
      canGoNext,
      canSkip,
      showProgress: true,
      showEstimatedTime: true
    };
  });

  const estimatedTimeRemaining = computed(() => {
    if (!config.value) return 0;
    
    const remainingSteps = config.value.steps.filter(step => !step.completed);
    return remainingSteps.reduce((total, step) => total + (step.estimatedTime || 5), 0);
  });

  // Initialize onboarding for user
  const initializeOnboarding = async (
    userId: string,
    serviceTypes: ServiceType[]
  ): Promise<OnboardingConfig> => {
    loading.value = true;
    error.value = null;

    try {
      // Use generateSteps function
      const steps = generateSteps(serviceTypes);

      // Get modules from service types
      const moduleIds = new Set<string>();
      serviceTypes.forEach(serviceType => {
        SERVICE_MODULE_MAPPING[serviceType]?.forEach(moduleId => {
          moduleIds.add(moduleId);
        });
      });

      // Create config
      const newConfig: OnboardingConfig = {
        userId,
        modules: Array.from(moduleIds),
        steps,
        currentStepId: steps[0]?.id,
        progress: {
          totalSteps: steps.length,
          completedSteps: 0,
          percentage: 0,
          estimatedTimeRemaining: steps.reduce((total, step) => total + (step.estimatedTime || 5), 0)
        },
        startedAt: new Date(),
        metadata: {
          serviceTypes
        }
      };

      config.value = newConfig;
      currentStep.value = steps[0] || null;

      // Update context
      updateContext();

      // Track event
      trackEvent('onboarding_started', undefined, undefined, { serviceTypes, totalSteps: steps.length });

      return newConfig;
    } catch (err: any) {
      error.value = err.message || 'Lỗi khởi tạo onboarding';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Load existing onboarding config
  const loadOnboardingConfig = async (userId: string): Promise<OnboardingConfig | null> => {
    loading.value = true;
    error.value = null;

    try {
      // Try to load from cache first (faster)
      if (process.client && window.__onboardingCache) {
        const cachedConfig = window.__onboardingCache.get(userId);
        if (cachedConfig) {
          config.value = cachedConfig;
          currentStep.value = config.value?.steps.find(s => s.id === config.value?.currentStepId) || null;
          updateContext();
          loading.value = false;
          return cachedConfig;
        }
      }

      // Fallback to localStorage
      const savedConfig = localStorage.getItem(`onboarding_${userId}`);
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        config.value = parsedConfig;
        currentStep.value = config.value?.steps.find(s => s.id === config.value?.currentStepId) || null;
        updateContext();

        // Cache for faster access
        if (process.client && window.__onboardingCache) {
          window.__onboardingCache.set(userId, parsedConfig);
        }

        return parsedConfig;
      }
      return null;
    } catch (err: any) {
      error.value = err.message || 'Lỗi tải cấu hình onboarding';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Save progress
  const saveProgress = async (): Promise<void> => {
    if (!config.value) return;

    try {
      // Save to localStorage (TODO: save to API)
      localStorage.setItem(`onboarding_${config.value.userId}`, JSON.stringify(config.value));

      // Update cache for faster access
      if (process.client && window.__onboardingCache) {
        window.__onboardingCache.set(config.value.userId, config.value);
      }
    } catch (err: any) {
      console.error('Lỗi lưu tiến trình:', err);
    }
  };

  // Navigate to step
  const goToStep = async (stepId: string): Promise<boolean> => {
    if (!config.value) return false;

    const step = config.value.steps.find(s => s.id === stepId);
    if (!step) return false;

    // Check dependencies
    const hasUncompletedDependencies = step.dependencies?.some(depId => {
      const depStep = config.value!.steps.find(s => s.id === depId);
      return depStep && !depStep.completed;
    });

    if (hasUncompletedDependencies) {
      error.value = 'Vui lòng hoàn thành các bước trước đó';
      return false;
    }

    currentStep.value = step;
    config.value.currentStepId = stepId;
    updateContext();
    await saveProgress();

    // Track event
    trackEvent('step_started', stepId, step.module);

    return true;
  };

  // Complete current step
  const completeStep = async (result: StepResult): Promise<boolean> => {
    if (!config.value || !currentStep.value) return false;

    loading.value = true;

    try {
      // Update step data
      currentStep.value.completed = result.success;
      currentStep.value.data = { ...currentStep.value.data, ...result.data };

      // Update progress
      const completedSteps = config.value.steps.filter(s => s.completed).length;
      config.value.progress = {
        totalSteps: config.value.steps.length,
        completedSteps,
        percentage: Math.round((completedSteps / config.value.steps.length) * 100),
        estimatedTimeRemaining: estimatedTimeRemaining.value
      };

      // Track event
      trackEvent('step_completed', currentStep.value.id, currentStep.value.module, result.data);

      // Save progress
      await saveProgress();

      // Navigate to next step or specified step
      if (result.nextStepId) {
        await goToStep(result.nextStepId);
      } else if (result.skipToStepId) {
        await goToStep(result.skipToStepId);
      } else {
        await goToNextStep();
      }

      return true;
    } catch (err: any) {
      error.value = err.message || 'Lỗi hoàn thành bước';
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Navigate to next step
  const goToNextStep = async (): Promise<boolean> => {
    if (!config.value || !currentStep.value) return false;

    // Mark current step as completed first
    if (!currentStep.value.completed) {
      const result: any = {
        stepId: currentStep.value.id,
        success: true,
        data: {}
      };

      // Update step data without auto-navigation
      currentStep.value.completed = result.success;
      currentStep.value.data = { ...currentStep.value.data, ...result.data };

      // Update progress
      const completedSteps = config.value.steps.filter(s => s.completed).length;
      config.value.progress = {
        totalSteps: config.value.steps.length,
        completedSteps,
        percentage: Math.round((completedSteps / config.value.steps.length) * 100),
        estimatedTimeRemaining: estimatedTimeRemaining.value
      };

      // Track event
      trackEvent('step_completed', currentStep.value.id, currentStep.value.module, result.data);

      // Save progress
      await saveProgress();
    }

    const currentIndex = config.value.steps.findIndex(s => s.id === currentStep.value?.id);
    const nextStep = config.value.steps[currentIndex + 1];

    if (nextStep) {
      return await goToStep(nextStep.id);
    } else {
      // Onboarding completed
      await completeOnboarding();
      return true;
    }
  };

  // Navigate to previous step
  const goToPreviousStep = async (): Promise<boolean> => {
    if (!config.value || !currentStep.value) return false;

    const currentIndex = config.value.steps.findIndex(s => s.id === currentStep.value?.id);
    const previousStep = config.value.steps[currentIndex - 1];

    if (previousStep) {
      return await goToStep(previousStep.id);
    }

    return false;
  };

  // Skip current step
  const skipStep = async (): Promise<boolean> => {
    if (!currentStep.value || currentStep.value.required) return false;

    // Track event
    trackEvent('step_skipped', currentStep.value.id, currentStep.value.module);

    return await goToNextStep();
  };

  // Complete onboarding
  const completeOnboarding = async (): Promise<void> => {
    if (!config.value) return;

    config.value.completedAt = new Date();
    await saveProgress();

    // Track event
    const startTime = config.value.startedAt instanceof Date
      ? config.value.startedAt.getTime()
      : new Date(config.value.startedAt).getTime();

    trackEvent('onboarding_completed', undefined, undefined, {
      totalTime: Date.now() - startTime,
      completedSteps: config.value.progress.completedSteps,
      totalSteps: config.value.progress.totalSteps
    });

    // Redirect based on service type
    const serviceTypes = config.value.metadata?.serviceTypes as string[];

    if (serviceTypes?.includes('TELESALE')) {
      // TELESALE -> Portal
      const portalUrl = `https://portal.dev.longvan.vn/crm/pages/resume-template/detail.xhtml?id=20.575.7922&orgId=${useCookie('orgId').value}&accessToken=${useCookie('token').value}`;
      window.location.href = portalUrl;
    } else if (serviceTypes?.includes('POS')) {
      // POS -> Unified org-store page
      router.push('/org-store');
    } else {
      // Default -> Unified org-store page
      router.push('/org-store');
    }
  };

  // Get step component
  const getStepComponent = (componentName: string) => {
    return STEP_COMPONENTS[componentName as keyof typeof STEP_COMPONENTS];
  };

  // Track events
  const trackEvent = (
    type: OnboardingEvent['type'], 
    stepId?: string, 
    moduleId?: string, 
    data?: Record<string, any>
  ) => {
    const event: OnboardingEvent = {
      type,
      stepId,
      moduleId,
      data,
      timestamp: new Date()
    };

    events.value.push(event);

    // TODO: Send to analytics API
    console.log('📊 Onboarding Event:', event);
  };

  // Update context
  const updateContext = () => {
    if (!config.value) return;

    context.config = config.value;
    context.currentStep = currentStep.value;
    context.availableModules = config.value.modules.map(moduleId => {
      const moduleConfig = ONBOARDING_MODULES[moduleId];
      return {
        ...moduleConfig,
        steps: config.value!.steps.filter(s => s.module === moduleId),
        enabled: true
      };
    });
  };

  // Reset onboarding
  const resetOnboarding = async (userId: string): Promise<void> => {
    localStorage.removeItem(`onboarding_${userId}`);
    config.value = null;
    currentStep.value = null;
    error.value = null;
    events.value = [];
  };

  return {
    // State
    config: readonly(config),
    currentStep: readonly(currentStep),
    loading: readonly(loading),
    error: readonly(error),
    context: readonly(context),
    events: readonly(events),

    // Computed
    progress,
    navigationOptions,
    estimatedTimeRemaining,

    // Methods
    initializeOnboarding,
    loadOnboardingConfig,
    saveProgress,
    goToStep,
    completeStep,
    goToNextStep,
    goToPreviousStep,
    skipStep,
    completeOnboarding,
    getStepComponent,
    trackEvent,
    resetOnboarding
  };
};
