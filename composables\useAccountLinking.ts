import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';

export default function useAccountLinking() {
  const router = useRouter();
  const loading = ref(false);
  
  const errors = reactive({
    password: '',
    otp: ''
  });

  // Link account with password - Using linkingUserLoginAndUserDetail
  const linkAccountWithPassword = async (params: {
    phone: string;
    password: string;
    socialToken: string;
    socialProvider: string;
  }) => {
    loading.value = true;
    errors.password = '';

    try {
      const $sdk = useNuxtApp().$sdk;

      console.log('🔐 Linking social account with existing account via password...');

      // Step 1: Verify password with existing account
      const loginResult = await $sdk.auth.login({
        username: params.phone,
        password: params.password
      });

      if (!loginResult) {
        throw new Error('Mật khẩu không chính xác');
      }

      console.log('✅ Password verification successful:', loginResult);

      // Step 2: Get social userLogin info using getUserLoginByToken
      const socialUserLogin = await $sdk.auth.getUserLoginByToken(params.socialToken);
      console.log('✅ Social user login info:', socialUserLogin);

      // Step 3: Extract IDs for linking
      // From social login: we need the userLoginId
      // From existing account: we need the partyId
      const socialUserLoginId = socialUserLogin.userLoginId || socialUserLogin.id;
      const existingPartyId = loginResult.partyId;

      console.log('🔗 Linking parameters:', {
        socialUserLoginId,
        existingPartyId,
        socialToken: params.socialToken,
        existingAccessToken: loginResult.accessToken
      });

      if (!socialUserLoginId) {
        throw new Error('Không thể lấy userLoginId từ tài khoản social');
      }

      if (!existingPartyId) {
        throw new Error('Không thể lấy partyId từ tài khoản hiện tại');
      }

      // Step 4: Link using linkingUserLoginAndUserDetail
      const linkResult = await $sdk.auth.linkingUserLoginAndUserDetail(
        socialUserLoginId, // userLoginId from social account
        existingPartyId    // partyId from existing account
      );

      console.log('✅ Account linking successful:', linkResult);

      // Step 5: Set authentication state with the existing account
      const { setToken } = useAuth();
      setToken(loginResult.accessToken);

      const authStore = useAuthStore();
      const user = {
        id: loginResult.partyId,
        name: loginResult.fullName,
        email: loginResult.email,
        phone: loginResult.phone,
        avatar: loginResult.avatarUrl,
        birthDate: loginResult.birthDate,
        // roles will be set when user selects org/store
      };
      authStore.setUser(user);

      useNuxtApp().$toast.success(`Tài khoản ${params.socialProvider} đã được liên kết thành công!`);

      return {
        linkResult,
        existingAccount: loginResult,
      };

    } catch (error: any) {
      console.error('❌ Password linking error:', error);

      if (error?.message?.includes('password') ||
          error?.message?.includes('mật khẩu') ||
          error?.message?.includes('incorrect') ||
          error?.message?.includes('invalid') ||
          error?.message?.includes('wrong') ||
          error?.response?.status === 401) {
        errors.password = "Mật khẩu không chính xác";
      } else {
        errors.password = error?.message || "Có lỗi xảy ra khi xác thực";
      }
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // Send OTP for account linking
  const sendOTPForLinking = async (params: {
    phone: string;
    method: 'SMS' | 'ZALO';
    socialToken: string;
  }) => {
    loading.value = true;

    try {
      const $sdk = useNuxtApp().$sdk;

      console.log(`📱 Sending OTP via ${params.method} for account linking...`);

      // Send OTP to existing phone number
      await $sdk.auth.sendOTP({
        phone: params.phone,
        method: params.method
      });

      console.log(`✅ OTP sent via ${params.method}`);
      useNuxtApp().$toast.success(`Mã OTP đã được gửi qua ${params.method === 'SMS' ? 'SMS' : 'Zalo'}`);

    } catch (error: any) {
      console.error(`❌ Error sending OTP via ${params.method}:`, error);
      useNuxtApp().$toast.error(error?.message || `Có lỗi xảy ra khi gửi OTP qua ${params.method}`);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // Link account with OTP - Using getAccessTokenByOTP to get user token
  const linkAccountWithOTP = async (params: {
    phone: string;
    otp: string;
    socialToken: string;
    socialProvider: string;
  }) => {
    loading.value = true;
    errors.otp = '';

    try {
      const $sdk = useNuxtApp().$sdk;

      console.log('🔗 Linking social account with existing account via OTP...');

      // Step 1: Get access token by validating OTP - this gives us the logged-in user's token
      const otpTokenResult = await $sdk.auth.getAccessTokenByOTP(
        params.otp,
        params.phone,
        'SMS' // Default to SMS, can be made configurable
      );

      if (!otpTokenResult || !otpTokenResult.accessToken) {
        throw new Error('Mã OTP không chính xác hoặc đã hết hạn');
      }

      console.log('✅ OTP validation successful, got access token:', otpTokenResult);

      // Step 2: Get existing user info using the access token
      const existingUserDetail = await $sdk.auth.getUserDetail(otpTokenResult.accessToken);
      if (!existingUserDetail) {
        throw new Error('Không thể lấy thông tin tài khoản hiện tại');
      }

      console.log('✅ Existing user detail:', existingUserDetail);

      // Step 3: Get social userLogin info using getUserLoginByToken
      const socialUserLogin = await $sdk.auth.getUserLoginByToken(params.socialToken);
      console.log('✅ Social user login info:', socialUserLogin);

      // Step 4: Extract IDs for linking
      // From social login: we need the userLoginId
      // From existing account: we need the partyId
      const socialUserLoginId = socialUserLogin.userLoginId || socialUserLogin.id;
      const existingPartyId = existingUserDetail.partyId;

      console.log('🔗 Linking parameters:', {
        socialUserLoginId,
        existingPartyId,
        socialToken: params.socialToken,
        existingAccessToken: otpTokenResult.accessToken
      });

      if (!socialUserLoginId) {
        throw new Error('Không thể lấy userLoginId từ tài khoản social');
      }

      if (!existingPartyId) {
        throw new Error('Không thể lấy partyId từ tài khoản hiện tại');
      }

      // Step 5: Link using linkingUserLoginAndUserDetail
      const linkResult = await $sdk.auth.linkingUserLoginAndUserDetail(
        socialUserLoginId, // userLoginId from social account
        existingPartyId    // partyId from existing account
      );

      console.log('✅ Account linking successful:', linkResult);

      // Step 6: Set authentication state with the existing user's token
      const { setToken } = useAuth();
      setToken(otpTokenResult.accessToken); // Use the existing user's token

      const authStore = useAuthStore();
      const user = {
        id: existingPartyId,
        name: existingUserDetail.fullName || existingUserDetail.name,
        email: existingUserDetail.email,
        phone: params.phone,
        avatar: existingUserDetail.avatarUrl || existingUserDetail.avatar,
        birthDate: existingUserDetail.birthDate,
      };
      authStore.setUser(user);

      useNuxtApp().$toast.success(`Tài khoản ${params.socialProvider} đã được liên kết thành công!`);

      return {
        linkResult,
        existingAccount: existingUserDetail,
        socialAccount: socialUserDetail,
        accessToken: otpTokenResult.accessToken
      };

    } catch (error: any) {
      console.error('❌ OTP linking error:', error);

      if (error?.message?.includes('OTP') ||
          error?.message?.includes('mã') ||
          error?.message?.includes('expired') ||
          error?.message?.includes('hết hạn') ||
          error?.message?.includes('invalid') ||
          error?.message?.includes('không chính xác')) {
        errors.otp = "Mã OTP không chính xác hoặc đã hết hạn";
      } else {
        errors.otp = error?.message || "Có lỗi xảy ra khi xác thực OTP";
      }
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // Redirect after successful linking
  const redirectAfterLinking = (registrationService?: string) => {
    console.log('🔄 Redirecting after linking with service:', registrationService);

    if (registrationService) {
      if (registrationService === 'TELESALE') {
        // TELESALE: Redirect directly to portal (user already has org)
        console.log('📞 TELESALE service - redirecting to portal');

        const currentToken = useCookie('accessToken').value;

        if (currentToken) {
          const portalUrl = `https://portal.dev.longvan.vn/crm/pages/resume-template/detail.xhtml?orgId=LONGVAN&accessToken=${currentToken}`;

          console.log('🚀 Redirecting TELESALE to portal:', portalUrl);
          useNuxtApp().$toast.success('Liên kết thành công! Chuyển đến portal...');

          setTimeout(() => {
            window.location.href = portalUrl;
          }, 2000);
        } else {
          console.error('❌ No access token found for portal redirect');
  
        }
      } else {
        // Other services: Go to onboarding flow since user already has account
        console.log('🚀 Account linked successfully, redirecting to onboarding flow');

        // Get user info to pass to onboarding
        const authStore = useAuthStore();
        const userId = authStore.user?.id;

        if (userId) {
          const redirectQuery: any = {
            userId: userId,
            serviceType: registrationService
          };

          console.log('🚀 Redirecting to onboarding with:', redirectQuery);

          router.push({
            path: '/onboarding',
            query: redirectQuery
          });
        } else {
          console.error('❌ No userId found after linking');
          // Fallback to org-store selection
          router.push('/org-store');
        }
      }
    } else {
      // Default redirect to org-store selection
      console.log('🚀 No registration service, redirecting to /org-store');
      router.push('/org-store');
    }
  };

  return {
    loading,
    errors,
    linkAccountWithPassword,
    sendOTPForLinking,
    linkAccountWithOTP,
    redirectAfterLinking
  };
}
