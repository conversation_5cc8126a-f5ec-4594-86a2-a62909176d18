<template>
  <div class="onboarding-progress-bar">
    <!-- Progress Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3">
        <h3 class="text-lg font-semibold text-gray-800">
          <PERSON><PERSON><PERSON><PERSON> lập hệ thống
        </h3>
        <div 
          v-if="showPercentage"
          class="px-2 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full"
        >
          {{ percentage }}%
        </div>
      </div>
      
      <div 
        v-if="showStepNumbers"
        class="text-sm text-gray-600"
      >
        Bước {{ current }} / {{ total }}
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="relative">
      <!-- Background -->
      <div 
        class="w-full bg-gray-200 rounded-full overflow-hidden"
        :class="sizeClasses"
      >
        <!-- Progress Fill -->
        <div 
          class="bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-500 ease-out rounded-full"
          :class="sizeClasses"
          :style="{ width: `${percentage}%` }"
        >
          <!-- Shimmer Effect -->
          <div 
            v-if="isAnimating"
            class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"
          ></div>
        </div>
      </div>

      <!-- Step Markers (for detailed progress) -->
      <div 
        v-if="showStepMarkers && steps.length > 0"
        class="absolute top-0 left-0 w-full flex justify-between"
        :class="markerTopClass"
      >
        <div
          v-for="(step, index) in steps"
          :key="step.id"
          class="flex flex-col items-center"
          :style="{ left: `${(index / (steps.length - 1)) * 100}%` }"
        >
          <!-- Marker Circle -->
          <div
            class="w-3 h-3 rounded-full border-2 transition-all duration-300"
            :class="getMarkerClass(step, index)"
          ></div>
          
          <!-- Step Label (optional) -->
          <div
            v-if="showStepLabels"
            class="mt-2 text-xs text-center max-w-16 leading-tight"
            :class="getMarkerTextClass(step)"
          >
            {{ step.title }}
          </div>
        </div>
      </div>
    </div>

    <!-- Estimated Time -->
    <div 
      v-if="showEstimatedTime && estimatedTimeRemaining > 0"
      class="mt-3 flex items-center justify-center text-sm text-gray-600"
    >
      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V5z" clip-rule="evenodd" />
      </svg>
      <span>Còn khoảng {{ formatTime(estimatedTimeRemaining) }}</span>
    </div>

    <!-- Current Step Info -->
    <div 
      v-if="currentStepTitle"
      class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200"
    >
      <div class="flex items-center space-x-2">
        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        <span class="text-sm font-medium text-blue-800">
          Đang thực hiện: {{ currentStepTitle }}
        </span>
      </div>
      <div 
        v-if="currentStepDescription"
        class="mt-1 text-xs text-blue-600"
      >
        {{ currentStepDescription }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { OnboardingStep } from '~/types/onboarding';

interface Props {
  current: number;
  total: number;
  percentage?: number;
  showPercentage?: boolean;
  showStepNumbers?: boolean;
  showStepMarkers?: boolean;
  showStepLabels?: boolean;
  showEstimatedTime?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  estimatedTimeRemaining?: number;
  currentStepTitle?: string;
  currentStepDescription?: string;
  steps?: OnboardingStep[];
  isAnimating?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  percentage: 0,
  showPercentage: true,
  showStepNumbers: true,
  showStepMarkers: false,
  showStepLabels: false,
  showEstimatedTime: true,
  size: 'md',
  color: 'blue',
  estimatedTimeRemaining: 0,
  steps: () => [],
  isAnimating: false
});

// Computed properties
const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm': return 'h-2';
    case 'lg': return 'h-4';
    default: return 'h-3';
  }
});

const markerTopClass = computed(() => {
  switch (props.size) {
    case 'sm': return '-mt-1';
    case 'lg': return '-mt-2';
    default: return '-mt-1.5';
  }
});

// Methods
const getMarkerClass = (step: OnboardingStep, index: number) => {
  if (step.completed) {
    return 'bg-green-500 border-green-500';
  } else if (index === props.current - 1) {
    return 'bg-blue-500 border-blue-500 animate-pulse';
  } else {
    return 'bg-white border-gray-300';
  }
};

const getMarkerTextClass = (step: OnboardingStep) => {
  if (step.completed) {
    return 'text-green-600 font-medium';
  } else {
    return 'text-gray-500';
  }
};

const formatTime = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} phút`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 
      ? `${hours} giờ ${remainingMinutes} phút`
      : `${hours} giờ`;
  }
};

// Watch for percentage changes to trigger animation
watch(() => props.percentage, (newVal, oldVal) => {
  if (newVal > oldVal) {
    // Trigger animation effect
    nextTick(() => {
      // Animation logic can be added here
    });
  }
});
</script>

<style scoped>
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Smooth progress bar animation */
.onboarding-progress-bar .bg-gradient-to-r {
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Pulse animation for current step marker */
@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

.animate-pulse {
  animation: pulse-blue 2s infinite;
}
</style>
