<template>
  <div
    class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4"
  >
    <!-- Background Pattern -->
    <div
      class="absolute inset-0 opacity-40"
      style="
        background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%239C92AC&quot; fill-opacity=&quot;0.05&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;4&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
      "
    ></div>

    <!-- Main Container -->
    <div
      class="relative w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 items-center"
    >
      <!-- Left Side - Branding & Info -->
      <div class="hidden lg:flex flex-col justify-center space-y-8 px-8">
        <!-- Logo & Brand -->
        <div class="space-y-6">
          <div class="flex items-center space-x-3">
            <div
              class="w-12 h-12 bg-primary rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-7 h-7 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                ></path>
              </svg>
            </div>
            <h1 class="text-3xl font-bold text-primary">POS Online</h1>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-gray-800">Tạo tài khoản mới</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              Đăng ký ngay để bắt đầu quản lý cửa hàng của bạn một cách hiệu quả
            </p>
          </div>
        </div>

        <!-- Features List -->
        <div class="space-y-4">
          <div class="flex items-start space-x-3">
            <div
              class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
            >
              <svg
                class="w-4 h-4 text-green-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800">Quản lý bán hàng</h3>
              <p class="text-sm text-gray-600">
                Theo dõi đơn hàng và doanh thu một cách dễ dàng
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div
              class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
            >
              <svg
                class="w-4 h-4 text-blue-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800">Quản lý khách hàng</h3>
              <p class="text-sm text-gray-600">
                Lưu trữ thông tin khách hàng và lịch sử mua hàng
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div
              class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
            >
              <svg
                class="w-4 h-4 text-purple-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800">Báo cáo thống kê</h3>
              <p class="text-sm text-gray-600">
                Phân tích dữ liệu kinh doanh chi tiết
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side - Register Form -->
      <div class="w-full max-w-md mx-auto lg:mx-0">
        <div
          class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8"
        >
          <!-- Mobile Logo -->
          <div
            class="lg:hidden flex items-center justify-center space-x-3 mb-8"
          >
            <div
              class="w-10 h-10 bg-primary rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                ></path>
              </svg>
            </div>
            <h1 class="text-2xl font-bold text-primary">POS Online</h1>
          </div>

          <!-- Form Header -->
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-2">Đăng ký</h2>
            <p class="text-gray-600">Tạo tài khoản mới để bắt đầu</p>
          </div>

          <!-- Register Form -->
          <AuthSocialLoginButtons mode="register" />

          <form @submit.prevent="handleRegisterSubmit" class="space-y-6">
            <!-- Customer Name Field -->
            <div class="space-y-2">
              <label
                for="customerName"
                class="block text-sm font-semibold text-gray-700"
              >
                Tên khách hàng
              </label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <input
                  id="customerName"
                  v-model="customerName"
                  type="text"
                  name="customerName"
                  autocomplete="name"
                  @blur="validateFieldOnBlur('customerName')"
                  :class="[
                    'block w-full pl-10 pr-3 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                    errors.customerName
                      ? 'border-red-300 bg-red-50'
                      : 'border-gray-300 bg-white hover:border-gray-400',
                  ]"
                  placeholder="Nhập tên của bạn"
                />
              </div>
              <Transition name="slide-down">
                <span
                  v-if="errors.customerName"
                  class="text-sm text-red-600 flex items-center space-x-1"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span>{{ errors.customerName }}</span>
                </span>
              </Transition>
            </div>

            <!-- Phone Number Field -->
            <div class="space-y-2">
              <label
                for="phoneNumber"
                class="block text-sm font-semibold text-gray-700"
              >
                Số điện thoại
              </label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                    ></path>
                  </svg>
                </div>
                <input
                  id="phoneNumber"
                  v-model="phoneNumber"
                  type="tel"
                  name="phoneNumber"
                  autocomplete="tel"
                  @blur="validateFieldOnBlur('phoneNumber')"
                  :class="[
                    'block w-full pl-10 pr-3 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                    errors.phoneNumber
                      ? 'border-red-300 bg-red-50'
                      : 'border-gray-300 bg-white hover:border-gray-400',
                  ]"
                  placeholder="Nhập số điện thoại"
                />
              </div>
              <Transition name="slide-down">
                <span
                  v-if="errors.phoneNumber"
                  class="text-sm text-red-600 flex items-center space-x-1"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span>{{ errors.phoneNumber }}</span>
                </span>
              </Transition>
            </div>

            <!-- Submit Button -->
            <div class="space-y-4">
              <button
                type="submit"
                :disabled="loading"
                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                  <svg
                    class="h-5 w-5 text-primary-light group-hover:text-white transition-colors duration-200"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </span>
                {{ loading ? "Đang xử lý..." : "Đăng ký" }}
              </button>

              <!-- Social Login -->
              <!-- Login Link -->
              <div class="text-center">
                <span class="text-sm text-gray-600">Đã có tài khoản? </span>
                <NuxtLink
                  :to="loginLink"
                  class="text-sm text-primary hover:text-primary/80 font-medium transition-colors duration-200 hover:underline"
                >
                  Đăng nhập ngay
                </NuxtLink>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div v-if="loading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
// Import component explicitly
import AuthSocialLoginButtons from "~/components/auth/SocialLoginButtons.vue";

useHead({
  title: "Đăng ký",
});

const route = useRoute();
const router = useRouter();

// Computed link to preserve serviceType when navigating to login
const loginLink = computed(() => {
  const query: Record<string, string> = {};

  // Preserve serviceType if it exists
  if (route.query.serviceType) {
    query.serviceType = route.query.serviceType as string;
  }

  return {
    path: '/login',
    query: Object.keys(query).length > 0 ? query : undefined
  };
});

// Check for social login success on mount
onMounted(async () => {
  // Ensure orgId context is set for social login
  const $sdk = useNuxtApp().$sdk;
  if (!$sdk.orgId || $sdk.orgId === 'N/A') {
    console.log('🔧 Setting default orgId context for social login...');
    $sdk.setOrgId('LONGVAN');
    useCookie("orgId").value = 'LONGVAN';
    console.log('✅ OrgId context set:', $sdk.orgId);
  }

  const accessToken = route.query.accessToken as string;
  const status = route.query.status as string;
  const orgId = route.query.orgID as string;
  const registrationType = (route.query.serviceType as string) || (route.query.type as string) || "POS";

  // Note: Social login callbacks are now handled by /auth/social-callback page
  // This page only handles normal registration flow
});

const {
  customerName,
  phoneNumber,
  errors,
  validateFieldOnBlur,
  handleRegisterSubmit,
  loading,
} = useRegister();
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-xl {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

/* Enhanced focus styles */
.focus\:ring-primary:focus {
  --tw-ring-color: #3f51b5;
}

/* Smooth transitions for all interactive elements */
button,
input {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
