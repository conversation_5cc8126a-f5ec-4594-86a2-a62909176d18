<template>
  <div class="flex-1 flex flex-col">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">
            {{ selectedOrg ? `Cửa hàng - ${selectedOrg.name}` : '<PERSON>ọn tổ chức' }}
          </h2>
          <p class="text-gray-600 mt-1">
            {{ selectedOrg ? 'Chọn cửa hàng để tiếp tục' : '<PERSON>ui lòng chọn tổ chức từ sidebar' }}
          </p>
        </div>
        <div v-if="selectedOrg" class="text-sm text-gray-500">
          {{ filteredStores?.length || 0 }} / {{ stores?.length || 0 }} cửa hàng
        </div>
      </div>

      <!-- Search Bar -->
      <div v-if="selectedOrg && stores?.length > 0" class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Tìm kiếm cửa hàng..."
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          @keydown.escape="searchQuery = ''"
        />
        <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <button
            @click="searchQuery = ''"
            class="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Stores Content -->
    <div class="flex-1 overflow-y-auto p-6">
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-16">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600">{{ loadingMessage }}</p>
        </div>
      </div>

      <!-- No Organization Selected -->
      <div v-else-if="!selectedOrg" class="flex items-center justify-center py-16">
        <div class="text-center">
          <div class="text-gray-400 mb-4">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Chọn tổ chức</h3>
          <p class="text-gray-600">Vui lòng chọn một tổ chức từ sidebar để xem danh sách cửa hàng</p>
        </div>
      </div>

      <!-- Stores Grid -->
      <transition-group
        v-if="filteredStores?.length > 0"
        name="store-grid"
        tag="div"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <div
          v-for="store in filteredStores"
          :key="`store-${store.id}`"
          class="group store-item"
        >
          <div
            @click="$emit('select', store)"
            class="relative bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-105 hover:-translate-y-1"
            :class="{
              'ring-2 ring-blue-500 ring-opacity-50 shadow-lg': selectedStore === store.id,
            }"
          >
            <!-- Store Header -->
            <div class="flex items-center justify-between mb-4">
              <div class="relative">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
              </div>
              <div v-if="selectedStore === store.id" class="text-blue-600">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>

            <!-- Store Info -->
            <div class="space-y-2">
              <h3 class="font-semibold text-gray-900 text-lg">{{ store.name }}</h3>
              <p class="text-sm text-gray-600">{{ getStoreDescription(store) }}</p>
              
              <!-- Store Stats -->
              <div class="flex items-center justify-between pt-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ store.type || 'POS' }}
                </span>
                <span class="text-xs text-gray-500">
                  {{ store.warehouses?.length || 0 }} kho
                </span>
              </div>
            </div>

            <!-- Loading Overlay -->
            <div v-if="selectLoading && selectedStore === store.id" class="absolute inset-0 bg-white bg-opacity-75 rounded-2xl flex items-center justify-center">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
          </div>
        </div>
      </transition-group>

      <!-- No Stores Found -->
      <div v-else-if="stores?.length > 0 && filteredStores?.length === 0" class="flex items-center justify-center py-16">
        <div class="text-center">
          <div class="text-gray-400 mb-4">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Không tìm thấy cửa hàng</h3>
          <p class="text-gray-600">Không có cửa hàng nào phù hợp với từ khóa "{{ searchQuery }}"</p>
        </div>
      </div>

      <!-- No Stores -->
      <div v-else class="flex items-center justify-center py-16">
        <div class="text-center">
          <div class="text-gray-400 mb-4">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-2.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">{{ emptyTitle }}</h3>
          <p class="text-gray-600">{{ emptyMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface Props {
  stores?: any[];
  selectedOrg?: any;
  selectedStore?: any;
  loading?: boolean;
  selectLoading?: boolean;
  loadingMessage?: string;
  emptyTitle?: string;
  emptyMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
  stores: () => [],
  selectedOrg: null,
  selectedStore: null,
  loading: false,
  selectLoading: false,
  loadingMessage: "Đang tải danh sách cửa hàng...",
  emptyTitle: "Không có cửa hàng",
  emptyMessage: "Tổ chức này chưa có cửa hàng nào"
});

const emit = defineEmits<{
  select: [store: any];
}>();

// Search functionality
const searchQuery = ref('');

const filteredStores = computed(() => {
  if (!searchQuery.value || !props.stores) return props.stores;

  const query = searchQuery.value.toLowerCase();
  return props.stores.filter(store =>
    store.name?.toLowerCase().includes(query) ||
    store.type?.toLowerCase().includes(query)
  );
});

// Utility functions
const getStoreDescription = (store: any) => {
  const warehouseCount = store.warehouses?.length || 0;
  return `Cửa hàng ${store.type || "POS"} với ${warehouseCount} kho hàng`;
};

// Watch for stores change to reset search
watch(() => props.stores, () => {
  searchQuery.value = '';
});
</script>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
