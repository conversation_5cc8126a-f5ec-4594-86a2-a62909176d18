<template>
  <div class="stepper-navigation">
    <!-- Horizontal Stepper (Desktop) -->
    <div 
      v-if="orientation === 'horizontal'"
      class="hidden md:block"
    >
      <div class="flex items-center justify-between">
        <div
          v-for="(step, index) in visibleSteps"
          :key="step.id"
          class="flex items-center"
          :class="{ 'flex-1': index < visibleSteps.length - 1 }"
        >
          <!-- Step Circle -->
          <div
            class="relative flex items-center justify-center cursor-pointer group"
            @click="handleStepClick(step)"
          >
            <div
              class="w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all duration-300"
              :class="getStepCircleClass(step, index)"
            >
              <!-- Completed Icon -->
              <svg
                v-if="step.completed"
                class="w-5 h-5 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <!-- Current Step Icon -->
              <svg
                v-else-if="isCurrentStep(step)"
                class="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <!-- Step Number -->
              <span
                v-else
                class="text-sm font-semibold"
                :class="getStepNumberClass(step)"
              >
                {{ index + 1 }}
              </span>
            </div>

            <!-- Step Label -->
            <div
              v-if="showLabels"
              class="absolute top-12 left-1/2 transform -translate-x-1/2 text-center"
            >
              <div
                class="text-sm font-medium whitespace-nowrap"
                :class="getStepLabelClass(step)"
              >
                {{ step.title }}
              </div>
             
            </div>

            <!-- Tooltip -->
            <div
              v-if="!showLabels"
              class="absolute bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10"
            >
              {{ step.title }}
              <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
            </div>
          </div>

          <!-- Connector Line -->
          <div
            v-if="index < visibleSteps.length - 1"
            class="flex-1 h-0.5 mx-4 transition-colors duration-300"
            :class="getConnectorClass(step, visibleSteps[index + 1])"
          ></div>
        </div>
      </div>
    </div>

    <!-- Vertical Stepper (Mobile) -->
    <div 
      v-else
      class="md:hidden space-y-4"
    >
      <div
        v-for="(step, index) in visibleSteps"
        :key="step.id"
        class="flex items-start space-x-3"
        @click="handleStepClick(step)"
      >
        <!-- Step Circle -->
        <div
          class="flex-shrink-0 w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300"
          :class="getStepCircleClass(step, index)"
        >
          <!-- Completed Icon -->
          <svg
            v-if="step.completed"
            class="w-4 h-4 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <!-- Current Step Icon -->
          <svg
            v-else-if="isCurrentStep(step)"
            class="w-4 h-4"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <!-- Step Number -->
          <span
            v-else
            class="text-xs font-semibold"
            :class="getStepNumberClass(step)"
          >
            {{ index + 1 }}
          </span>
        </div>

        <!-- Step Content -->
        <div class="flex-1 min-w-0">
          <div
            class="text-sm font-medium"
            :class="getStepLabelClass(step)"
          >
            {{ step.title }}
          </div>
          <div
            v-if="step.description"
            class="text-xs text-gray-500 mt-1"
          >
            {{ step.description }}
          </div>
         
        </div>

        <!-- Status Badge -->
        <div class="flex-shrink-0">
          <span
            v-if="step.completed"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
          >
            Hoàn thành
          </span>
          <span
            v-else-if="isCurrentStep(step)"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            Đang thực hiện
          </span>
          <span
            v-else-if="canAccessStep(step)"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
          >
            Sẵn sàng
          </span>
          <span
            v-else
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-400"
          >
            Chờ
          </span>
        </div>
      </div>
    </div>

    <!-- Module Sections (if showing modules) -->
    <div
      v-if="showModules && groupedSteps"
      class="mt-8 space-y-6"
    >
      <div
        v-for="(moduleSteps, moduleId) in groupedSteps"
        :key="moduleId"
        class="border border-gray-200 rounded-lg p-4"
      >
        <div class="flex items-center space-x-3 mb-4">
          <div
            class="w-8 h-8 rounded-lg flex items-center justify-center"
            :class="getModuleIconClass(moduleId)"
          >
            <svg
              class="w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd" />
            </svg>
          </div>
          <div>
            <h4 class="font-medium text-gray-900">
              {{ getModuleName(moduleId) }}
            </h4>
            <p class="text-sm text-gray-500">
              {{ getModuleProgress(moduleSteps) }}
            </p>
          </div>
        </div>

        <!-- Module Steps -->
        <div class="space-y-2">
          <div
            v-for="step in moduleSteps"
            :key="step.id"
            class="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50 cursor-pointer"
            @click="handleStepClick(step)"
          >
            <div
              class="w-6 h-6 rounded-full border flex items-center justify-center"
              :class="getStepCircleClass(step, 0, true)"
            >
              <svg
                v-if="step.completed"
                class="w-3 h-3 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <div
                v-else-if="isCurrentStep(step)"
                class="w-2 h-2 bg-blue-500 rounded-full"
              ></div>
            </div>
            <span
              class="text-sm"
              :class="getStepLabelClass(step)"
            >
              {{ step.title }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { OnboardingStep } from '~/types/onboarding';
import { ONBOARDING_MODULES } from '~/config/onboarding-modules';

interface Props {
  steps: OnboardingStep[];
  currentStepId: string;
  onStepClick?: (stepId: string) => void;
  showLabels?: boolean;
  showModules?: boolean;
  orientation?: 'horizontal' | 'vertical';
  maxVisibleSteps?: number;
}

const props = withDefaults(defineProps<Props>(), {
  showLabels: true,
  showModules: false,
  orientation: 'horizontal',
  maxVisibleSteps: 5
});

const emit = defineEmits<{
  stepClick: [stepId: string];
}>();

// Computed properties
const visibleSteps = computed(() => {
  if (props.maxVisibleSteps && props.steps.length > props.maxVisibleSteps) {
    const currentIndex = props.steps.findIndex(s => s.id === props.currentStepId);
    const start = Math.max(0, currentIndex - Math.floor(props.maxVisibleSteps / 2));
    const end = Math.min(props.steps.length, start + props.maxVisibleSteps);
    return props.steps.slice(start, end);
  }
  return props.steps;
});

const groupedSteps = computed(() => {
  if (!props.showModules) return null;
  
  const groups: Record<string, OnboardingStep[]> = {};
  props.steps.forEach(step => {
    if (!groups[step.module]) {
      groups[step.module] = [];
    }
    groups[step.module].push(step);
  });
  return groups;
});

// Methods
const isCurrentStep = (step: OnboardingStep): boolean => {
  return step.id === props.currentStepId;
};

const canAccessStep = (step: OnboardingStep): boolean => {
  if (!step.dependencies) return true;
  
  return step.dependencies.every(depId => {
    const depStep = props.steps.find(s => s.id === depId);
    return depStep?.completed;
  });
};

const handleStepClick = (step: OnboardingStep) => {
  if (canAccessStep(step) || step.completed) {
    emit('stepClick', step.id);
    props.onStepClick?.(step.id);
  }
};

const getStepCircleClass = (step: OnboardingStep, index: number, small = false) => {
  const baseSize = small ? 'w-6 h-6' : 'w-10 h-10';
  
  if (step.completed) {
    return `${baseSize} bg-green-500 border-green-500`;
  } else if (isCurrentStep(step)) {
    return `${baseSize} bg-blue-500 border-blue-500 animate-pulse`;
  } else if (canAccessStep(step)) {
    return `${baseSize} bg-white border-gray-300 hover:border-blue-300`;
  } else {
    return `${baseSize} bg-gray-100 border-gray-200`;
  }
};

const getStepNumberClass = (step: OnboardingStep) => {
  if (canAccessStep(step)) {
    return 'text-gray-600';
  } else {
    return 'text-gray-400';
  }
};

const getStepLabelClass = (step: OnboardingStep) => {
  if (step.completed) {
    return 'text-green-600';
  } else if (isCurrentStep(step)) {
    return 'text-blue-600';
  } else if (canAccessStep(step)) {
    return 'text-gray-700';
  } else {
    return 'text-gray-400';
  }
};

const getConnectorClass = (currentStep: OnboardingStep, nextStep: OnboardingStep) => {
  if (currentStep.completed) {
    return 'bg-green-300';
  } else {
    return 'bg-gray-200';
  }
};

const getStepIcon = (step: OnboardingStep): string => {
  // Map step types to icons
  const iconMap: Record<string, string> = {
    'create-organization': 'building-office',
    'create-store': 'storefront',
    'setup-products': 'cube',
    'import-customers': 'users',
    'create-warehouse': 'building-storefront',
    // Add more mappings as needed
  };
  
  return iconMap[step.id] || 'cog';
};

const getModuleIcon = (moduleId: string): string => {
  return ONBOARDING_MODULES[moduleId]?.icon || 'cube';
};

const getModuleName = (moduleId: string): string => {
  return ONBOARDING_MODULES[moduleId]?.name || moduleId;
};

const getModuleIconClass = (moduleId: string): string => {
  const color = ONBOARDING_MODULES[moduleId]?.color || 'gray';
  return `bg-${color}-100 text-${color}-600`;
};

const getModuleProgress = (steps: OnboardingStep[]): string => {
  const completed = steps.filter(s => s.completed).length;
  return `${completed}/${steps.length} hoàn thành`;
};
</script>

<style scoped>
/* Custom animations */
@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse {
  animation: pulse-blue 2s infinite;
}

/* Smooth transitions */
.stepper-navigation * {
  transition: all 0.2s ease-in-out;
}
</style>
