<template>
  <div class="min-h-screen bg-gray-100 flex">
    <!-- Sidebar Component -->
    <Sidebar :isSlimSidebar="isSlimSidebar" @toggleSidebar="toggleSidebar" />
    
    <!-- Main Content -->
    <div class="flex-1 p-8">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">
          Test Tooltip Sidebar
        </h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">
            <PERSON><PERSON><PERSON><PERSON> khiển Sidebar
          </h2>
          
          <div class="space-y-4">
            <div class="flex items-center space-x-4">
              <button
                @click="toggleSidebar"
                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {{ isSlimSidebar ? 'Mở rộng Sidebar' : 'Thu gọn <PERSON>' }}
              </button>
              
              <span class="text-gray-600">
                Trạng thái hiện tại: {{ isSlimSidebar ? 'Thu gọn' : 'Mở rộng' }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">
            Hướng dẫn kiểm tra Tooltip
          </h2>
          
          <div class="space-y-4">
            <div class="p-4 bg-blue-50 rounded-lg">
              <h3 class="font-medium text-blue-900 mb-2">
                1. Thu gọn Sidebar
              </h3>
              <p class="text-blue-800 text-sm">
                Nhấn nút "Thu gọn Sidebar" để chuyển sidebar sang chế độ slim.
              </p>
            </div>
            
            <div class="p-4 bg-green-50 rounded-lg">
              <h3 class="font-medium text-green-900 mb-2">
                2. Hover vào các menu items
              </h3>
              <p class="text-green-800 text-sm">
                Khi sidebar ở chế độ slim, hover chuột vào các menu items để xem tooltip xuất hiện bên phải.
              </p>
            </div>
            
            <div class="p-4 bg-purple-50 rounded-lg">
              <h3 class="font-medium text-purple-900 mb-2">
                3. Kiểm tra menu có children
              </h3>
              <p class="text-purple-800 text-sm">
                Hover vào menu "Báo cáo" để xem tooltip tương tác với các child items có thể click được.
              </p>
            </div>
            
            <div class="p-4 bg-yellow-50 rounded-lg">
              <h3 class="font-medium text-yellow-900 mb-2">
                4. Kiểm tra footer items
              </h3>
              <p class="text-yellow-800 text-sm">
                Hover vào các items ở footer như "Tổ chức", "Cửa hàng", và nút toggle để xem tooltip.
              </p>
            </div>

            <div class="p-4 bg-red-50 rounded-lg">
              <h3 class="font-medium text-red-900 mb-2">
                5. Tooltip nằm bên phải
              </h3>
              <p class="text-red-800 text-sm">
                Tất cả tooltip sẽ xuất hiện bên phải sidebar, không sử dụng v-tippy library nữa.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// State
const isSlimSidebar = ref(true); // Bắt đầu với slim sidebar để dễ test

// Functions
const toggleSidebar = () => {
  isSlimSidebar.value = !isSlimSidebar.value;
};

// Meta
definePageMeta({
  title: 'Test Tooltip Sidebar',
  description: 'Trang test tooltip cho sidebar'
});
</script>
