/**
 * Onboarding Preloader Plugin
 * 
 * Preloads onboarding components and assets for better performance
 */

export default defineNuxtPlugin(() => {
  // Only run on client side
  if (process.server) return;

  // Temporary disable to fix router error
  console.log('[Onboarding Preloader] Temporarily disabled');
  return;

  const router = useRouter();

  // Preload onboarding assets when user is likely to need them
  const preloadOnboardingAssets = async () => {
    try {
      // Preload core onboarding components
      const coreComponents = [
        () => import('~/components/onboarding/common/ProgressBar.vue'),
        () => import('~/components/onboarding/common/StepperNavigation.vue'),
        () => import('~/components/onboarding/common/OnboardingLayout.vue'),
      ];

      // Preload in background
      Promise.all(coreComponents.map(loader => loader())).catch(() => {
        // Silently fail - not critical
      });

      // Preload onboarding composable
      import('~/composables/useOnboarding').catch(() => {
        // Silently fail - not critical
      });

      // Preload module configuration
      import('~/config/onboarding-modules').catch(() => {
        // Silently fail - not critical
      });

    } catch (error) {
      // Silently fail - preloading is not critical
      console.debug('Onboarding preload failed:', error);
    }
  };

  // Preload module-specific components based on service type
  const preloadModuleComponents = async (serviceTypes: string[]) => {
    try {
      const moduleLoaders: (() => Promise<any>)[] = [];

      // Always preload organization components
      moduleLoaders.push(
        () => import('~/components/onboarding/modules/organization/CreateOrganization.vue')
      );

      // Preload based on service types
      if (serviceTypes.includes('POS') || serviceTypes.includes('SALEPOINT')) {
        moduleLoaders.push(
          () => import('~/components/onboarding/modules/pos/CreateStore.vue')
        );
      }

      if (serviceTypes.includes('CRM') || serviceTypes.includes('TELESALE')) {
        moduleLoaders.push(
          () => import('~/components/onboarding/modules/crm/ImportCustomers.vue')
        );
      }

      // Preload in background
      Promise.all(moduleLoaders.map(loader => loader())).catch(() => {
        // Silently fail - not critical
      });

    } catch (error) {
      console.debug('Module preload failed:', error);
    }
  };

  // Trigger preloading based on route
  // Add safety check for router
  if (router && typeof router.beforeEach === 'function') {
    router.beforeEach((to) => {
    // Preload when user is on registration pages
    if (to.path.startsWith('/register')) {
      // Delay preloading to not interfere with current page
      setTimeout(preloadOnboardingAssets, 1000);
    }

    // Preload when user is on onboarding pages
    if (to.path.startsWith('/onboarding')) {
      const serviceTypes = to.query.serviceTypes;
      if (serviceTypes) {
        try {
          const parsedServiceTypes = typeof serviceTypes === 'string' 
            ? JSON.parse(serviceTypes) 
            : serviceTypes;
          setTimeout(() => preloadModuleComponents(parsedServiceTypes), 500);
        } catch {
          // Invalid service types, use default
          setTimeout(() => preloadModuleComponents(['POS']), 500);
        }
      }
    }
    });
  } else {
    console.warn('[Onboarding Preloader] Router not available, skipping route-based preloading');
  }

  // Preload when user hovers over onboarding-related links
  const setupHoverPreloading = () => {
    // Wait for DOM to be ready
    setTimeout(() => {
      const onboardingLinks = document.querySelectorAll('a[href*="/onboarding"], a[href*="/register"]');
      
      onboardingLinks.forEach(link => {
        let preloadTriggered = false;
        
        link.addEventListener('mouseenter', () => {
          if (!preloadTriggered) {
            preloadTriggered = true;
            preloadOnboardingAssets();
          }
        }, { once: true });
      });
    }, 2000);
  };

  // Setup hover preloading
  setupHoverPreloading();

  // Preload critical onboarding assets on idle
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      // Only preload if user seems engaged (has been on site for a while)
      setTimeout(() => {
        // Check if user is still active
        if (document.hasFocus()) {
          preloadOnboardingAssets();
        }
      }, 10000); // After 10 seconds
    });
  }

  // Cache management for onboarding data
  const setupOnboardingCache = () => {
    // Cache onboarding configuration in sessionStorage
    const cacheOnboardingConfig = (userId: string, config: any) => {
      try {
        const cacheKey = `onboarding_cache_${userId}`;
        const cacheData = {
          config,
          timestamp: Date.now(),
          version: '1.0'
        };
        sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));
      } catch (error) {
        // Storage might be full or disabled
        console.debug('Failed to cache onboarding config:', error);
      }
    };

    const getCachedOnboardingConfig = (userId: string) => {
      try {
        const cacheKey = `onboarding_cache_${userId}`;
        const cached = sessionStorage.getItem(cacheKey);
        
        if (cached) {
          const cacheData = JSON.parse(cached);
          const isExpired = Date.now() - cacheData.timestamp > 30 * 60 * 1000; // 30 minutes
          
          if (!isExpired && cacheData.version === '1.0') {
            return cacheData.config;
          } else {
            // Remove expired cache
            sessionStorage.removeItem(cacheKey);
          }
        }
      } catch (error) {
        console.debug('Failed to get cached onboarding config:', error);
      }
      return null;
    };

    // Expose cache functions globally for use in composables
    window.__onboardingCache = {
      set: cacheOnboardingConfig,
      get: getCachedOnboardingConfig
    };
  };

  setupOnboardingCache();

  // Performance monitoring for onboarding
  const setupPerformanceMonitoring = () => {
    // Track onboarding page load times
    router.afterEach((to) => {
      if (to.path.startsWith('/onboarding')) {
        // Use Performance API to measure
        if ('performance' in window && 'mark' in performance) {
          performance.mark('onboarding-page-start');
          
          // Measure when page is fully loaded
          nextTick(() => {
            setTimeout(() => {
              try {
                performance.mark('onboarding-page-end');
                performance.measure('onboarding-page-load', 'onboarding-page-start', 'onboarding-page-end');
                
                const measures = performance.getEntriesByName('onboarding-page-load');
                if (measures.length > 0) {
                  const loadTime = measures[0].duration;
                  console.debug(`Onboarding page loaded in ${loadTime.toFixed(2)}ms`);
                  
                  // Send to analytics if needed
                  // analytics.track('onboarding_page_load_time', { duration: loadTime });
                }
              } catch (error) {
                console.debug('Performance measurement failed:', error);
              }
            }, 100);
          });
        }
      }
    });
  };

  setupPerformanceMonitoring();

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    // Clear any pending preload timers
    // This helps prevent memory leaks
  });

  return {
    provide: {
      onboardingPreloader: {
        preloadAssets: preloadOnboardingAssets,
        preloadModules: preloadModuleComponents
      }
    }
  };
});

// Type augmentation for global cache
declare global {
  interface Window {
    __onboardingCache?: {
      set: (userId: string, config: any) => void;
      get: (userId: string) => any;
    };
  }
}
