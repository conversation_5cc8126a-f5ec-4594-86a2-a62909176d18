<template>
  <div class="w-full max-w-full">
    <!-- Store Tabs - Mobile -->
    <div class="block md:hidden mb-2">
      <TabStoreOrder
        @setStore="handleSetStore"
        class="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300"
      />
    </div>

    <!-- Header Section - Desktop -->
    <div class="hidden md:flex flex-row justify-between items-center mb-3">
      <TabStoreOrder
        @setStore="handleSetStore"
        class="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300"
      />
      <div class="flex items-center gap-2">
        <TabChangeSearchOrder @toogleTab="handleSetTab" />
      </div>
    </div>

    <!-- Search Toggle - Mobile -->
    <div class="block md:hidden mb-2">
      <TabChangeSearchOrder @toogleTab="handleSetTab" />
    </div>

    <!-- Search Section -->
    <div
      class="bg-white rounded-lg border border-gray-200 shadow-sm p-3 md:p-4 mb-3"
    >
      <div class="space-y-3">
        <!-- Basic Search -->
        <div v-if="!isSearchDetail" class="space-y-2">
          <!-- Search Fields Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <!-- Order Code Search -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700">
                Mã đơn hàng
              </label>
              <div class="relative">
                <input
                  type="text"
                  placeholder="Nhập mã đơn hàng..."
                  class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                  v-model="keyword"
                  @keydown.enter="handleSearch"
                  :disabled="isSearching"
                />
                <button
                  v-if="keyword"
                  @click="keyword = ''"
                  class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    class="w-3 h-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Customer ID Search -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700">
                ID khách hàng
              </label>
              <div class="relative">
                <input
                  type="text"
                  placeholder="Nhập ID khách hàng..."
                  class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                  v-model="customerKeyWord"
                  @keydown.enter="handleSearch"
                  :disabled="isSearching"
                />
                <button
                  v-if="customerKeyWord"
                  @click="customerKeyWord = ''"
                  class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    class="w-3 h-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Date Range Picker -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700">
                Khoảng thời gian
              </label>
              <flat-pickr
                class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors text-center"
                placeholder="Chọn khoảng thời gian"
                :config="datePickerConfig"
                v-model="dateRange"
                :disabled="isSearching"
              />
            </div>

            <!-- Status Select -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700">
                Trạng thái
              </label>
              <InputField
                type="select"
                :defaultText="'Chọn trạng thái'"
                :selectClass="'w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors'"
                v-model="selectedStatus"
                :options="dataOrderStatus"
                @change="handleChangeStatus"
              />
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-2 pt-1">
            <button
              @click="reset"
              :disabled="isSearching"
              class="flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:ring-2 focus:ring-gray-500/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg
                class="w-3 h-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Xóa bộ lọc
            </button>
            <button
              @click="handleSearch"
              :disabled="isSearching"
              class="flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium text-white bg-primary border border-primary rounded-md hover:bg-primary/90 focus:ring-2 focus:ring-primary/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg
                v-if="!isSearching"
                class="w-3 h-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607Z"
                />
              </svg>
              <svg
                v-else
                class="w-3 h-3 animate-spin"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                />
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              {{ isSearching ? "Đang tìm..." : "Tìm kiếm" }}
            </button>
          </div>
        </div>

        <!-- Advanced Search -->
        <div v-if="isSearchDetail" class="space-y-2">
          <!-- Advanced Search Header -->
          <div class="flex items-center gap-2 pb-1 border-b border-gray-200">
            <svg
              class="w-4 h-4 text-primary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
              />
            </svg>
            <h3 class="text-sm font-semibold text-gray-900">
              Tìm kiếm chi tiết
            </h3>
          </div>

          <!-- Advanced Search Fields Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <!-- Order Code Search -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >Mã đơn hàng</label
              >
              <div class="relative">
                <input
                  type="text"
                  placeholder="Nhập mã đơn hàng..."
                  class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                  v-model="keyword"
                  @keydown.enter="handleSearch"
                  :disabled="isSearching"
                />
                <button
                  v-if="keyword"
                  @click="keyword = ''"
                  class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    class="w-3 h-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Customer ID Search -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >ID khách hàng</label
              >
              <div class="relative">
                <input
                  type="text"
                  placeholder="Nhập ID khách hàng..."
                  class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                  v-model="customerKeyWord"
                  @keydown.enter="handleSearch"
                  :disabled="isSearching"
                />
                <button
                  v-if="customerKeyWord"
                  @click="customerKeyWord = ''"
                  class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    class="w-3 h-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Product Search -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >Sản phẩm</label
              >
              <div class="relative">
                <input
                  type="text"
                  placeholder="Tên, mã sản phẩm, SKU..."
                  class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                  v-model="productKeyword"
                  @keydown.enter="handleSearch"
                  :disabled="isSearching"
                />
                <button
                  v-if="productKeyword"
                  @click="productKeyword = ''"
                  class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    class="w-3 h-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Date Range Picker -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >Khoảng thời gian</label
              >
              <flat-pickr
                class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors text-center"
                placeholder="Chọn khoảng thời gian"
                :config="datePickerConfig"
                v-model="dateRange"
                :disabled="isSearching"
              />
            </div>

            <!-- Status Select -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >Trạng thái đơn hàng</label
              >
              <InputField
                type="select"
                :defaultText="'Chọn trạng thái'"
                :selectClass="'w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors'"
                v-model="selectedStatus"
                :options="dataOrderStatus"
                @change="handleChangeStatus"
              />
            </div>

            <!-- Payment Method Select -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >Phương thức thanh toán</label
              >
              <InputField
                type="select"
                :defaultText="'Chọn phương thức'"
                :selectClass="'w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors'"
                v-model="selectedPaymentMethod"
                :options="dataPaymentMethod"
                @change="handleChangePaymentMethod"
              />
            </div>

            <!-- Employee Select -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >Nhân viên</label
              >
              <InputField
                type="select"
                :defaultText="'Chọn nhân viên'"
                :selectClass="'w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors'"
                v-model="selectedEmployee"
                :options="dataEmployee"
                @change="handleEmployeeChange"
              />
            </div>

            <!-- FFM Status Select -->
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700"
                >Trạng thái FFM</label
              >
              <InputField
                type="select"
                :defaultText="'Chọn trạng thái FFM'"
                :selectClass="'w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors'"
                v-model="selectedFfmStatus"
                :options="dataFFMStatus"
                @change="handleChangeFFMStatus"
              />
            </div>
          </div>

          <!-- Advanced Action Buttons -->
          <div
            class="flex flex-col sm:flex-row gap-2 pt-2 border-t border-gray-200"
          >
            <button
              @click="reset"
              :disabled="isSearching"
              class="flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:ring-2 focus:ring-gray-500/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg
                class="w-3 h-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Xóa tất cả bộ lọc
            </button>
            <button
              @click="handleSearch"
              :disabled="isSearching"
              class="flex items-center justify-center gap-1 px-4 py-2 text-sm font-medium text-white bg-primary border border-primary rounded-md hover:bg-primary/90 focus:ring-2 focus:ring-primary/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg
                v-if="!isSearching"
                class="w-3 h-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607Z"
                />
              </svg>
              <svg
                v-else
                class="w-3 h-3 animate-spin"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                />
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              {{ isSearching ? "Đang tìm..." : "Tìm kiếm chi tiết" }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import "tippy.js/dist/tippy.css";

import FlatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
import type { Options } from "flatpickr/dist/types/options"; // Đúng kiểu dữ liệu
const emits = defineEmits([
  "selectedDate",
  "selectedStaff",
  "changeKeyWord",
  "clearKeyword",
  "handleSearch",
]);
const dateRange = ref<string[]>([]);
const datePickerConfig = ref<Partial<Options>>({
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: (selectedDates: any) => {
    if (selectedDates.length === 2) {
      handleChangeDate(selectedDates[1].getTime(), selectedDates[0].getTime());
    }
  },
});
// Reactive state
const selectedPaymentMethod = ref("");
const isSearching = ref<boolean>(false);
const keyword = ref<string>("");
const selectedStatus = ref("");
const productKeyword = ref("");
const customerKeyWord = ref("");

// Props
const props = defineProps<{
  dataEmployee?: any[];
  dataOrderStatus?: any[];
  dataPaymentMethod?: any[];
}>();

// Search request data
const dataRequest = reactive({
  keyword: keyword.value,
  product_multi_value: productKeyword.value,
  customer_multi_value: customerKeyWord.value,
  date_create_to: "" as string | null,
  date_create_from: "" as string | null,
  employee_assign: "" as string | null,
  status: "" as string | null,
  ffm_status: "" as string | null,
  payment_method: "" as string | null,
  exist_ffm_status: "" as string | boolean,
});
// const dataFFM = ref([
//   { name: "Chưa ffm", value: "false" },
//   { name: "Đã ffm", value: "true" },
// ]);
const selectedFfmStatus = ref();
// tìm kiếm theo ffm
const dataFFMStatus = ref([
  { name: "Chưa FFM", value: "Chưa FFM" },
  { name: "Đã FFM", value: "Đã FFM" },
  { name: "Đang xử lý", value: "OPEN" },
  { name: "Đang chờ DVVC lấy hàng", value: "WAIT_DELIVERY" },
  { name: "Đang giao", value: "PROCESSING_DELIVERY" },
  { name: "Hoàn thành", value: "FULFILLED" },
  { name: "Đang trả hàng", value: "RETURNING" },
  { name: "Đã trả hàng", value: "RETURNED" },
  { name: "Hủy FFM", value: "UNFULFILLED" },
]);
const handleChangeFFMStatus = () => {
  if (selectedFfmStatus.value) {
    switch (selectedFfmStatus.value) {
      case "Chưa FFM":
      case "":
        console.log("Chưa FFM");
        dataRequest.exist_ffm_status = false;
        dataRequest.ffm_status = "";
        break;
      case "Đã FFM":
        console.log("Đã FFM");
        dataRequest.exist_ffm_status = true;
        dataRequest.ffm_status = "";
        break;
      default:
        dataRequest.exist_ffm_status = "";

        dataRequest.ffm_status = selectedFfmStatus.value;

        console.log("Sau có trạng thái");
    }
  }
};

// const handleChangeFFMStatus = async (ffmStatus) => {
//   switch (selectedFfmStatus.value) {
//     case "Chưa ffm":
//     case "":
//       options.exist_ffm_status = false;
//       options.ffm_status = "";
//       break;
//     case "Đã ffm":
//       options.ffm_status = "";
//       options.exist_ffm_status = true;
//       break;
//     case "Đang xử lý":
//       options.exist_ffm_status = "";
//       options.ffm_status = "OPEN";
//       break;
//     case "Đang chờ DVVC lấy hàng":
//       options.exist_ffm_status = "";
//       options.ffm_status = "WAIT_DELIVERY";
//       break;
//     case "Đang giao":
//       options.exist_ffm_status = "";

//       options.ffm_status = "PROCESSING_DELIVERY";
//       break;
//     case "Hoàn thành":
//       options.exist_ffm_status = "";

//       options.ffm_status = "FULFILLED";
//       break;
//     case "Đang trả hàng":
//       options.exist_ffm_status = "";

//       options.ffm_status = "RETURNING";
//       break;
//     case "Đã trả hàng":
//       options.exist_ffm_status = "";

//       options.ffm_status = "RETURNED";
//       break;
//     default:
//       break;
//   }
//   console.log("options.ffm_status", options.ffm_status);
// };

// Search functionality
const handleSearch = async () => {
  try {
    isSearching.value = true;
    emits("handleSearch", dataRequest);
  } catch (error) {
    console.error("Search error:", error);
  } finally {
    // Reset loading state after a short delay to show feedback
    setTimeout(() => {
      isSearching.value = false;
    }, 500);
  }
};

// Employee selection
const selectedEmployee = ref();
const handleEmployeeChange = () => {
  dataRequest.employee_assign = selectedEmployee.value;
  emits("handleSearch", dataRequest);
};
const handleChangeDate = (dateTo: string, dateFrom: string) => {
  dataRequest.date_create_to = dateTo;
  dataRequest.date_create_from = dateFrom;
  emits("handleSearch", dataRequest);
};
watch(keyword, (newKeyword) => {
  dataRequest.keyword = newKeyword;
});
watch(productKeyword, (newKeyword) => {
  dataRequest.product_multi_value = newKeyword;
});
watch(customerKeyWord, (newKeyword) => {
  dataRequest.customer_multi_value = newKeyword;
});
const reset = () => {
  selectedPaymentMethod.value = "";
  selectedStatus.value = "";
  dateRange.value = [];
  selectedEmployee.value = "";
  productKeyword.value = "";
  customerKeyWord.value = "";
  keyword.value = "";
  dataRequest.keyword = "";
  selectedFfmStatus.value = "";
  dataRequest.date_create_from = "";
  (dataRequest.ffm_status = ""), (dataRequest.exist_ffm_status = "");
  dataRequest.date_create_to = "";
  dataRequest.employee_assign = "";
  (dataRequest.customer_multi_value = ""),
    (dataRequest.payment_method = ""),
    (dataRequest.product_multi_value = "");
  dataRequest.status = "";
  dataRequest.exist_ffm_status = "";
  emits("handleSearch", dataRequest);
};
// Router setup
const router = useRouter();
router.beforeEach(async (to, _from, next) => {
  if (to.path === `/order`) {
    selectedEmployee.value = "";
  }
  next();
});
const isSearchDetail = ref(false);
const handleSetTab = (tab: string) => {
  if (tab === "DETAIL") {
    isSearchDetail.value = true;
  } else {
    isSearchDetail.value = false;
    (dataRequest.payment_method = ""), (dataRequest.product_multi_value = "");
  }
};
const handleChangeStatus = () => {
  if (selectedStatus.value === "Tất cả") {
    dataRequest.status = "";
    return;
  }
  dataRequest.status = selectedStatus.value;
};
const handleChangePaymentMethod = () => {
  dataRequest.payment_method = selectedPaymentMethod.value;
};
const { setSubStoreId } = useTabContext();

const handleSetStore = async (store: any) => {
  await setSubStoreId(store?.id);
  emits("handleSearch", dataRequest);
};
</script>
