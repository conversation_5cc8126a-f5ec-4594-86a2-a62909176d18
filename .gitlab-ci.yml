stages:
  - build
  - docker

variables:
  DOCKER_REGISTRY: containers-registry.longvan.vn/longvan-docker
  FF_USE_POD_ACTIVE_DEADLINE_SECONDS: "false"

build_app:
  stage: build
  image: node:20-alpine
  script:
    - npm config set legacy-peer-deps true
    - npm install
    - npm run build:$DEPLOY_ENV
  artifacts:
    paths:
      - .output/
  rules:
    - if: '$CI_COMMIT_REF_NAME == "development"'
      variables:
        DEPLOY_ENV: development
    - if: '$CI_COMMIT_REF_NAME == "production"'
      variables:
        DEPLOY_ENV: production
    - if: '$CI_COMMIT_REF_NAME == "pre-production"'
      variables:
        DEPLOY_ENV: pre-production
    - when: never

build_docker:
  stage: docker
  image: gcr.io/kaniko-project/executor:v1.22.0-debug
  script:
    - SHORT_SHA=$(echo "$CI_COMMIT_SHA" | cut -c1-7)
    - /kaniko/executor --context . --dockerfile Dockerfile --build-arg ENV=$DEPLOY_ENV --destination $DOCKER_REGISTRY/salepoint-service:$SHORT_SHA-$DEPLOY_ENV --cache=true
  rules:
    - if: '$CI_COMMIT_REF_NAME == "development"'
      variables:
        DEPLOY_ENV: development
    - if: '$CI_COMMIT_REF_NAME == "production"'
      variables:
        DEPLOY_ENV: production
    - if: '$CI_COMMIT_REF_NAME == "pre-production"'
      variables:
        DEPLOY_ENV: pre-production
    - when: never
  dependencies:
    - build_app
