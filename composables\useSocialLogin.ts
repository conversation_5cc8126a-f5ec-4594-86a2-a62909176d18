import { ref } from "vue";
import { useRouter } from "vue-router";
import { useNuxtApp } from "#app";
import { useAuthStore } from "@/stores/auth";

export default function useSocialLogin() {
  const $sdk = useNuxtApp().$sdk;
  const router = useRouter();
  const authStore = useAuthStore();
  const loading = ref(false);

  // Google Login
  const loginWithGoogle = async () => {
    loading.value = true;
    try {
      console.log("🔍 Attempting Google login...");

      // Preserve serviceType from current URL
      const route = useRoute();
      const serviceType = route.query.serviceType || route.query.service || '';
      const redirectUrl = serviceType
        ? `${window.location.origin}/auth/social-callback?serviceType=${serviceType}`
        : `${window.location.origin}/auth/social-callback`;

      console.log("🔗 Google redirect URL:", redirectUrl);
      console.log("📋 SDK orgId:", $sdk.orgId);
      // Set default LONGVAN org context for social login
      $sdk.setOrgId('LONGVAN');
      useCookie("orgId").value = 'LONGVAN';
      const response = await $sdk.auth.loginGoogle(redirectUrl);
      console.log("🚀 ~ loginWithGoogle ~ response:", response);

      if (response) {
        window.location.href = response;
      } else {
        throw new Error("Invalid Google login response");
      }
    } catch (error) {
      console.error("❌ Google login error:", error);
      useNuxtApp().$toast.error("Lỗi đăng nhập Google. Vui lòng thử lại.");
    } finally {
      loading.value = false;
    }
  };

  // Facebook Login
  const loginWithFacebook = async () => {
    loading.value = true;
    try {
      console.log("🔍 Attempting Facebook login...");

      // Use the correct method name from SDK: loginFacebook
      // Preserve serviceType from current URL
      const route = useRoute();
      const serviceType = route.query.serviceType || route.query.service || '';
      const redirectUrl = serviceType
        ? `${window.location.origin}/auth/social-callback?type=facebook&serviceType=${serviceType}`
        : `${window.location.origin}/auth/social-callback?type=facebook`;

      console.log("🔗 Calling loginFacebook with redirectUrl:", redirectUrl);

      const response = await $sdk.auth.loginFacebook(redirectUrl);

      console.log("✅ Facebook login response:", response);

      if (response && typeof response === 'string') {
        // Redirect to Facebook OAuth URL
        console.log("🚀 Redirecting to:", response);
        window.location.href = response;
      } else if (response) {
        console.log("⚠️ Unexpected response format:", response);
        useNuxtApp().$toast.error("Phản hồi từ Facebook không đúng định dạng");
      } else {
        throw new Error("No redirect URL received from Facebook login");
      }
    } catch (error: any) {
      console.error("❌ Facebook login error:", error);
      console.error("❌ Error details:", {
        message: error?.message,
        response: error?.response,
        graphQLErrors: error?.graphQLErrors
      });

      useNuxtApp().$toast.error("Lỗi đăng nhập Facebook. Vui lòng thử lại.");
    } finally {
      loading.value = false;
    }
  };

  // Zalo Login
  const loginWithZalo = async () => {
    loading.value = true;
    try {
      console.log("🔍 Attempting Zalo login...");

      // Use the correct method name from SDK: loginZalo
      // Preserve serviceType from current URL
      const route = useRoute();
      const serviceType = route.query.serviceType || route.query.service || '';
      const redirectUrl = serviceType
        ? `${window.location.origin}/auth/social-callback?type=zalo&serviceType=${serviceType}`
        : `${window.location.origin}/auth/social-callback?type=zalo`;

      console.log("🔗 Calling loginZalo with redirectUrl:", redirectUrl);
      console.log("🔧 SDK orgId:", $sdk.orgId);

      const response = await $sdk.auth.loginZalo(redirectUrl);

      console.log("✅ Zalo login response:", response);
      console.log("📋 Response type:", typeof response);

      if (response && typeof response === 'string') {
        // Redirect to Zalo OAuth URL
        console.log("🚀 Redirecting to:", response);
        window.location.href = response;
      } else if (response) {
        console.log("⚠️ Unexpected response format:", response);
        useNuxtApp().$toast.error("Phản hồi từ Zalo không đúng định dạng");
      } else {
        throw new Error("No redirect URL received from Zalo login");
      }
    } catch (error: any) {
      console.error("❌ Zalo login error:", error);
      console.error("❌ Error details:", {
        message: error?.message,
        response: error?.response,
        graphQLErrors: error?.graphQLErrors,
        networkError: error?.networkError
      });

      // More specific error messages
      if (error?.message?.includes('GraphQL')) {
        useNuxtApp().$toast.error("Lỗi kết nối server. Vui lòng thử lại sau.");
      } else if (error?.message?.includes('orgId')) {
        useNuxtApp().$toast.error("Lỗi cấu hình tổ chức. Vui lòng liên hệ admin.");
      } else {
        useNuxtApp().$toast.error("Lỗi đăng nhập Zalo. Vui lòng thử lại.");
      }
    } finally {
      loading.value = false;
    }
  };

  // Handle social login response
  const handleSocialLoginResponse = async (response: any, provider: string) => {
    if (response && response.accessToken) {
      // Set token and user data
      const { setToken } = useAuth();
      setToken(response.accessToken);

      const user = {
        id: response.partyId || response.id,
        name: response.fullName || response.name,
        email: response.email || "",
        phone: response.phone || "",
        avatar: response.avatarUrl || response.avatar || "",
        birthDate: response.birthDate || "",
        // roles will be set when user selects org/store
      };

      authStore.setUser(user);

      useNuxtApp().$toast.success(`Đăng nhập ${provider} thành công!`);
      router.push("/org-store");
    } else {
      throw new Error(`Invalid response from ${provider} login`);
    }
  };

  // Note: Social login methods now directly redirect to OAuth providers
  // The actual login handling happens in the callback pages

  // Initialize and check available methods

  return {
    loading,
    loginWithGoogle,
    loginWithFacebook,
    loginWithZalo,
  };
}
