<template>
  <div class="create-store-step">
    <!-- Header -->
    <div class="text-center mb-8">
      <div
        class="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4"
      >
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path
            fill-rule="evenodd"
            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
            clip-rule="evenodd"
          />
        </svg>
      </div>
      <h2 class="text-2xl font-bold text-gray-800 mb-2">Tạo Cửa Hàng</h2>
      <p class="text-gray-600 max-w-md mx-auto">
        Thiế<PERSON> lập thông tin cửa hàng đầu tiên đ<PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> b<PERSON> hàng
      </p>
    </div>

    <!-- Organization Info -->
    <div
      v-if="organizationData"
      class="max-w-2xl mx-auto mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200"
    >
      <div class="flex items-center space-x-3">
        <div
          class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"
        >
          <svg
            class="w-5 h-5 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div>
          <h3 class="font-medium text-blue-900">
            {{ organizationData.orgName }}
          </h3>
          <p class="text-sm text-blue-700">Tạo cửa hàng cho tổ chức này</p>
        </div>
      </div>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="max-w-2xl mx-auto space-y-6">
      <!-- Store Name -->
      <div class="space-y-2">
        <label
          for="storeName"
          class="block text-sm font-semibold text-gray-700"
        >
          Tên cửa hàng <span class="text-red-500">*</span>
        </label>
        <div class="relative">
          <div
            class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10"
          >
            <svg
              class="h-5 w-5 text-gray-400 transition-colors duration-200"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <input
            id="storeName"
            v-model="formData.storeName"
            type="text"
            :class="[
              'block w-full pl-10 pr-4 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
              errors.storeName
                ? 'border-red-300 bg-red-50'
                : 'border-gray-300 bg-white hover:border-gray-400',
            ]"
            placeholder="Nhập tên cửa hàng của bạn"
            @blur="validateField('storeName')"
          />
        </div>

        <span v-if="errors.storeName" class="text-sm text-red-600">{{
          errors.storeName
        }}</span>
      </div>

      <!-- Store Type -->
      <div class="space-y-2">
        <label class="block text-sm font-semibold text-gray-700">
          Loại cửa hàng <span class="text-red-500">*</span>
        </label>
        <div class="grid grid-cols-2 gap-3">
          <label
            v-for="type in storeTypes"
            :key="type.value"
            class="relative flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200"
            :class="
              formData.storeType === type.value
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300'
            "
          >
            <input
              v-model="formData.storeType"
              type="radio"
              :value="type.value"
              class="sr-only"
              @change="validateField('storeType')"
            />
            <div class="flex items-center">
              <div
                class="w-4 h-4 rounded-full border-2 flex items-center justify-center"
                :class="
                  formData.storeType === type.value
                    ? 'border-green-500'
                    : 'border-gray-300'
                "
              >
                <div
                  v-if="formData.storeType === type.value"
                  class="w-2 h-2 bg-green-500 rounded-full"
                ></div>
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900">
                  {{ type.label }}
                </div>
                <div class="text-xs text-gray-500">{{ type.description }}</div>
              </div>
            </div>
          </label>
        </div>
        <span v-if="errors.storeType" class="text-sm text-red-600">{{
          errors.storeType
        }}</span>
      </div>

      <!-- Progress Indicator -->
      <div
        v-if="loading"
        class="mt-6 p-4 bg-green-50 rounded-lg border border-green-200"
      >
        <div class="flex items-center space-x-3">
          <div
            class="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600"
          ></div>
          <span class="text-sm font-medium text-green-800">
            Đang tạo cửa hàng...
          </span>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import type { StepComponentProps, StepResult } from "~/types/onboarding";

// Props
const props = defineProps<StepComponentProps>();

// Emits
const emit = defineEmits<{
  "update:canProceed": [value: boolean];
}>();

// Form data
const formData = reactive({
  storeName: "",
  storeType: "pos", // Default là POS
});

// Form validation
const errors = reactive({
  storeName: "",
  storeType: "",
});

// Loading state
const loading = ref(false);

// Input focus state
const isInputFocused = ref(false);

// Store types - kênh bán hàng thực tế (chỉ POS và Website)
const storeTypes = [
  { value: "pos", label: "POS", description: "Điểm bán hàng vật lý" },
  { value: "website", label: "Website", description: "Website bán hàng riêng" },
  // Tạm thời comment các loại khác
  // { value: 'shopee', label: 'Shopee', description: 'Sàn thương mại điện tử Shopee' },
  // { value: 'lazada', label: 'Lazada', description: 'Sàn thương mại điện tử Lazada' },
  // { value: 'tiki', label: 'Tiki', description: 'Sàn thương mại điện tử Tiki' },
  // { value: 'tiktok', label: 'TikTok Shop', description: 'Bán hàng qua TikTok Shop' },
  // { value: 'nhanh', label: 'Nhanh.vn', description: 'Hệ thống quản lý bán hàng' },
  // { value: 'pancake', label: 'Pancake', description: 'Platform bán hàng Pancake' }
];

// Get organization data from previous steps
const organizationData = computed(() => {
  const createOrgStep = props.context.config.steps.find(
    (s) => s.id === "create-organization"
  );
  return createOrgStep?.data || props.context.organizationData;
});

// Auto-generate store name based on organization + store type
const generateStoreName = () => {
  if (organizationData.value?.orgName && formData.storeType) {
    const storeTypeLabel =
      storeTypes.find((t) => t.value === formData.storeType)?.label ||
      formData.storeType;
    formData.storeName = `${organizationData.value.orgName} - ${storeTypeLabel}`;
  }
};

// Validation functions
const validateField = (field: string) => {
  switch (field) {
    case "storeName":
      if (!formData.storeName.trim()) {
        errors.storeName = "Tên cửa hàng không được để trống";
      } else if (formData.storeName.trim().length < 2) {
        errors.storeName = "Tên cửa hàng phải có ít nhất 2 ký tự";
      } else {
        errors.storeName = "";
      }
      break;
    case "storeType":
      if (!formData.storeType) {
        errors.storeType = "Vui lòng chọn kênh bán hàng";
      } else {
        errors.storeType = "";
      }
      break;
  }
  updateCanProceed();
};

const validateForm = (): boolean => {
  validateField("storeName");
  validateField("storeType");

  return !errors.storeName && !errors.storeType;
};

const updateCanProceed = () => {
  const isValid = formData.storeName.trim().length >= 2 && !!formData.storeType;
  emit("update:canProceed", isValid);
};

// Handle input focus
const handleInputFocus = () => {
  isInputFocused.value = true;
};

// Handle input blur with focus state
const handleInputBlur = () => {
  isInputFocused.value = false;
  validateField("storeName");
};

// Submit handler
const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  loading.value = true;

  try {
    const $sdk = useNuxtApp().$sdk;

    console.log("🏪 Creating store...");
    console.log("🔧 SDK token:", $sdk.token);
    console.log("🍪 Cookie token:", useCookie("token").value);

    // Re-set token if SDK token is null but cookie has token
    const cookieToken = useCookie("token").value;
    if (!$sdk.token && cookieToken) {
      console.log("🔄 Re-setting token from cookie to SDK...");
      $sdk.setToken(cookieToken);
      console.log("✅ Token re-set to SDK:", $sdk.token);
    }

    // Create store using SDK
    console.log("📞 Calling createStoreChannel with:", {
      storeName: formData.storeName,
      type: formData.storeType,
      id: "",
      sdkToken: $sdk.token,
    });

    const storeResult = await $sdk.store.createStoreChannel({
      storeName: formData.storeName,
      type: formData.storeType,
    });

    console.log("📦 Store creation response:", storeResult);

    // Handle response structure: { status: 1, data: { id: "1244", ... } }
    const storeId = storeResult?.data?.id || storeResult?.id;

    if (!storeId || storeResult?.status !== 1) {
      console.error("❌ Store creation failed:", storeResult);
      throw new Error("Không thể tạo cửa hàng. Vui lòng thử lại.");
    }

    console.log("✅ Store created with ID:", storeId);

    // Set store context
    useCookie("storeId").value = storeId;

    // Complete step
    const result: StepResult = {
      stepId: props.step.id,
      success: true,
      data: {
        storeId,
        storeResult: storeResult?.data || storeResult, // Pass the actual store data
        formData: { ...formData },
      },
    };

    // Don't show toast here - parent component will handle it
    props.onComplete(result);
  } catch (error: any) {
    console.error("❌ Error creating store:", error);
    useNuxtApp().$toast.error(
      error?.message || "Có lỗi xảy ra khi tạo cửa hàng"
    );
  } finally {
    loading.value = false;
  }
};

// Watch form changes
watch(formData, updateCanProceed, { deep: true });

// Watch store type changes to auto-generate store name
watch(
  () => formData.storeType,
  () => {
    generateStoreName();
  }
);

// Initialize
onMounted(() => {
  console.log("🏪 [CREATE STORE] Component mounted!");
  console.log("🏪 [CREATE STORE] Step data:", props.step);
  console.log("🏪 [CREATE STORE] Context:", props.context);

  // Ensure SDK has token from cookie
  const $sdk = useNuxtApp().$sdk;
  const cookieToken = useCookie("token").value;

  if (cookieToken && !$sdk.token) {
    console.log("🔄 Syncing token from cookie to SDK on mount...");
    $sdk.setToken(cookieToken);
    console.log("✅ Token synced:", $sdk.token);
  }

  // Load existing data if available
  if (props.step.data) {
    Object.assign(formData, props.step.data.formData || {});
  } else {
    // Auto-generate initial store name
    generateStoreName();
  }
  updateCanProceed();
});
</script>

<style scoped>
/* Custom animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Form transitions */
.create-store-step input,
.create-store-step textarea,
.create-store-step select {
  transition: all 0.2s ease-in-out;
}

.create-store-step input:focus,
.create-store-step textarea:focus,
.create-store-step select:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
}
</style>
