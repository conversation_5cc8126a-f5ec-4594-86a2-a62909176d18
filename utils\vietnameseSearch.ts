/**
 * Vietnamese text search utility
 * Handles diacritics normalization for proper Vietnamese text filtering
 */

/**
 * Remove Vietnamese diacritics from text
 * @param text - Text to normalize
 * @returns Normalized text without diacritics
 */
export const removeVietnameseDiacritics = (text: string): string => {
  if (!text) return "";
  
  const diacriticsMap: Record<string, string> = {
    // Lowercase vowels
    'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
    'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
    'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
    'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
    'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
    'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
    'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
    'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
    'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
    'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
    'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
    'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
    'đ': 'd',
    
    // Uppercase vowels
    'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
    'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
    'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
    'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
    'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
    'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
    'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
    'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
    'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
    'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
    'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
    'Ỳ': 'Y', 'Ý': 'Y', 'Ỵ': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y',
    'Đ': 'D'
  };

  return text.replace(/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/g, (match) => diacriticsMap[match] || match);
};

/**
 * Normalize text for search (remove diacritics and convert to lowercase)
 * @param text - Text to normalize
 * @returns Normalized text for search
 */
export const normalizeForSearch = (text: string): string => {
  return removeVietnameseDiacritics(text).toLowerCase().trim();
};

/**
 * Check if search query matches text (Vietnamese-aware)
 * @param text - Text to search in
 * @param query - Search query
 * @returns True if query matches text
 */
export const vietnameseTextMatch = (text: string, query: string): boolean => {
  if (!text || !query) return false;
  
  const normalizedText = normalizeForSearch(text);
  const normalizedQuery = normalizeForSearch(query);
  
  return normalizedText.includes(normalizedQuery);
};

/**
 * Filter array of objects by Vietnamese text search
 * @param items - Array of items to filter
 * @param query - Search query
 * @param searchFields - Fields to search in (e.g., ['geoName', 'name'])
 * @returns Filtered array
 */
export const filterVietnameseText = <T extends Record<string, any>>(
  items: T[],
  query: string,
  searchFields: string[]
): T[] => {
  if (!query || !query.trim()) return items;
  
  const normalizedQuery = normalizeForSearch(query);
  
  return items.filter(item => 
    searchFields.some(field => {
      const fieldValue = item[field];
      if (typeof fieldValue === 'string') {
        return normalizeForSearch(fieldValue).includes(normalizedQuery);
      }
      return false;
    })
  );
};

/**
 * Highlight matching text in Vietnamese text
 * @param text - Original text
 * @param query - Search query
 * @returns Text with highlighted matches
 */
export const highlightVietnameseText = (text: string, query: string): string => {
  if (!text || !query) return text;
  
  const normalizedText = normalizeForSearch(text);
  const normalizedQuery = normalizeForSearch(query);
  
  if (!normalizedText.includes(normalizedQuery)) return text;
  
  // Find the position of the match in the normalized text
  const matchIndex = normalizedText.indexOf(normalizedQuery);
  if (matchIndex === -1) return text;
  
  // Extract the matching part from the original text
  const beforeMatch = text.substring(0, matchIndex);
  const matchingPart = text.substring(matchIndex, matchIndex + query.length);
  const afterMatch = text.substring(matchIndex + query.length);
  
  return `${beforeMatch}<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">${matchingPart}</mark>${afterMatch}`;
};

/**
 * Debounce function for search input
 * @param func - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};
