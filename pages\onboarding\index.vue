<template>
  <OnboardingLayout
    :current-step="currentStep"
    :current-step-id="currentStepId"
    :steps="steps"
    :progress="progress"
    :navigation-options="navigationOptions"
    :loading="loading"
    :can-proceed="canProceed"
    @back="handleBack"
    @next="handleNext"
    @skip="handleSkip"
    @step-click="handleStepClick"
    @exit="handleExit"
  >
    <!-- Step Content -->
    <template #content>
      <component
        v-if="currentStepComponent"
        :is="currentStepComponent"
        :key="currentStepId"
        :step="currentStep"
        :context="context"
        ref="stepComponentRef"
        @complete="handleStepComplete"
        @skip="handleSkip"
        @back="handleBack"
        @update:can-proceed="canProceed = $event"
      />
      
      <!-- Fallback content -->
      <div
        v-else
        class="text-center py-12"
      >
        <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Không tìm thấy component
        </h3>
        <p class="text-gray-600 mb-4">
          Component cho bước "{{ currentStep?.title }}" chưa được tạo.
        </p>
        <button
          @click="handleSkip"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
        >
          Bỏ qua bước này
        </button>
      </div>
    </template>

    <!-- Help Content -->
    <template #help>
      <div class="space-y-4">
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Hướng dẫn chung</h4>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• Hoàn thành từng bước theo thứ tự</li>
            <li>• Có thể bỏ qua các bước không bắt buộc</li>
            <li>• Tiến trình sẽ được lưu tự động</li>
            <li>• Có thể quay lại chỉnh sửa sau</li>
          </ul>
        </div>
        
        <div v-if="currentStep">
          <h4 class="font-medium text-gray-900 mb-2">Bước hiện tại</h4>
          <div class="bg-blue-50 rounded-lg p-3">
            <h5 class="font-medium text-blue-900">{{ currentStep.title }}</h5>
            <p class="text-sm text-blue-700 mt-1">{{ currentStep.description }}</p>
            
          </div>
        </div>

        <div>
          <h4 class="font-medium text-gray-900 mb-2">Cần hỗ trợ?</h4>
          <p class="text-sm text-gray-600 mb-3">
            Liên hệ với chúng tôi nếu bạn gặp khó khăn
          </p>
          <div class="space-y-2">
            <a
              href="tel:1900-xxx-xxx"
              class="flex items-center text-sm text-blue-600 hover:text-blue-800"
            >
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              1900-xxx-xxx
            </a>
            <a
              href="mailto:<EMAIL>"
              class="flex items-center text-sm text-blue-600 hover:text-blue-800"
            >
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </template>
  </OnboardingLayout>
</template>

<script setup lang="ts">
import type { StepResult, ServiceType } from '~/types/onboarding';

// Meta
definePageMeta({
  // middleware: 'auth', // Temporarily disabled - user comes from registration flow
  layout: false
});

// Composables
const { 
  config,
  currentStep,
  loading,
  error,
  context,
  progress,
  navigationOptions,
  initializeOnboarding,
  loadOnboardingConfig,
  goToStep,
  completeStep,
  goToNextStep,
  goToPreviousStep,
  skipStep,
  getStepComponent
} = useOnboarding();

const route = useRoute();
const router = useRouter();
const { $toast } = useNuxtApp();

// State
const canProceed = ref(true);
const currentStepComponent = ref<any>(null);
const stepComponentRef = ref<any>(null);

// Computed
const currentStepId = computed(() => config.value?.currentStepId || '');
const steps = computed(() => config.value?.steps || []);

// Initialize onboarding
onMounted(async () => {
  try {
    const userId = route.query.userId as string;
    const serviceType = route.query.serviceType as string;

    if (!userId) {
      $toast.error('Thiếu thông tin user ID');
      router.push('/login');
      return;
    }

    // Parse service type (single value)
    const serviceTypes: ServiceType[] = serviceType ? [serviceType as ServiceType] : ['POS'];

    // Try to load existing config first
    const existingConfig = await loadOnboardingConfig(userId);

    if (!existingConfig) {
      // Initialize new onboarding
      await initializeOnboarding(userId, serviceTypes);
    }

    // Load current step component
    await loadCurrentStepComponent();

  } catch (err: any) {
    console.error('Error initializing onboarding:', err);
    $toast.error(err.message || 'Lỗi khởi tạo onboarding');
  }
});

// Watch for step changes
watch(currentStep, async () => {
  // Ensure SDK token is synced on every step change
  const $sdk = useNuxtApp().$sdk;
  const cookieToken = useCookie('token').value;

  if (cookieToken && !$sdk.token) {
    console.log('🔄 Syncing token from cookie to SDK on step change...');
    $sdk.setToken(cookieToken);
    console.log('✅ Token synced for step:', currentStep.value?.id);
  }

  await loadCurrentStepComponent();
});

// Methods
const loadCurrentStepComponent = async () => {
  if (!currentStep.value) {
    console.log('🔍 [COMPONENT LOADER] No current step');
    currentStepComponent.value = null;
    return;
  }

  console.log('🔍 [COMPONENT LOADER] Loading component for step:', {
    stepId: currentStep.value.id,
    componentName: currentStep.value.component,
    module: currentStep.value.module
  });

  try {
    const componentLoader = getStepComponent(currentStep.value.component);
    console.log('🔍 [COMPONENT LOADER] Component loader found:', !!componentLoader);

    if (componentLoader) {
      const module = await componentLoader();
      currentStepComponent.value = module.default || module;
      console.log('🔍 [COMPONENT LOADER] Component loaded successfully:', currentStep.value.component);
    } else {
      console.warn(`🚨 [COMPONENT LOADER] Component not found: ${currentStep.value.component}`);
      currentStepComponent.value = null;
    }
  } catch (err) {
    console.error('🚨 [COMPONENT LOADER] Error loading step component:', err);
    currentStepComponent.value = null;
  }
};

const handleBack = async () => {
  try {
    await goToPreviousStep();
  } catch (err: any) {
    $toast.error(err.message || 'Lỗi quay lại bước trước');
  }
};

const handleNext = async () => {
  console.log('🚀 handleNext called!');

  if (!canProceed.value) {
    $toast.warning('Vui lòng hoàn thành thông tin bắt buộc');
    return;
  }

  try {
    // Check if current step component has handleContinue method
    const componentInstance = stepComponentRef.value;
    if (componentInstance && typeof componentInstance.handleContinue === 'function') {
      console.log('📝 Found handleContinue method, calling it...');
      await componentInstance.handleContinue();
      return;
    }

    // Fallback: Trigger form submit for current step component
    console.log('📝 Triggering form submit for current step...');
    const formElement = document.querySelector('form');
    if (formElement) {
      console.log('📋 Found form, triggering submit...');
      formElement.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
    } else {
      console.log('⚠️ No form found, going to next step directly...');
      await goToNextStep();
    }
  } catch (err: any) {
    console.error('❌ Error in handleNext:', err);
    $toast.error(err.message || 'Lỗi chuyển bước tiếp theo');
  }
};

const handleSkip = async () => {
  try {
    await skipStep();
  } catch (err: any) {
    $toast.error(err.message || 'Lỗi bỏ qua bước');
  }
};

const handleStepClick = async (stepId: string) => {
  try {
    await goToStep(stepId);
  } catch (err: any) {
    $toast.error(err.message || 'Lỗi chuyển đến bước');
  }
};

const handleStepComplete = async (result: StepResult) => {
  try {
    await completeStep(result);
  } catch (err: any) {
    $toast.error(err.message || 'Lỗi hoàn thành bước');
  }
};

const handleExit = () => {
  // Confirm before exit
  if (confirm('Bạn có chắc muốn thoát? Tiến trình sẽ được lưu lại.')) {
    router.push('/org-store');
  }
};



// Error handling
watch(error, (newError) => {
  if (newError) {
    $toast.error(newError);
  }
});
</script>
