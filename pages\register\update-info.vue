<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -inset-10 opacity-30">
        <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="relative w-full max-w-lg mx-auto">
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6">
        <!-- Compact Header -->
        <div class="text-center mb-6">
          <div class="w-12 h-12 bg-primary rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <h2 class="text-xl font-bold text-gray-800 mb-2">Hoàn tất đăng ký</h2>
          <div class="flex items-center justify-center space-x-2 text-sm">
            <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
              ✓ Google
            </span>
            <span class="text-gray-400">→</span>
            <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
              Thông tin
            </span>
          </div>
        </div>

        <!-- Streamlined Form -->
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- Compact Name Field -->
          <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <p class="text-xs text-gray-500 mb-1">Họ và tên từ Google</p>
                <p class="font-medium text-gray-900">{{ customerName }}</p>
              </div>
            </div>
          </div>

          <!-- Streamlined Phone Field -->
          <div class="space-y-3">
            <label for="phoneNumber" class="block text-sm font-medium text-gray-700">
              Số điện thoại <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </div>
              <input
                id="phoneNumber"
                v-model="phoneNumber"
                type="tel"
                name="phoneNumber"
                autocomplete="tel"
                @blur="validateFieldOnBlur('phoneNumber')"
                :class="[
                  'block w-full pl-9 pr-3 py-2.5 border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                  errors.phoneNumber
                    ? 'border-red-300 bg-red-50'
                    : phoneExists
                    ? 'border-amber-300 bg-amber-50'
                    : 'border-gray-300 bg-white hover:border-gray-400',
                ]"
                placeholder="Nhập số điện thoại của bạn"
              />
              <!-- Status Indicator -->
              <div v-if="phoneExists" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-4 w-4 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
            </div>

            <!-- Error Message -->
            <Transition name="slide-down">
              <div v-if="errors.phoneNumber" class="text-sm text-red-600 flex items-center space-x-1">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ errors.phoneNumber }}</span>
              </div>
            </Transition>
          </div>

          <!-- Compact Phone Exists Alert -->
          <Transition name="slide-down">
            <div v-if="phoneExists" class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-4">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="font-medium text-amber-800">Số này đã được đăng ký</h4>
                  <p class="text-sm text-amber-700">
                    <strong>{{ phoneNumber }}</strong> đã tồn tại. Đây có phải tài khoản của bạn?
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-2">
                <button
                  type="button"
                  @click="handleAccountVerification"
                  class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  ✓ Đúng, là tôi
                </button>
                <button
                  type="button"
                  @click="handleNotMyAccount"
                  :disabled="loading"
                  class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium disabled:opacity-50"
                >
                  <span v-if="loading" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Đang lưu...
                  </span>
                  <span v-else>
                    ✓ Không phải - Tiếp tục
                  </span>
                </button>
              </div>
            </div>
          </Transition>

          <!-- Smart Submit Button -->
          <div class="space-y-3">
            <!-- Main Submit Button -->
            <button
              v-if="!phoneExists"
              type="submit"
              :disabled="loading || !isFormValid"
              class="w-full flex justify-center items-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <span v-if="loading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang xử lý...
              </span>
              <span v-else class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
                Tiếp tục đăng ký
              </span>
            </button>


          </div>
        </form>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div v-if="loading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';

useHead({
  title: "Cập nhật thông tin",
  meta: [
    { name: "description", content: "Cập nhật thông tin để hoàn tất đăng ký" }
  ]
});

const route = useRoute();
const router = useRouter();

// Extract info from query params
const accessToken = ref((route.query.accessToken as string) || "");
const registrationType = ref((route.query.type as string) || "NORMAL");

// Debug: Check token userDetail on mount
onMounted(async () => {
  if (accessToken.value) {
    try {
      const $sdk = useNuxtApp().$sdk;

      // Ensure orgId context is set
      if (!$sdk.orgId || $sdk.orgId === 'N/A') {
        console.log('🔧 Setting default orgId context for social login...');
        $sdk.setOrgId('LONGVAN');
        useCookie("orgId").value = 'LONGVAN';
        console.log('✅ OrgId context set:', $sdk.orgId);
      }

      $sdk.setToken(accessToken.value);

      console.log("🔍 [Update-Info] Checking token userDetail...");
      const userDetail = await $sdk.auth.getUserDetail(accessToken.value);
      console.log("👤 [Update-Info] UserDetail from token:", userDetail);
      console.log("📋 [Update-Info] UserDetail details:", {
        partyId: userDetail.partyId,
        fullName: userDetail.fullName,
        phone: userDetail.phone,
        email: userDetail.email,
        hasPhone: !!userDetail.phone,
        orgPositionsMap: userDetail.orgPositionsMap
      });

      if (userDetail) {
        console.log("✅ [Update-Info] Token has userDetail:", {
          partyId: userDetail.partyId,
          fullName: userDetail.fullName,
          phone: userDetail.phone,
          email: userDetail.email,
          hasOrgPositionsMap: !!userDetail.orgPositionsMap
        });

        // Pre-fill form if userDetail has data
        if (userDetail.fullName && !customerName.value) {
          customerName.value = userDetail.fullName;
          console.log("📝 Pre-filled customerName from userDetail");
        }

        if (userDetail.phone && !phoneNumber.value) {
          phoneNumber.value = userDetail.phone;
          console.log("📝 Pre-filled phoneNumber from userDetail");

          // If user already has phone, they might be fully registered
          // Just need to set auth state and redirect to onboarding
          if (userDetail.phone && userDetail.fullName) {
            console.log("🎯 User already has complete profile, setting auth state...");

            // Set auth store
            const authStore = useAuthStore();
            const user = {
              id: userDetail.partyId,
              name: userDetail.fullName,
              email: userDetail.email || "",
              phone: userDetail.phone,
              avatar: userDetail.avatarUrl || "",
              birthDate: userDetail.birthDate || "",
            };
            authStore.setUser(user);

            // Set token and org data
            useCookie('token').value = accessToken.value;
            if (userDetail.orgPositionsMap) {
              useCookie("dataOrg").value = JSON.stringify(userDetail.orgPositionsMap);
            }

            console.log("✅ Auth state set, redirecting to onboarding...");

            // Get serviceType from URL
            const serviceType = route.query.serviceType || 'POS';

            // Redirect to onboarding
            router.push(`/onboarding?userId=${userDetail.partyId}&serviceType=${serviceType}`);
            return;
          }
        }
      } else {
        console.log("❌ [Update-Info] Token does not have userDetail");
      }
    } catch (error) {
      console.error("❌ [Update-Info] Error getting userDetail from token:", error);
    }
  }
});

// Form state
const customerName = ref("");
const phoneNumber = ref("");
const loading = ref(false);
const phoneExists = ref(false);

// Error states
const errors = ref({
  phoneNumber: "",
});

// Validation
const validatePhoneNumber = (phone: string): string => {
  if (!phone || !phone.trim()) {
    return "Số điện thoại không được để trống";
  }
  
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check if it's a valid Vietnamese phone number
  if (!/^(0[3|5|7|8|9])[0-9]{8}$/.test(cleanPhone)) {
    return "Số điện thoại không hợp lệ (VD: 0901234567)";
  }
  
  return "";
};

const validateFieldOnBlur = (fieldName: string) => {
  switch (fieldName) {
    case 'phoneNumber':
      errors.value.phoneNumber = validatePhoneNumber(phoneNumber.value);
      // Check if phone exists when valid
      if (!errors.value.phoneNumber) {
        checkPhoneExists();
      }
      break;
  }
};

// Form validation
const isFormValid = computed(() => {
  return customerName.value.trim() && 
         phoneNumber.value.trim() && 
         !validatePhoneNumber(phoneNumber.value);
});

// Check if phone number already exists
const checkPhoneExists = async () => {
  if (!phoneNumber.value.trim()) return;

  try {
    const $sdk = useNuxtApp().$sdk;
    const response = await $sdk.auth.checkUserLogin(phoneNumber.value);

    // Handle different response formats
    // Could be: true/false, {exists: true}, or {phone: "xxx", exists: true}
    phoneExists.value = response === true || response?.exists === true;

    console.log('📱 Phone check result:', { phone: phoneNumber.value, response, exists: phoneExists.value });

    if (phoneExists.value) {
      useNuxtApp().$toast.warning(`Số điện thoại ${phoneNumber.value} đã được đăng ký`);
    }
  } catch (error) {
    console.error('❌ Error checking phone:', error);
    phoneExists.value = false;
  }
};

// Handle account verification (user confirms it's their account)
const handleAccountVerification = () => {
  console.log('🔐 User wants to verify account for phone:', phoneNumber.value);

  // Redirect to verify account page with 2 options: password or OTP
  const queryParams: any = {
    phone: phoneNumber.value,
    accessToken: accessToken.value,
    customerName: customerName.value,
    type: registrationType.value,
    socialLogin: 'true',
    mode: 'link-account' // Special mode for account linking
  };

  // Only add serviceType if it exists in current route
  if (route.query.serviceType) {
    queryParams.serviceType = route.query.serviceType;
  }

  router.push({
    path: '/register/verify-account',
    query: queryParams
  });
};

// Send OTP for phone verification before updating to Google account
const sendOTPForVerification = async () => {
  const $sdk = useNuxtApp().$sdk;

  console.log('� Sending OTP for phone verification...');

  // Send OTP to verify phone number
  const otpResponse = await $sdk.auth.sendOTP(phoneNumber.value);

  if (otpResponse) {
    console.log('✅ OTP sent successfully');

    // Redirect to OTP verification with social login context
    const queryParams: any = {
      phone: phoneNumber.value,
      customerName: customerName.value,
      accessToken: accessToken.value,
      type: registrationType.value
    };

    // Only add serviceType if it exists
    if (route.query.serviceType) {
      queryParams.serviceType = route.query.serviceType;
    }

    router.push({
      path: '/register/verify-social-otp',
      query: queryParams
    });
  } else {
    throw new Error('Không thể gửi OTP');
  }
};

// Handle "not my account" (user wants to update phone to their Google account)
const handleNotMyAccount = async () => {
  console.log('📱 User wants to update this phone number to their Google account');

  // Validate phone first
  errors.value.phoneNumber = validatePhoneNumber(phoneNumber.value);
  if (errors.value.phoneNumber) {
    return;
  }

  loading.value = true;

  try {
    console.log('📱 SĐT chưa tồn tại - Gửi OTP để xác thực...');
    await sendOTPForVerification();
  } catch (error: any) {
    console.error('❌ Error sending OTP:', error);
    useNuxtApp().$toast.error(error?.message || "Có lỗi xảy ra khi gửi OTP");
  } finally {
    loading.value = false;
  }
};

// Handle form submission
const handleSubmit = async () => {
  // Validate form
  errors.value.phoneNumber = validatePhoneNumber(phoneNumber.value);

  if (errors.value.phoneNumber) {
    return;
  }

  // Check phone exists first if not already checked
  if (!phoneExists.value) {
    await checkPhoneExists();
  }

  // If phone exists, don't proceed with normal flow
  if (phoneExists.value) {
    useNuxtApp().$toast.warning("Vui lòng chọn một trong các tùy chọn bên trên");
    return;
  }

  loading.value = true;

  try {
    console.log('📱 SĐT chưa tồn tại - Gửi OTP để xác thực...');
    await sendOTPForVerification();
    return; // Exit early since we're redirecting

    console.log('� Case 1: SDT không tồn tại - Update phone vào tài khoản Google...');

    // Step 1: Update phone number to Google account using updateInfo
    console.log('🔄 Updating phone to Google account...');

    // Set token first
    $sdk.setToken(accessToken.value);

    // Update user info with phone number
    const updateResult = await $sdk.auth.updateInfo({
      phone: phoneNumber.value,
      fullName: customerName.value
    }, 'oauth'); // Use 'oauth' type for social login updates

    if (updateResult) {
      console.log('✅ Phone updated to Google account successfully:', updateResult);

      // Get updated user detail
      const updatedUserDetail = await $sdk.auth.getUserDetail(accessToken.value);
      console.log('📦 Updated user detail:', updatedUserDetail);

      if (updatedUserDetail) {
        // Set auth state
        const { setToken } = useAuth();
        setToken(accessToken.value);

        const authStore = useAuthStore();
        const user = {
          id: updatedUserDetail.partyId,
          name: updatedUserDetail.fullName,
          email: updatedUserDetail.email,
          phone: phoneNumber.value,
          avatar: updatedUserDetail.avatarUrl,
          birthDate: updatedUserDetail.birthDate,
        };
        authStore.setUser(user);

        useNuxtApp().$toast.success('Cập nhật thông tin thành công!');

        // Check if user has organizations
        if (updatedUserDetail.orgPositionsMap && Object.keys(updatedUserDetail.orgPositionsMap).length > 0) {
          // User has orgs, go to org-store selection
          console.log('🏢 User has organizations, redirecting to org-store');
          router.push('/org-store');
        } else {
          // User has no orgs, go to onboarding
          console.log('🚀 User has no organizations, redirecting to onboarding');
          const serviceType = route.query.serviceType;
          const queryParams: any = { userId: updatedUserDetail.partyId };

          if (serviceType) {
            queryParams.serviceType = serviceType;
          }

          router.push({
            path: '/onboarding',
            query: queryParams
          });
        }
      }
    } else {
      throw new Error('Không thể cập nhật thông tin');
    }
    
  } catch (error: any) {
    console.error('❌ Update info error:', error);
    useNuxtApp().$toast.error(error?.message || "Có lỗi xảy ra khi cập nhật thông tin");
  } finally {
    loading.value = false;
  }
};

// Get user info from token on mount
onMounted(async () => {
  if (!accessToken.value) {
    console.error('❌ Missing access token');
    useNuxtApp().$toast.error("Thông tin đăng nhập không hợp lệ");
    router.push('/register');
    return;
  }
  
  try {
    const $sdk = useNuxtApp().$sdk;
    $sdk.setToken(accessToken.value);
    
    // Get user info from Google
    const userDetail = await $sdk.auth.getUserDetail(accessToken.value);
    customerName.value = userDetail.fullName || userDetail.name || '';
    
    console.log('✅ Got user info from Google:', {
      name: customerName.value
    });
    
    if (!customerName.value) {
      useNuxtApp().$toast.error("Không thể lấy thông tin từ Google");
      router.push('/register');
    }
    
  } catch (error) {
    console.error('❌ Error getting user info:', error);
    useNuxtApp().$toast.error("Không thể lấy thông tin người dùng");
    router.push('/register');
  }
});

// Watch phone number for real-time validation
watch(phoneNumber, (newValue) => {
  if (errors.value.phoneNumber && newValue.trim()) {
    errors.value.phoneNumber = validatePhoneNumber(newValue);
  }
  // Reset phone exists when user changes phone
  phoneExists.value = false;
});
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
