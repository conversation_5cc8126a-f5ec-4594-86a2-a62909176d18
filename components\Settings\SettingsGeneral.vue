<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900"><PERSON><PERSON><PERSON> đặt chung</h2>
      <p class="mt-1 text-sm text-gray-600">
        Qu<PERSON>n lý các cài đặt cơ bản của hệ thống
      </p>
    </div>

    <!-- General Settings Form -->
    <div class="space-y-6">
      <div class="bg-gray-50 rounded-lg p-4">
        <h3 class="text-base font-medium text-gray-900 mb-4">Hi<PERSON>n thị</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700"
                >Chế độ tối</label
              >
              <p class="text-xs text-gray-500">Bật chế độ tối cho giao diện</p>
            </div>
            <button
              type="button"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              role="switch"
              aria-checked="false"
            >
              <span
                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
              ></span>
            </button>
          </div>
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700">Tạo đơn </label>
              <p class="text-xs text-gray-500">Bật cho tạo đơn </p>
            </div>
            <button
              type="button"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              role="switch"
              aria-checked="false"
            >
              <span
                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
              ></span>
            </button>
          </div>
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700"
                >Xuất hóa đơn</label
              >
              <p class="text-xs text-gray-500">
                Bật cho nhân viên xuất hóa đơn
              </p>
            </div>
            <button
              type="button"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              role="switch"
              aria-checked="false"
            >
              <span
                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
              ></span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Component logic for general settings
</script>
