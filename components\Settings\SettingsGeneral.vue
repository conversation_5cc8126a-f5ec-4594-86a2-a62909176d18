<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900"><PERSON><PERSON><PERSON> đặt chung</h2>
      <p class="mt-1 text-sm text-gray-600">
        Qu<PERSON>n lý các cài đặt cơ bản của hệ thống
      </p>
    </div>

    <!-- General Settings Form -->
    <div class="space-y-6">
      <div class="bg-gray-50 rounded-lg p-4">
        <h3 class="text-base font-medium text-gray-900 mb-4">Hiển thị</h3>
        <div class="space-y-4">
          <ToggleButton
            v-model="settings.darkMode"
            label="Chế độ tối"
            description="Bật chế độ tối cho giao diện"
            @change="handleDarkModeChange"
          />
          <ToggleButton
            v-model="settings.autoCreateOrder"
            label="Tạo đơn"
            description="Bật cho tạo đơn khi tồ<PERSON> kho sản phẩm < 5"
            color="green"
            @change="handleAutoCreateOrderChange"
          />
          <ToggleButton
            v-model="settings.allowInvoiceExport"
            label="Xuất hóa đơn"
            description="Bật cho nhân viên xuất hóa đơn"
            color="purple"
            @change="handleInvoiceExportChange"
          />
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Settings reactive data
const settings = reactive({
  darkMode: false,
  autoCreateOrder: true,
  allowInvoiceExport: false,
});

// Event handlers
const handleDarkModeChange = (value: boolean) => {
  console.log("Dark mode changed:", value);
  // TODO: Implement dark mode toggle logic
  // Could emit event to parent or update global state
};

const handleAutoCreateOrderChange = (value: boolean) => {
  console.log("Auto create order changed:", value);
  // TODO: Implement auto create order logic
};

const handleInvoiceExportChange = (value: boolean) => {
  console.log("Invoice export changed:", value);
  // TODO: Implement invoice export permission logic
};
</script>
