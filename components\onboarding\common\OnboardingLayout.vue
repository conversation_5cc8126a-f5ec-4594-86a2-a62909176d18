<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Simple Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <!-- Logo & Title -->
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                />
              </svg>
            </div>
            <div>
              <h1 class="text-lg font-bold text-gray-900">
                Thiết Lập Hệ Thống
              </h1>
              <p class="text-sm text-gray-600">{{ currentStep?.title }}</p>
            </div>
          </div>

          <!-- Progress & Exit -->
          <div class="flex items-center space-x-4">
            <!-- Simple Progress -->

            <div class="flex items-center space-x-2 text-sm text-gray-600">
              <span class="font-medium"
                > {{ (progress.current || 0) + 1 }}/{{
                  progress.total || 0
                }}</span
              >
              <div class="w-24 bg-gray-200 rounded-full h-2">
                <div
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  :style="{
                    width: `${
                      progress.total > 0
                        ? (((progress.current || 0) + 1) / progress.total) * 100
                        : 0
                    }%`,
                  }"
                ></div>
              </div>
            </div>

            <!-- Exit Button -->
            <button
              @click="$emit('exit')"
              class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              title="Thoát"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-6 py-8">
      <!-- Step Description -->
      <div class="text-center mb-8">
        <p class="text-gray-600 max-w-2xl mx-auto">
          {{ currentStep?.description }}
        </p>
    
      </div>

      <!-- Content Area -->
      <div
        class="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden"
      >
        <div class="p-8">
          <slot name="content" />
        </div>

        <!-- Bottom Navigation -->
        <div class="bg-gray-50 border-t border-gray-200 px-8 py-6">
          <div class="flex items-center justify-between">
            <button
              v-if="navigationOptions.canGoBack"
              @click="$emit('back')"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
            >
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
              Quay lại
            </button>
            <div v-else></div>

            <div class="flex items-center space-x-3">
              <!-- Skip Button -->
              <button
                v-if="navigationOptions.canSkip && !currentStep?.required"
                @click="$emit('skip')"
                class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 transition-colors duration-200"
              >
                Bỏ qua
              </button>

              <!-- Next Button -->
              <button
                @click="$emit('next')"
                :disabled="!canProceed || loading"
                class="inline-flex items-center px-8 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <span v-if="loading" class="inline-flex items-center">
                  <svg
                    class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Đang xử lý...
                </span>
                <span v-else class="inline-flex items-center">
                  {{ navigationOptions.isLastStep ? "Hoàn thành" : "Tiếp tục" }}
                  <svg
                    v-if="!navigationOptions.isLastStep"
                    class="w-4 h-4 ml-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Help Section -->
    <div class="text-center py-6">
      <p class="text-sm text-gray-500 mb-2">Cần hỗ trợ?</p>
      <a
        href="mailto:<EMAIL>"
        class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors duration-200"
      >
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path
            d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
          />
          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
        </svg>
        <EMAIL>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import type {
  OnboardingStep,
  OnboardingProgress,
  NavigationOptions,
} from "~/types/onboarding";

// Props
interface Props {
  currentStep?: OnboardingStep;
  currentStepId?: string;
  steps?: OnboardingStep[];
  progress: OnboardingProgress;
  navigationOptions: NavigationOptions;
  loading?: boolean;
  canProceed?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  canProceed: true,
});

// Debug progress
console.log("🔍 OnboardingLayout progress:", props.progress);
console.log("🔍 OnboardingLayout steps:", props.steps);
console.log("🔍 OnboardingLayout currentStep:", props.currentStep);

// Emits
defineEmits<{
  back: [];
  next: [];
  skip: [];
  "step-click": [stepId: string];
  exit: [];
}>();
</script>

<style scoped>
/* Custom animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}
</style>
