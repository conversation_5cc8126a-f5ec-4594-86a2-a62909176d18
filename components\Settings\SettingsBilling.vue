<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900"><PERSON><PERSON> to<PERSON></h2>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON>u<PERSON>n lý thông tin thanh toán và hóa đơn
      </p>
    </div>

    <!-- Current Plan -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900">Gói Professional</h3>
          <p class="text-sm text-gray-600">G<PERSON>i hiện tại của bạn</p>
          <p class="text-2xl font-bold text-blue-600 mt-2">₫2,500,000/tháng</p>
        </div>
        <div class="text-right">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
            <PERSON><PERSON> ho<PERSON> động
          </span>
          <p class="text-sm text-gray-600 mt-2">Gia hạn: 15/02/2024</p>
        </div>
      </div>
      
      <div class="mt-4 flex space-x-3">
        <button
          type="button"
          class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
        >
          Nâng cấp gói
        </button>
        <button
          type="button"
          class="bg-white text-blue-600 border border-blue-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-50"
        >
          Thay đổi gói
        </button>
      </div>
    </div>

    <!-- Payment Methods -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Phương thức thanh toán</h3>
      
      <div class="space-y-3">
        <div class="flex items-center justify-between p-4 bg-white rounded-lg border border-green-200">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-6 bg-blue-600 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">VISA</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">•••• •••• •••• 4242</p>
              <p class="text-xs text-gray-500">Hết hạn 12/2025</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-green-600 font-medium">Mặc định</span>
            <button class="text-xs text-blue-600 hover:text-blue-800">Chỉnh sửa</button>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-6 bg-red-600 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">MC</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">•••• •••• •••• 8888</p>
              <p class="text-xs text-gray-500">Hết hạn 08/2024</p>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-xs text-blue-600 hover:text-blue-800">Đặt mặc định</button>
            <button class="text-xs text-red-600 hover:text-red-800">Xóa</button>
          </div>
        </div>
      </div>
      
      <button
        type="button"
        class="mt-4 bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50"
      >
        Thêm phương thức thanh toán
      </button>
    </div>

    <!-- Billing History -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Lịch sử thanh toán</h3>
      
      <div class="overflow-hidden">
        <table class="min-w-full bg-white rounded-lg">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ngày
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Mô tả
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Số tiền
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trạng thái
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Hóa đơn
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr>
              <td class="px-4 py-3 text-sm text-gray-900">15/01/2024</td>
              <td class="px-4 py-3 text-sm text-gray-900">Gói Professional - Tháng 1</td>
              <td class="px-4 py-3 text-sm text-gray-900">₫2,500,000</td>
              <td class="px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Đã thanh toán
                </span>
              </td>
              <td class="px-4 py-3">
                <button class="text-sm text-blue-600 hover:text-blue-800">Tải xuống</button>
              </td>
            </tr>
            <tr>
              <td class="px-4 py-3 text-sm text-gray-900">15/12/2023</td>
              <td class="px-4 py-3 text-sm text-gray-900">Gói Professional - Tháng 12</td>
              <td class="px-4 py-3 text-sm text-gray-900">₫2,500,000</td>
              <td class="px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Đã thanh toán
                </span>
              </td>
              <td class="px-4 py-3">
                <button class="text-sm text-blue-600 hover:text-blue-800">Tải xuống</button>
              </td>
            </tr>
            <tr>
              <td class="px-4 py-3 text-sm text-gray-900">15/11/2023</td>
              <td class="px-4 py-3 text-sm text-gray-900">Gói Professional - Tháng 11</td>
              <td class="px-4 py-3 text-sm text-gray-900">₫2,500,000</td>
              <td class="px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Đã thanh toán
                </span>
              </td>
              <td class="px-4 py-3">
                <button class="text-sm text-blue-600 hover:text-blue-800">Tải xuống</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <button
        type="button"
        class="mt-4 text-sm text-blue-600 hover:text-blue-800 font-medium"
      >
        Xem tất cả lịch sử
      </button>
    </div>

    <!-- Billing Information -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Thông tin hóa đơn</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Tên công ty
          </label>
          <input
            type="text"
            value="Công ty TNHH ABC"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Mã số thuế
          </label>
          <input
            type="text"
            value="0123456789"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
          />
        </div>
        
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Địa chỉ
          </label>
          <textarea
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="Nhập địa chỉ công ty..."
          >123 Đường ABC, Quận 1, TP.HCM</textarea>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Component logic for billing settings
</script>
