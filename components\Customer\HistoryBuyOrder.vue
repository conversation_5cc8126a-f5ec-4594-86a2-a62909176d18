<template>
  <div class="rounded-lg p-2 border bg-white col-span-3 md:col-span-6">
    <span class="text-lg font-semibold text-primary cursor-pointer">
      <PERSON><PERSON>ch sử mua hàng
    </span>
    <div class="mt-2 overflow-auto h-[44vh]">
      <table class="table-auto w-full text-sm">
        <thead class="sticky top-0 z-2">
          <tr class="text-left font-semibold bg-blue-100">
            <th class="p-2 w-1/12 text-center"><PERSON><PERSON> đơn</th>
            <th class="p-2 w-2/12">Nhân viên</th>
            <th class="p-2 w-6/12">Sản phẩm</th>
            <th class="p-2 w-1/12">Tổng giảm</th>
            <th class="p-2 w-2/12">Thanh toán</th>
            <th class="p-2 w-full">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-5"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                  />
                </svg>
              </span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="diary in listOrder"
            :key="diary.id"
            class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white"
          >
            <TableDiary
              :diary="diary"
              :isNotDraft="true"
              @handleLoading="toggleLoading"
              @handleOpenDetailDialog="handleOpenDetailDialog"
              :isDetailCustomerOrder="true"
            ></TableDiary>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <DetailOrderDialog
    v-if="isOpenDetailDialog"
    :order="SelectedOrder"
    @cancel="toogleDetailOrder"
  ></DetailOrderDialog>
</template>

<script setup>
import { ref } from "vue";

const headers = [
  "Mã Đơn Hàng",
  "Thời Gian Tạo",
  "Sản Phẩm",
  "Giá",
  "SL",
  "Tổng Phải Thu",
  "Tổng Giảm",
  "Tổng Thu",
  "Trạng Thái",
];

const visibleColumns = ref(headers.map(() => true));

const props = defineProps({
  listOrder: {
    type: Array,
    default: () => [],
  },
});
const handleStatusClass = (statusDecription) => {
  switch (statusDecription) {
    case "REQUEST":
      return "text-[#5BC0EB] rounded-full px-2 py-1 text-sm font-semibold";
    case "OPEN":
      return "text-[#64B5F6] rounded-full px-2 py-1 text-sm font-semibold";
    case "APPROVED":
      return "text-[#4CAF50] rounded-full px-2 py-1 text-sm font-semibold";
    case "IN_PROGRESS":
      return "text-[#FFB74D] rounded-full px-2 py-1 text-sm font-semibold";
    case "WAIT_DELIVERY":
      return "text-[#FF9800] rounded-full px-2 py-1 text-sm font-semibold";
    case "PROCESSING_DELIVERY":
      return "text-[#2196F3] rounded-full px-2 py-1 text-sm font-semibold";
    case "COMPLETED":
      return "text-[#388E3C] rounded-full px-2 py-1 text-sm font-semibold";
    case "RETURNING":
      return "text-[#BA68C8] rounded-full px-2 py-1 text-sm font-semibold";
    case "RETURNED":
      return "text-[#E57373] rounded-full px-2 py-1 text-sm font-semibold";
    default:
      return "text-[#636AE8] rounded-full px-2 py-1 text-sm font-semibold";
  }
};
const isOpenDetailDialog = ref(false);
const SelectedOrder = ref();
const handleOpenDetailDialog = async (order) => {
  SelectedOrder.value = order;
  isOpenDetailDialog.value = true;
};
const toogleDetailOrder = () => {
  isOpenDetailDialog.value = false;
};
</script>
