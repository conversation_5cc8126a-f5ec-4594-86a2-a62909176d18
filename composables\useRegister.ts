import { ref, watch, nextTick, computed } from "vue";
import { useRouter } from "vue-router";
import { useNuxtApp } from "#app";

/**
 * Updated Registration Flow:
 * 1. Create userLogin -> tạo tài khoản đăng nhập
 * 2. Send OTP token -> g<PERSON>i mã OTP xác thực
 * 3. Validate OTP -> xác thực mã OTP (thay vì getAccessTokenByOTP)
 * 4. Create org -> tạo tổ chức/cửa hàng
 * 5. Create userDetail -> tạo thông tin chi tiết user
 * 6. Update info -> cập nhật thông tin user (tên, password, etc.)
 * 7. Add role user -> g<PERSON> quyền cho user trong org
 */

export default function useRegister() {
  const $sdk = useNuxtApp().$sdk;
  const router = useRouter();
  const route = useRoute();

  // Registration service from query params
  const registrationService = ref(
    (route.query.serviceType as string) ||
      (route.query.service as string) ||
      (route.query.type as string) ||
      "POS"
  );

  console.log("🔧 useRegister initialized with:", {
    routeQuery: route.query,
    registrationService: registrationService.value,
    currentPath: route.path,
  });

  // Form data
  const customerName = ref("");
  const phoneNumber = ref("");
  const otp = ref("");
  const password = ref("");
  const confirmPassword = ref("");

  // Store userLoginId from createUserLogin response
  const userLoginId = ref("");

  // OTP delivery method selection
  const otpMethod = ref("SMS"); // "SMS" or "ZALO"

  // UI states
  const loading = ref(false);
  const hiddenPassword = ref(false);
  const hiddenConfirmPassword = ref(false);
  const otpSent = ref(false);
  const registrationData = ref<any>({});

  // Error states
  const errors = ref({
    customerName: "",
    phoneNumber: "",
    otp: "",
    password: "",
    confirmPassword: "",
  });

  // Enhanced validation functions
  const validateCustomerName = (name: string): string => {
    if (!name || !name.trim()) {
      return "Tên khách hàng không được để trống";
    }
    if (name.trim().length < 2) {
      return "Tên khách hàng phải có ít nhất 2 ký tự";
    }
    if (name.trim().length > 50) {
      return "Tên khách hàng không được quá 50 ký tự";
    }
    // Allow Vietnamese characters, letters, and spaces
    const nameRegex = /^[a-zA-ZÀ-ỹ\s]+$/;
    if (!nameRegex.test(name.trim())) {
      return "Tên khách hàng chỉ được chứa chữ cái và khoảng trắng";
    }
    return "";
  };

  const validatePhoneNumber = (phone: string): string => {
    if (!phone || !phone.trim()) {
      return "Số điện thoại không được để trống";
    }
    // Remove spaces and special characters
    const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, "");
    // Vietnamese phone number: exactly 10 digits, starts with 0
    const phoneRegex = /^0[3|5|7|8|9][0-9]{8}$/;
    if (!phoneRegex.test(cleanPhone)) {
      return "Số điện thoại không hợp lệ. Vui lòng nhập số điện thoại Việt Nam 10 số (VD: 0901234567)";
    }
    return "";
  };

  const validateStoreName = (name: string): string => {
    if (!name || !name.trim()) {
      return "Tên cửa hàng không được để trống";
    }
    if (name.trim().length < 2) {
      return "Tên cửa hàng phải có ít nhất 2 ký tự";
    }
    if (name.trim().length > 100) {
      return "Tên cửa hàng không được quá 100 ký tự";
    }
    // Allow Vietnamese characters, letters, numbers, spaces, and common business characters
    const storeNameRegex = /^[a-zA-Z0-9À-ỹ\s\-\&\.\,]+$/;
    if (!storeNameRegex.test(name.trim())) {
      return "Tên cửa hàng chứa ký tự không hợp lệ";
    }
    return "";
  };

  const validateOTP = (otp: string): string => {
    if (!otp || !otp.trim()) {
      return "Mã OTP không được để trống";
    }
    const cleanOTP = otp.replace(/\s/g, "");
    if (cleanOTP.length !== 6) {
      return "Mã OTP phải có đúng 6 chữ số";
    }
    if (!/^[0-9]{6}$/.test(cleanOTP)) {
      return "Mã OTP chỉ được chứa số";
    }
    return "";
  };

  const validatePassword = (pwd: string): string => {
    if (!pwd || !pwd.trim()) {
      return "Mật khẩu không được để trống";
    }
    if (pwd.length < 6) {
      return "Mật khẩu phải có ít nhất 6 ký tự";
    }
    if (pwd.length > 50) {
      return "Mật khẩu không được quá 50 ký tự";
    }
    return "";
  };

  const validateConfirmPassword = (pwd: string, confirmPwd: string): string => {
    if (!confirmPwd || !confirmPwd.trim()) {
      return "Xác nhận mật khẩu không được để trống";
    }
    if (pwd !== confirmPwd) {
      return "Mật khẩu xác nhận không khớp";
    }
    return "";
  };

  const resetErrors = () => {
    errors.value = {
      customerName: "",
      phoneNumber: "",
      otp: "",
      password: "",
      confirmPassword: "",
    };
  };

  // Field validation states to track if field has been touched
  const fieldTouched = ref({
    customerName: false,
    phoneNumber: false,
    storeName: false,
    otp: false,
    password: false,
    confirmPassword: false,
  });

  // Validation methods for onBlur events
  const validateFieldOnBlur = (fieldName: keyof typeof errors.value) => {
    fieldTouched.value[fieldName] = true;

    switch (fieldName) {
      case "customerName":
        errors.value.customerName = validateCustomerName(customerName.value);
        break;
      case "phoneNumber":
        errors.value.phoneNumber = validatePhoneNumber(phoneNumber.value);
        break;
      case "otp":
        errors.value.otp = validateOTP(otp.value);
        break;
      case "password":
        errors.value.password = validatePassword(password.value);
        // Also revalidate confirm password if it's been touched
        if (fieldTouched.value.confirmPassword) {
          errors.value.confirmPassword = validateConfirmPassword(
            password.value,
            confirmPassword.value
          );
        }
        break;
      case "confirmPassword":
        errors.value.confirmPassword = validateConfirmPassword(
          password.value,
          confirmPassword.value
        );
        break;
    }
  };

  // Real-time validation watchers (only after field has been touched)
  watch(customerName, (newValue) => {
    if (fieldTouched.value.customerName) {
      errors.value.customerName = validateCustomerName(newValue);
    }
  });

  watch(phoneNumber, (newValue) => {
    if (fieldTouched.value.phoneNumber) {
      errors.value.phoneNumber = validatePhoneNumber(newValue);
    }
  });

  watch(otp, (newValue) => {
    if (fieldTouched.value.otp) {
      errors.value.otp = validateOTP(newValue);
    }
  });

  watch(password, (newValue) => {
    if (fieldTouched.value.password) {
      errors.value.password = validatePassword(newValue);
    }
    // Also revalidate confirm password if it's been touched
    if (fieldTouched.value.confirmPassword) {
      errors.value.confirmPassword = validateConfirmPassword(
        newValue,
        confirmPassword.value
      );
    }
  });

  watch(confirmPassword, (newValue) => {
    if (fieldTouched.value.confirmPassword) {
      errors.value.confirmPassword = validateConfirmPassword(
        password.value,
        newValue
      );
    }
  });

  // Computed properties for form validation
  const isRegistrationFormValid = computed(() => {
    return (
      customerName.value.trim() &&
      phoneNumber.value.trim() &&
      !validateCustomerName(customerName.value) &&
      !validatePhoneNumber(phoneNumber.value)
    );
  });

  const isOtpFormValid = computed(() => {
    return (
      otp.value.trim() &&
      password.value.trim() &&
      confirmPassword.value.trim() &&
      !validateOTP(otp.value) &&
      !validatePassword(password.value) &&
      !validateConfirmPassword(password.value, confirmPassword.value)
    );
  });

  // Registration step 1: Submit registration info
  const handleRegisterSubmit = async () => {
    await nextTick();
    resetErrors();

    console.log(
      "📝 handleRegisterSubmit called with service:",
      registrationService.value
    );

    // Validate form fields using enhanced validation
    const customerNameError = validateCustomerName(customerName.value);
    if (customerNameError) {
      errors.value.customerName = customerNameError;
      return;
    }

    const phoneError = validatePhoneNumber(phoneNumber.value);
    if (phoneError) {
      errors.value.phoneNumber = phoneError;
      return;
    }

    loading.value = true;

    try {
      // Step 1: Check số điện thoại đã tồn tại chưa
      const checkResponse = await $sdk.auth.checkUserLogin(phoneNumber.value);

      if (checkResponse) {
        useNuxtApp().$toast.error("Số điện thoại đã được đăng ký");
        return;
      }

      // Step 2: Tạo userLogin trước
      const userLoginResponse = await $sdk.auth.createUserLogin(
        phoneNumber.value
      );

      // Lưu userLoginId để sử dụng sau này
      if (userLoginResponse?.id) {
        userLoginId.value = userLoginResponse.id;
        console.log("📝 UserLogin created with ID:", userLoginId.value);
      }

      // Step 3: Gửi OTP để xác nhận (mặc định qua SMS)
      const otpResponse = await $sdk.auth.sendOTP(phoneNumber.value);
      console.log("📱 OTP đã được gửi qua SMS");

      if (otpResponse) {
        registrationData.value = {
          ...otpResponse,
          customerName: customerName.value,
          userLoginId: userLoginId.value,
        };
        otpSent.value = true;
        useNuxtApp().$toast.success(
          "Mã OTP đã được gửi đến số điện thoại của bạn"
        );

        // Navigate to OTP verification page
        router.push({
          path: "/register/otp-verification",
          query: {
            phone: phoneNumber.value,
            customerName: customerName.value,
            userLoginId: userLoginId.value,
            serviceType: registrationService.value, // ✅ Preserve registration service
          },
        });
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      useNuxtApp().$toast.error(
        error?.message || "Có lỗi xảy ra khi đăng ký. Vui lòng thử lại."
      );
    } finally {
      loading.value = false;
    }
  };

  // OTP verification and password creation
  const handleOtpVerification = async () => {
    await nextTick();
    resetErrors();

    // Debug: Kiểm tra tất cả dữ liệu trước khi xử lý
    console.log("📝 Debug - handleOtpVerification data:");
    console.log("  phoneNumber:", phoneNumber.value);
    console.log("  customerName:", customerName.value);
    console.log("  otp:", otp.value);
    console.log("  password length:", password.value?.length || 0);

    // Validate OTP using enhanced validation
    const otpError = validateOTP(otp.value);
    if (otpError) {
      errors.value.otp = otpError;
      return;
    }

    // Validate password using enhanced validation
    const passwordError = validatePassword(password.value);
    if (passwordError) {
      errors.value.password = passwordError;
      return;
    }

    // Validate confirm password
    const confirmPasswordError = validateConfirmPassword(
      password.value,
      confirmPassword.value
    );
    if (confirmPasswordError) {
      errors.value.confirmPassword = confirmPasswordError;
      return;
    }

    loading.value = true;

    try {
      // Step 1: Validate OTP (sử dụng validateOTP thay vì getAccessTokenByOTP)
      const verifyResponse = await $sdk.auth.validateOTP(
        otp.value,
        phoneNumber.value
      );

      if (verifyResponse) {
        // Step 2: Thực hiện registration flow (không tạo org ở đây nữa)
        await executeRegistrationFlow();
        // executeRegistrationFlow sẽ tự redirect đến trang tạo org
      } else {
        useNuxtApp().$toast.error("Mã OTP không chính xác");
      }
    } catch (error: any) {
      console.error("OTP verification error:", error);
      useNuxtApp().$toast.error(
        error?.message || "Mã OTP không chính xác hoặc đã hết hạn"
      );
    } finally {
      loading.value = false;
    }
  };

  // OTP-only verification (for new separated flow)
  const handleOtpOnlyVerification = async () => {
    console.log('🔍 handleOtpOnlyVerification called with:', {
      otp: otp.value,
      phone: phoneNumber.value,
      customerName: customerName.value,
      userLoginId: userLoginId.value,
      serviceType: registrationService.value
    });

    await nextTick();
    resetErrors();

    // Validate OTP only
    const otpError = validateOTP(otp.value);
    if (otpError) {
      console.log('❌ OTP validation failed:', otpError);
      errors.value.otp = otpError;
      return;
    }

    console.log('✅ OTP validation passed, calling SDK...');
    loading.value = true;

    try {
      // Validate OTP
      const verifyResponse = await $sdk.auth.validateOTP(
        otp.value,
        phoneNumber.value
      );

      console.log('📡 SDK validateOTP response:', verifyResponse);

      if (verifyResponse) {
        useNuxtApp().$toast.success("Xác thực OTP thành công!");

        console.log('🚀 Navigating to create-password page...');
        // Navigate to password creation page
        router.push({
          path: "/register/create-password",
          query: {
            phone: phoneNumber.value,
            customerName: customerName.value,
            userLoginId: userLoginId.value,
            serviceType: registrationService.value,
          },
        });
      } else {
        console.log('❌ OTP verification failed from SDK');
        errors.value.otp = "Mã OTP không chính xác";
      }
    } catch (error: any) {
      console.error("❌ OTP verification error:", error);
      errors.value.otp = error?.message || "Mã OTP không chính xác hoặc đã hết hạn";
    } finally {
      loading.value = false;
    }
  };

  // Password creation handler (for separated flow)
  const handlePasswordCreation = async (newPassword: string, confirmNewPassword: string) => {
    await nextTick();
    resetErrors();

    // Validate password
    const passwordError = validatePassword(newPassword);
    if (passwordError) {
      errors.value.password = passwordError;
      return false;
    }

    // Validate confirm password
    const confirmPasswordError = validateConfirmPassword(newPassword, confirmNewPassword);
    if (confirmPasswordError) {
      errors.value.confirmPassword = confirmPasswordError;
      return false;
    }

    loading.value = true;

    try {
      // Set password values
      password.value = newPassword;
      confirmPassword.value = confirmNewPassword;

      // Execute registration flow
      await executeRegistrationFlow();
      return true;
    } catch (error: any) {
      console.error("Password creation error:", error);
      useNuxtApp().$toast.error(error?.message || "Có lỗi xảy ra trong quá trình đăng ký");
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Resend OTP via SMS
  const resendOtpViaSMS = async () => {
    if (!phoneNumber.value) {
      useNuxtApp().$toast.error("Không tìm thấy số điện thoại");
      return;
    }

    loading.value = true;

    try {
      // Gửi lại OTP qua SMS
      await $sdk.auth.sendOTP(phoneNumber.value);
      useNuxtApp().$toast.success("Mã OTP mới đã được gửi qua SMS");
    } catch (error: any) {
      console.error("Resend OTP via SMS error:", error);
      useNuxtApp().$toast.error(
        error?.message || "Có lỗi xảy ra khi gửi lại mã OTP qua SMS"
      );
    } finally {
      loading.value = false;
    }
  };

  // Resend OTP via Zalo
  const resendOtpViaZalo = async () => {
    if (!phoneNumber.value) {
      useNuxtApp().$toast.error("Không tìm thấy số điện thoại");
      return;
    }

    loading.value = true;

    try {
      // Gửi lại OTP qua Zalo
      await $sdk.auth.sendOTP(phoneNumber.value, "ZALO");
      useNuxtApp().$toast.success("Mã OTP mới đã được gửi qua Zalo");
    } catch (error: any) {
      console.error("Resend OTP via Zalo error:", error);
      useNuxtApp().$toast.error(
        error?.message || "Có lỗi xảy ra khi gửi lại mã OTP qua Zalo"
      );
    } finally {
      loading.value = false;
    }
  };

  // Legacy resend method (for backward compatibility)
  const resendOtp = resendOtpViaSMS;

  // Assign user roles based on registration type
  const assignUserRoles = async (userId: string) => {
    console.log(
      "🔐 Assigning roles for registration service:",
      registrationService.value
    );

    try {
      // Base roles for all users
      const baseRoles = [{ role: "EMPLOYEE", type: "PERMISSION" }];

      // Additional roles based on registration service
      const additionalRoles = getAdditionalRolesByType(
        registrationService.value
      );

      const allRoles = [...baseRoles, ...additionalRoles];

      // Assign all roles
      for (const { role, type } of allRoles) {
        try {
          await $sdk.auth.addRoleUser(role, userId, type);
          console.log(
            `✅ Role ${role} (${type}) assigned for ${registrationService.value} registration`
          );
        } catch (error) {
          console.error(`❌ Error assigning ${role} role:`, error);
          // Continue with other roles even if one fails
        }
      }
    } catch (error) {
      console.error("❌ Error in role assignment:", error);
      console.warn("Role assignment failed but continuing registration...");
    }
  };

  // Get additional roles based on registration type
  const getAdditionalRolesByType = (type: string) => {
    switch (type.toUpperCase()) {
      case "TELESALE":
        return [
          { role: "TELESALE", type: "PERMISSION" },
          { role: "SALES_MANAGER", type: "POSITION" },
        ];

      case "ADMIN":
        return [
          { role: "ORG_ADMIN", type: "POSITION" },
          { role: "SYSTEM_ADMIN", type: "PERMISSION" },
        ];

      case "MANAGER":
        return [
          { role: "ORG_ADMIN", type: "POSITION" },
          { role: "MANAGER", type: "PERMISSION" },
        ];

      case "NORMAL":
      default:
        return [{ role: "ORG_ADMIN", type: "POSITION" }];
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    hiddenPassword.value = !hiddenPassword.value;
  };

  const toggleConfirmPasswordVisibility = () => {
    hiddenConfirmPassword.value = !hiddenConfirmPassword.value;
  };

  // Execute registration flow theo diagram mới
  const executeRegistrationFlow = async () => {
    try {
      console.log("🚀 Bắt đầu registration flow...");

      // UserLogin đã được tạo ở bước trước, OTP đã được validate
      // Bây giờ thực hiện các bước tiếp theo (không tạo org ở đây nữa):

      // Step 1: Tạo userDetail (sử dụng userLoginId thay vì phoneNumber)
      console.log("Step 2: Tạo userDetail...");
      console.log(
        "📝 Using userLoginId for createUserDetail:",
        userLoginId.value
      );

      if (!userLoginId.value) {
        throw new Error("UserLoginId không tồn tại. Vui lòng thử lại.");
      }
      // set sdk orgId
      $sdk.setOrgId("LONGVAN");
      const userDetailResponse = await $sdk.auth.createUserDetail(
        userLoginId.value
      );
      const userId = userDetailResponse?.partyId;
      const token = userDetailResponse?.accessToken; // Lấy token từ userDetailResponse

      // Step 3: Tạo mật khẩu cho user (createPassword)
      console.log("Step 3: Tạo mật khẩu cho user...");
      try {
        // Cần setToken trước khi gọi createPassword
        // Lấy token từ userDetailResponse
        if (token) {
          $sdk.setToken(token);
          console.log("📝 Token đã được set từ userDetailResponse");
        } else {
          console.warn("⚠️ Không tìm thấy token trong userDetailResponse");
        }

        // Sử dụng hàm  updateInfo để set password thay vì cần update thêm tên
        await $sdk.auth.updateInfo(
          {
            fullName: customerName.value,
            phone: phoneNumber.value,
          },
          "",
          password.value
        );
        console.log(
          "✅ Mật khẩu đã được tạo thành công - password length:",
          password.value?.length
        );
      } catch (error) {
        console.error("❌ Lỗi khi tạo mật khẩu:", error);
        console.warn(
          "Create password step failed but continuing registration..."
        );
      }

      // Step 4: Add role user (gán quyền cho user trong org)
      console.log("Step 4: Add role user...");

      // 4.1: Add PERMISSION role (EMPLOYEE)
      // try {
      //   await $sdk.auth.addRoleUser('EMPLOYEE', userId,'PERMISSION');
      //   console.log('✅ Role EMPLOYEE (PERMISSION) đã được gán cho user:', userId);
      // } catch (error) {
      //   console.error('❌ Lỗi khi gán PERMISSION role:', error);
      //   console.warn('Add PERMISSION role failed but continuing...');
      // }

      // 4.2: Add POSITION role (ORG_ADMIN)
      // try {
      //   await $sdk.auth.addRoleUser('ORG_ADMIN', userId, 'POSITION');
      //   console.log('✅ Role ORG_ADMIN (POSITION) đã được gán cho user:', userId);
      // } catch (error) {
      //   console.error('❌ Lỗi khi gán POSITION role:', error);
      //   console.warn('Add POSITION role failed but continuing...');
      // }

      // Step 5: Get lại userDetail sau khi set role và set thông tin đăng nhập
      console.log("Step 5: Get lại userDetail và set thông tin đăng nhập...");
      try {
        // Get lại userDetail để có thông tin đầy đủ sau khi set role
        // Sử dụng token thay vì userId
        const updatedUserDetail = await $sdk.auth.getUserDetail(token);

        // Set thông tin đăng nhập giống như trong useAuth (DTO giống nhau)
        const authStore = useAuthStore();
        const user = {
          id: updatedUserDetail.partyId,
          name: updatedUserDetail.fullName,
          email: updatedUserDetail.email,
          phone: updatedUserDetail.phone,
          avatar: updatedUserDetail.avatarUrl,
          birthDate: updatedUserDetail.birthDate,
          roles: [],
        };

        authStore.setUser(user);

        // Set token và org data giống như trong useAuth
        if (updatedUserDetail.accessToken) {
          useCookie("token").value = updatedUserDetail.accessToken;
          $sdk.setToken(updatedUserDetail.accessToken);
        }

        if (updatedUserDetail.orgPositionsMap) {
          useCookie("dataOrg").value = JSON.stringify(
            updatedUserDetail.orgPositionsMap
          );
        }

        // Set userLoginId cookie for onboarding
        if (userLoginId.value) {
          useCookie("userLoginId").value = userLoginId.value;
          console.log("✅ Set userLoginId cookie:", userLoginId.value);
        }

        console.log(
          "✅ Đã get lại userDetail và set thông tin đăng nhập thành công"
        );
      } catch (error) {
        console.error("❌ Lỗi khi get userDetail sau registration:", error);
        // Fallback: Lưu thông tin cơ bản vào auth store
        console.log("Fallback: Lưu thông tin cơ bản vào auth store...");
        const authStore = useAuthStore();
        authStore.setUser({
          id: userId,
          name: customerName.value,
          phone: phoneNumber.value,
          email: "",
          avatar: "",
          birthDate: "",
        });
        console.log("✅ Fallback: User info đã được lưu vào auth store");
      }

      console.log("✅ Registration flow completed successfully");
      console.log("📋 Flow summary:");
      console.log("  1. ✅ Create userLogin");
      console.log("  2. ✅ Send OTP token");
      console.log("  3. ✅ Validate OTP");
      console.log("  4. ✅ Create userDetail (using userLoginId)");
      console.log("  5. ✅ Create password (using createPassword)");
      console.log("  6. ✅ Add role user (EMPLOYEE + ORG_ADMIN)");
      console.log("  7. ✅ Get userDetail & set auth data (như đã đăng nhập)");
      console.log("  8. 🔄 Redirect to create organization page");

      // Redirect to new onboarding system
      useNuxtApp().$toast.success(
        "Đăng ký thành công! Bắt đầu thiết lập hệ thống."
      );

      // Prepare onboarding parameters
      const onboardingParams = new URLSearchParams({
        userId: userId,
        serviceType: registrationService.value,
      });

      router.push(`/onboarding?${onboardingParams.toString()}`);
    } catch (error: any) {
      console.error("❌ Registration flow error:", error);
      throw new Error(`Lỗi trong quá trình đăng ký: ${error.message}`);
    }
  };

  return {
    // Form data
    customerName,
    phoneNumber,
    userLoginId,
    otp,
    password,
    confirmPassword,
    otpMethod,
    registrationService,

    // UI states
    loading,
    hiddenPassword,
    hiddenConfirmPassword,
    otpSent,
    errors,

    // Validation
    isRegistrationFormValid,
    isOtpFormValid,
    validateFieldOnBlur,
    fieldTouched,
    validateCustomerName,
    validatePhoneNumber,
    validateStoreName,
    validateOTP,
    validatePassword,
    validateConfirmPassword,

    // Methods
    handleRegisterSubmit,
    handleOtpVerification,
    handleOtpOnlyVerification,
    handlePasswordCreation,
    resendOtp,
    resendOtpViaSMS,
    resendOtpViaZalo,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    resetErrors,
  };
}
