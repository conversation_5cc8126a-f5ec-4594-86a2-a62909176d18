<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white rounded-2xl shadow-xl p-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.818-4.954A9.955 9.955 0 0121 12c0 5.523-4.477 10-10 10S1 17.523 1 12 5.477 2 11 2a9.955 9.955 0 015.818 1.954l-3.364 3.364A5.5 5.5 0 1016.5 16.5l3.364-3.364z"></path>
          </svg>
        </div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2"><PERSON><PERSON><PERSON> thực tài khoản</h1>
        <p class="text-gray-600">
          Số điện thoại <strong>{{ phoneNumber }}</strong> đã tồn tại trong hệ thống
        </p>
        <p class="text-sm text-gray-500 mt-2">
          Chọn phương thức xác thực để liên kết với tài khoản Google của bạn
        </p>
      </div>

      <!-- Verification Method Selection -->
      <div v-if="!selectedMethod" class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-800 text-center mb-6">
          Chọn phương thức xác thực
        </h3>
        
        <!-- Password Method -->
        <button
          @click="selectMethod('password')"
          class="w-full p-4 border-2 border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 text-left group"
        >
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3a1 1 0 011-1h2.586l6.414-6.414A6 6 0 0121 9z"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Xác thực bằng mật khẩu</h4>
              <p class="text-sm text-gray-600">Nhập mật khẩu của tài khoản hiện tại</p>
            </div>
          </div>
        </button>

        <!-- OTP Method -->
        <button
          @click="selectMethod('otp')"
          class="w-full p-4 border-2 border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all duration-200 text-left group"
        >
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Xác thực bằng OTP</h4>
              <p class="text-sm text-gray-600">Nhận mã xác thực qua SMS hoặc Zalo</p>
            </div>
          </div>
        </button>
      </div>

      <!-- Password Verification Form -->
      <div v-if="selectedMethod === 'password'" class="space-y-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">Nhập mật khẩu</h3>
          <button
            @click="selectMethod('otp')"
            class="text-sm text-blue-600 hover:text-blue-700 transition-colors"
          >
            Dùng OTP →
          </button>
        </div>

        <form @submit.prevent="handlePasswordVerification" class="space-y-4">
          <div class="space-y-2">
            <label for="password" class="block text-sm font-semibold text-gray-700">
              Mật khẩu
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                name="password"
                autocomplete="current-password"
                @blur="validatePassword"
                :class="[
                  'w-full pl-4 pr-12 py-3 border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200',
                  errors.password
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-200 focus:border-blue-300'
                ]"
                placeholder="Nhập mật khẩu của bạn"
              />
              <button
                type="button"
                @click="togglePasswordVisibility"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <svg
                  :class="showPassword ? 'hidden' : 'block'"
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg
                  :class="showPassword ? 'block' : 'hidden'"
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
              </button>
            </div>
            <Transition name="slide-down">
              <span v-if="errors.password" class="flex items-center text-sm text-red-600 mt-1">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ errors.password }}</span>
              </span>
            </Transition>
          </div>

          <button
            type="submit"
            :disabled="isLoading || !password.trim()"
            class="w-full bg-blue-600 text-white py-3 px-4 rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold"
          >
            <span v-if="isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Đang xác thực...
            </span>
            <span v-else>Xác thực</span>
          </button>
        </form>
      </div>

      <!-- OTP Verification Form -->
      <div v-if="selectedMethod === 'otp'" class="space-y-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">Xác thực OTP</h3>
          <button
            @click="selectMethod('password')"
            class="text-sm text-blue-600 hover:text-blue-700 transition-colors"
          >
            ← Dùng mật khẩu
          </button>
        </div>

        <!-- OTP Method Selection -->
        <div v-if="!otpSent" class="space-y-4">
          <p class="text-sm text-gray-600 text-center">
            Chọn phương thức nhận mã OTP
          </p>
          
          <div class="grid grid-cols-2 gap-3">
            <button
              @click="sendOTP('SMS')"
              :disabled="isLoading"
              class="p-3 border-2 border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 disabled:opacity-50"
            >
              <div class="text-center">
                <svg class="w-6 h-6 mx-auto mb-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm font-medium">SMS</span>
              </div>
            </button>

            <button
              @click="sendOTP('ZALO')"
              :disabled="isLoading"
              class="p-3 border-2 border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all duration-200 disabled:opacity-50"
            >
              <div class="text-center">
                <svg class="w-6 h-6 mx-auto mb-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-sm font-medium">Zalo</span>
              </div>
            </button>
          </div>
        </div>

        <!-- OTP Input Form -->
        <div v-if="otpSent" class="space-y-4">
          <div class="text-center">
            <p class="text-sm text-gray-600">
              Mã OTP đã được gửi qua SMS đến số điện thoại
            </p>
            <p class="font-semibold text-gray-800">{{ phoneNumber }}</p>
            <div class="flex items-center justify-center space-x-2 mt-2">
              <p class="text-xs text-gray-500">
                qua {{ otpMethod === 'SMS' ? 'SMS' : 'Zalo' }}
              </p>
              <button
                v-if="otpMethod === 'SMS'"
                @click="sendOTP('ZALO')"
                class="text-xs text-blue-600 hover:text-blue-700 underline"
              >
                Chuyển sang Zalo
              </button>
              <button
                v-else
                @click="sendOTP('SMS')"
                class="text-xs text-blue-600 hover:text-blue-700 underline"
              >
                Chuyển sang SMS
              </button>
            </div>
          </div>

          <form @submit.prevent="handleOTPVerification" class="space-y-4">
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-gray-700 text-center">
                Mã OTP
              </label>
              <div class="flex justify-center space-x-3">
                <input
                  v-for="(digit, index) in otpDigits"
                  :key="index"
                  :ref="(el) => setOtpInputRef(el as HTMLInputElement, index)"
                  v-model="otpDigits[index]"
                  type="text"
                  inputmode="numeric"
                  pattern="[0-9]*"
                  maxlength="1"
                  autocomplete="one-time-code"
                  :class="[
                    'w-12 h-12 text-center text-xl font-bold border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200',
                    errors.otp
                      ? 'border-red-300 bg-red-50'
                      : 'border-gray-200 focus:border-blue-300'
                  ]"
                  @input="handleOtpInput(index, $event)"
                  @keydown="handleOtpKeydown(index, $event)"
                  @paste="handleOtpPaste($event)"
                />
              </div>
              <Transition name="slide-down">
                <span v-if="errors.otp" class="flex items-center justify-center text-sm text-red-600 mt-2">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  <span>{{ errors.otp }}</span>
                </span>
              </Transition>
            </div>

            <button
              type="submit"
              :disabled="isLoading || otp.length !== 6"
              class="w-full bg-green-600 text-white py-3 px-4 rounded-xl hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold"
            >
              <span v-if="isLoading" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang xác thực...
              </span>
              <span v-else>Xác thực OTP</span>
            </button>

            <!-- Resend OTP -->
            <div class="text-center">
              <button
                type="button"
                @click="resendOTP"
                :disabled="isLoading"
                class="text-sm text-blue-600 hover:text-blue-800 transition-colors disabled:opacity-50"
              >
                Gửi lại mã OTP
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Back to Register -->
      <div class="text-center mt-6">
        <NuxtLink
          to="/register"
          class="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200 hover:underline"
        >
          ← Quay lại đăng ký
        </NuxtLink>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';

useHead({
  title: "Xác thực tài khoản",
});

const route = useRoute();
const router = useRouter();

// Get params from route
const phoneNumber = ref(route.query.phone as string || '');
const accessToken = ref(route.query.accessToken as string || '');
const customerName = ref(route.query.customerName as string || '');
const registrationService = ref((route.query.serviceType as string) || (route.query.service as string) || '');

// State management - Default to password method
const selectedMethod = ref<'password' | 'otp' | null>('password');
const loading = ref(false);

// Password verification
const password = ref('');
const showPassword = ref(false);

// OTP verification
const otpSent = ref(false);
const otpMethod = ref<'SMS' | 'ZALO'>('SMS');
const otpDigits = ref(['', '', '', '', '', '']);
const otpInputRefs = ref<HTMLInputElement[]>([]);

// Error handling
const errors = reactive({
  password: '',
  otp: ''
});

// Computed
const otp = computed(() => otpDigits.value.join(''));

// Methods
const selectMethod = (method: 'password' | 'otp') => {
  selectedMethod.value = method;
  // Reset states
  password.value = '';
  errors.password = '';
  errors.otp = '';
  otpSent.value = false;
  otpDigits.value = ['', '', '', '', '', ''];

  // Auto-send SMS when selecting OTP method
  if (method === 'otp') {
    nextTick(() => {
      sendOTP('SMS');
    });
  }
};

const goBack = () => {
  selectedMethod.value = null;
  // Reset all states
  password.value = '';
  errors.password = '';
  errors.otp = '';
  otpSent.value = false;
  otpDigits.value = ['', '', '', '', '', ''];
};

// Password validation
const validatePassword = () => {
  if (!password.value.trim()) {
    errors.password = "Mật khẩu không được để trống";
    return false;
  }
  if (password.value.length < 6) {
    errors.password = "Mật khẩu phải có ít nhất 6 ký tự";
    return false;
  }
  errors.password = '';
  return true;
};

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// Use account linking composable
const {
  loading: linkingLoading,
  errors: linkingErrors,
  linkAccountWithPassword,
  sendOTPForLinking,
  linkAccountWithOTP,
  redirectAfterLinking
} = useAccountLinking();

// Override loading state
const isLoading = computed(() => loading.value || linkingLoading.value);

// Password verification handler
const handlePasswordVerification = async () => {
  if (!validatePassword()) return;

  try {
    await linkAccountWithPassword({
      phone: phoneNumber.value,
      password: password.value,
      socialToken: accessToken.value,
      socialProvider: 'google'
    });

    // Redirect after successful linking
    console.log('🔗 Account linking successful, redirecting with service:', registrationService.value);
    redirectAfterLinking(registrationService.value);

  } catch (error) {
    // Error handling is done in the composable
    errors.password = linkingErrors.password;
  }
};

// OTP methods
const sendOTP = async (method: 'SMS' | 'ZALO') => {
  otpMethod.value = method;

  try {
    await sendOTPForLinking({
      phone: phoneNumber.value,
      method: method,
      socialToken: accessToken.value
    });

    otpSent.value = true;

    // Focus first OTP input
    nextTick(() => {
      if (otpInputRefs.value[0]) {
        otpInputRefs.value[0].focus();
      }
    });

  } catch (error) {
    // Error handling is done in the composable
  }
};

// OTP input handling
const setOtpInputRef = (el: HTMLInputElement, index: number) => {
  if (el) {
    otpInputRefs.value[index] = el;
  }
};

const handleOtpInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value.replace(/\D/g, ''); // Only allow digits

  if (value.length <= 1) {
    otpDigits.value[index] = value;

    // Auto-focus next input
    if (value && index < 5) {
      nextTick(() => {
        otpInputRefs.value[index + 1]?.focus();
      });
    }
  }

  // Clear error when user starts typing
  if (errors.otp && value) {
    errors.otp = '';
  }
};

const handleOtpKeydown = (index: number, event: KeyboardEvent) => {
  // Handle backspace
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    nextTick(() => {
      otpInputRefs.value[index - 1]?.focus();
    });
  }
};

const handleOtpPaste = (event: ClipboardEvent) => {
  event.preventDefault();
  const pastedData = event.clipboardData?.getData('text').replace(/\D/g, '') || '';

  if (pastedData.length === 6) {
    for (let i = 0; i < 6; i++) {
      otpDigits.value[i] = pastedData[i] || '';
    }
    // Focus last input
    nextTick(() => {
      otpInputRefs.value[5]?.focus();
    });
  }
};

// OTP validation
const validateOTP = () => {
  if (otp.value.length !== 6) {
    errors.otp = "Vui lòng nhập đầy đủ 6 số";
    return false;
  }
  errors.otp = '';
  return true;
};

// OTP verification handler
const handleOTPVerification = async () => {
  if (!validateOTP()) return;

  try {
    await linkAccountWithOTP({
      phone: phoneNumber.value,
      otp: otp.value,
      socialToken: accessToken.value,
      socialProvider: 'google'
    });

    // Redirect after successful linking
    console.log('🔗 Account linking successful, redirecting with service:', registrationService.value);
    redirectAfterLinking(registrationService.value);

  } catch (error) {
    // Error handling is done in the composable
    errors.otp = linkingErrors.otp;
  }
};

// Resend OTP
const resendOTP = async () => {
  await sendOTP(otpMethod.value);
};

// Validate required params on mount
onMounted(() => {
  if (!phoneNumber.value || !accessToken.value) {
    console.error('❌ Missing required params for account verification');
    useNuxtApp().$toast.error("Thông tin không hợp lệ");
    router.push('/register');
  }
});
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
