<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900"><PERSON><PERSON><PERSON> mật</h2>
      <p class="mt-1 text-sm text-gray-600">
        Quản lý cài đặt bảo mật và quyền truy cập
      </p>
    </div>

    <!-- Two-Factor Authentication -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">X<PERSON><PERSON> thự<PERSON> hai yếu tố (2FA)</h3>
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-700">Tăng cường bảo mật tài khoản với xác thực hai yếu tố</p>
          <p class="text-xs text-gray-500 mt-1">Ch<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> ho<PERSON></p>
        </div>
        <button
          type="button"
          class="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
        >
          Kích hoạt 2FA
        </button>
      </div>
    </div>

    <!-- Login Sessions -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Phiên đăng nhập</h3>
      <div class="space-y-4">
        <div class="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">Chrome trên Windows</p>
              <p class="text-xs text-gray-500">Phiên hiện tại • Hà Nội, Việt Nam</p>
            </div>
          </div>
          <span class="text-xs text-green-600 font-medium">Đang hoạt động</span>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-white rounded-lg border">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">Safari trên iPhone</p>
              <p class="text-xs text-gray-500">2 giờ trước • Hà Nội, Việt Nam</p>
            </div>
          </div>
          <button class="text-xs text-red-600 hover:text-red-800">Đăng xuất</button>
        </div>
      </div>
      
      <button
        type="button"
        class="mt-4 text-sm text-red-600 hover:text-red-800 font-medium"
      >
        Đăng xuất tất cả thiết bị khác
      </button>
    </div>

    <!-- Security Settings -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Cài đặt bảo mật</h3>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <label class="text-sm font-medium text-gray-700">Thông báo đăng nhập</label>
            <p class="text-xs text-gray-500">Nhận email khi có đăng nhập từ thiết bị mới</p>
          </div>
          <button
            type="button"
            class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-primary transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            role="switch"
            aria-checked="true"
          >
            <span class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
          </button>
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <label class="text-sm font-medium text-gray-700">Tự động đăng xuất</label>
            <p class="text-xs text-gray-500">Đăng xuất sau 30 phút không hoạt động</p>
          </div>
          <button
            type="button"
            class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            role="switch"
            aria-checked="false"
          >
            <span class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
          </button>
        </div>
      </div>
    </div>
    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Component logic for security settings
</script>
