<template>
  <div class="relative" ref="selectRef">
    <!-- Label -->
    <label
      v-if="label"
      :for="inputId"
      class="text-sm font-semibold text-gray-700 flex items-center space-x-1 mb-2"
    >
      <span>{{ label }}</span>
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- Input Field -->
    <div class="relative">
      <input
        :id="inputId"
        ref="inputRef"
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 pr-10"
        :class="[
          inputClass,
          {
            'border-red-500 focus:ring-red-500': hasError,
            'bg-gray-100 cursor-not-allowed': disabled,
            'cursor-pointer': readonly,
          },
        ]"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        @input="handleInput"
        @click="handleInputClick"
        autocomplete="off"
      />

      <!-- Dropdown Arrow -->
      <div
        class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
      >
        <svg
          class="w-5 h-5 text-gray-400 transition-transform duration-200"
          :class="{ 'rotate-180': isOpen }"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>

      <!-- Clear Button -->
      <button
        v-if="searchQuery && !readonly && !disabled && clearable"
        type="button"
        class="absolute inset-y-0 right-8 flex items-center pr-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
        @click="clearSelection"
        @mousedown.prevent
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Dropdown -->
    <Transition
      name="dropdown"
      enter-active-class="transition-all duration-200 ease-out"
      leave-active-class="transition-all duration-150 ease-in"
      enter-from-class="opacity-0 scale-95 translate-y-1"
      enter-to-class="opacity-100 scale-100 translate-y-0"
      leave-from-class="opacity-100 scale-100 translate-y-0"
      leave-to-class="opacity-0 scale-95 translate-y-1"
    >
      <div
        v-show="isOpen && !disabled"
        class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg"
        :class="[
          dropdownClass,
          {
            'bottom-full mb-1 mt-0': shouldShowUpward,
          },
        ]"
      >
        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center py-4">
          <svg
            class="animate-spin h-5 w-5 text-primary"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <span class="ml-2 text-sm text-gray-600">{{ loadingText }}</span>
        </div>

        <!-- Options List -->
        <div
          v-else
          class="overflow-y-auto"
          :style="{ maxHeight: maxDropdownHeight }"
        >
          <!-- No Results -->
          <div
            v-if="filteredOptions.length === 0"
            class="px-4 py-3 text-sm text-gray-500 text-center"
          >
            {{ noResultsText }}
          </div>

          <!-- Options -->
          <button
            v-for="(option, index) in filteredOptions"
            :key="getOptionKey(option)"
            type="button"
            class="w-full px-4 py-3 text-left text-sm hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors duration-150 border-b border-gray-100 last:border-b-0"
            :class="{
              'bg-primary text-white hover:bg-primary focus:bg-primary':
                index === highlightedIndex,
              'bg-blue-50 text-blue-700':
                isSelected(option) && index !== highlightedIndex,
            }"
            @click="selectOption(option)"
            @mouseenter="highlightedIndex = index"
          >
            <div class="flex items-center justify-between">
              <span v-html="highlightMatch(getOptionLabel(option))"></span>
              <svg
                v-if="isSelected(option)"
                class="w-4 h-4 text-current"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </button>
        </div>
      </div>
    </Transition>

    <!-- Error Message -->
    <div
      v-if="errorMessage"
      class="absolute -bottom-5 left-0 text-xs text-red-500"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick, onMounted, onUnmounted } from "vue";
import {
  filterVietnameseText,
  highlightVietnameseText,
  debounce,
} from "~/utils/vietnameseSearch";

// Types
interface Option {
  [key: string]: any;
}

interface Props {
  modelValue?: any;
  options?: Option[];
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  clearable?: boolean;
  loading?: boolean;
  loadingText?: string;
  noResultsText?: string;
  errorMessage?: string;
  inputClass?: string;
  dropdownClass?: string;
  maxDropdownHeight?: string;
  searchFields?: string[];
  valueKey?: string;
  labelKey?: string;
  searchDelay?: number;
  minSearchLength?: number;
  autoSelectFirst?: boolean;
  placement?: "top" | "bottom" | "auto";
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  placeholder: "Chọn một tùy chọn...",
  clearable: true,
  loadingText: "Đang tải...",
  noResultsText: "Không tìm thấy kết quả",
  maxDropdownHeight: "200px",
  searchFields: () => ["geoName", "name", "title", "label"],
  valueKey: "geoId",
  labelKey: "geoName",
  searchDelay: 300,
  minSearchLength: 0,
  autoSelectFirst: false,
  placement: "auto",
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: any];
  search: [query: string];
  select: [option: Option];
  clear: [];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}>();

// Refs
const selectRef = ref<HTMLDivElement>();
const inputRef = ref<HTMLInputElement>();

// State
const isOpen = ref(false);
const searchQuery = ref("");
const highlightedIndex = ref(-1);
const dropdownDirection = ref<"down" | "up">("down");
const inputId = ref(
  `searchable-select-${Math.random().toString(36).substring(2, 11)}`
);

// Computed
const hasError = computed(() => !!props.errorMessage);

const selectedOption = computed(() => {
  if (!props.modelValue) return null;
  return (
    props.options.find(
      (option) => getOptionValue(option) === props.modelValue
    ) || null
  );
});

const filteredOptions = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < props.minSearchLength) {
    return props.options;
  }
  return filterVietnameseText(
    props.options,
    searchQuery.value,
    props.searchFields
  );
});

const shouldShowUpward = computed(() => {
  if (props.placement === "top") return true;
  if (props.placement === "bottom") return false;
  // For 'auto', use the calculated direction
  return dropdownDirection.value === "up";
});

// Methods
const getOptionValue = (option: Option): any => {
  return option[props.valueKey] || option.value || option.id;
};

const getOptionLabel = (option: Option): string => {
  return (
    option[props.labelKey] ||
    option.label ||
    option.name ||
    option.title ||
    String(getOptionValue(option))
  );
};

const getOptionKey = (option: Option): string => {
  return String(getOptionValue(option));
};

const isSelected = (option: Option): boolean => {
  return getOptionValue(option) === props.modelValue;
};

const highlightMatch = (text: string): string => {
  if (!searchQuery.value || searchQuery.value.length < props.minSearchLength) {
    return text;
  }
  return highlightVietnameseText(text, searchQuery.value);
};

const selectOption = (option: Option) => {
  const value = getOptionValue(option);
  emit("update:modelValue", value);
  emit("select", option);
  searchQuery.value = getOptionLabel(option);
  isOpen.value = false;
  highlightedIndex.value = -1;
};

const clearSelection = () => {
  emit("update:modelValue", null);
  emit("clear");
  searchQuery.value = "";
  isOpen.value = false;
  highlightedIndex.value = -1;
  inputRef.value?.focus();
};

const handleFocus = (event: FocusEvent) => {
  emit("focus", event);
  if (!props.readonly) {
    isOpen.value = true;
    if (props.placement === "auto") {
      calculateDropdownDirection();
    }
  }
};

const handleBlur = (event: FocusEvent) => {
  emit("blur", event);
  // Delay closing to allow option selection
  setTimeout(() => {
    isOpen.value = false;
    highlightedIndex.value = -1;

    // Reset search query to selected option label if exists
    if (selectedOption.value) {
      searchQuery.value = getOptionLabel(selectedOption.value);
    } else if (!searchQuery.value) {
      searchQuery.value = "";
    }
  }, 150);
};

const handleInputClick = () => {
  if (props.readonly && !isOpen.value) {
    isOpen.value = true;
    if (props.placement === "auto") {
      calculateDropdownDirection();
    }
  }
};

const debouncedSearch = debounce((query: string) => {
  emit("search", query);
}, props.searchDelay);

const handleInput = () => {
  if (!props.readonly) {
    isOpen.value = true;
    highlightedIndex.value = -1;
    debouncedSearch(searchQuery.value);
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case "ArrowDown":
      event.preventDefault();
      if (!isOpen.value) {
        isOpen.value = true;
        if (props.placement === "auto") {
          calculateDropdownDirection();
        }
      } else {
        highlightedIndex.value = Math.min(
          highlightedIndex.value + 1,
          filteredOptions.value.length - 1
        );
      }
      break;

    case "ArrowUp":
      event.preventDefault();
      if (isOpen.value) {
        highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
      }
      break;

    case "Enter":
      event.preventDefault();
      if (isOpen.value && highlightedIndex.value >= 0) {
        selectOption(filteredOptions.value[highlightedIndex.value]);
      } else if (props.autoSelectFirst && filteredOptions.value.length > 0) {
        selectOption(filteredOptions.value[0]);
      }
      break;

    case "Escape":
      event.preventDefault();
      isOpen.value = false;
      highlightedIndex.value = -1;
      inputRef.value?.blur();
      break;

    case "Tab":
      isOpen.value = false;
      highlightedIndex.value = -1;
      break;
  }
};

const calculateDropdownDirection = async () => {
  await nextTick();
  if (!selectRef.value || props.placement !== "auto") return;

  const rect = selectRef.value.getBoundingClientRect();
  const spaceBelow = window.innerHeight - rect.bottom;
  const spaceAbove = rect.top;
  const dropdownHeight = parseInt(props.maxDropdownHeight) || 200;

  // If there's not enough space below and more space above, open upward
  dropdownDirection.value =
    spaceBelow < dropdownHeight && spaceAbove > spaceBelow ? "up" : "down";
};

const handleClickOutside = (event: Event) => {
  if (selectRef.value && !selectRef.value.contains(event.target as Node)) {
    isOpen.value = false;
    highlightedIndex.value = -1;
  }
};

// Watchers
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && selectedOption.value) {
      searchQuery.value = getOptionLabel(selectedOption.value);
    } else {
      searchQuery.value = "";
    }
  },
  { immediate: true }
);

// Watch for options changes to update display name when options are loaded
watch(
  () => props.options,
  (newOptions) => {
    if (newOptions && newOptions.length > 0 && props.modelValue) {
      const option = newOptions.find(
        (opt) => getOptionValue(opt) === props.modelValue
      );
      if (option && !searchQuery.value) {
        searchQuery.value = getOptionLabel(option);
      }
    }
  },
  { immediate: true, deep: true }
);

// Additional watcher to handle cases where modelValue is set before options are loaded
watch(
  [() => props.modelValue, () => props.options],
  ([newValue, newOptions]) => {
    if (newValue && newOptions && newOptions.length > 0) {
      const option = newOptions.find((opt) => getOptionValue(opt) === newValue);
      if (option && (!searchQuery.value || searchQuery.value === "")) {
        searchQuery.value = getOptionLabel(option);
      }
    }
  },
  { immediate: true }
);

watch(isOpen, (newValue) => {
  if (newValue && props.placement === "auto") {
    calculateDropdownDirection();
  }
});

// Lifecycle
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped>
/* Custom scrollbar for dropdown */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Highlight styles for search matches */
:deep(mark) {
  @apply bg-yellow-200 text-yellow-900 px-1 rounded;
}
</style>
