<template>
  <header class="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50">
    <div class="px-6 py-3">
      <div class="flex items-center justify-between">
        <!-- Left Section - Logo and Title -->
        <div class="flex items-center space-x-3">
          <!-- Logo -->
          <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 120 120"
              fill="none"
              class="w-5 h-5"
            >
              <path
                d="M15.0498 56.1001V78.5501C15.0498 101 24.0498 110 46.4998 110H73.4498C95.8998 110 104.9 101 104.9 78.5501V56.1001"
                stroke="#FAFAFA"
                stroke-width="6"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M59.9998 56.1001C70.4932 56.1001 78.9998 47.5935 78.9998 37.1001C78.9998 26.6067 70.4932 18.1001 59.9998 18.1001C49.5064 18.1001 40.9998 26.6067 40.9998 37.1001C40.9998 47.5935 49.5064 56.1001 59.9998 56.1001Z"
                stroke="#FAFAFA"
                stroke-width="6"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M60 85C51.65 85 47.5 89.15 47.5 97.5V110H72.5V97.5C72.5 89.15 68.35 85 60 85Z"
                stroke="#FAFAFA"
                stroke-width="6"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <!-- Title -->
          <div>
            <h1 class="text-lg font-bold text-gray-900">Chọn cửa hàng</h1>
          </div>
        </div>

        <!-- Right Section - User Menu -->
        <div class="flex items-center space-x-3">
          <!-- User Menu -->
          <div class="relative" ref="userMenuRef">
            <button
              @click="toggleUserMenu"
              class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <!-- User Avatar -->
              <div class="w-7 h-7 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                <span class="text-xs font-medium text-white">
                  {{ userInitials }}
                </span>
              </div>

              <!-- User Info -->
              <div class="hidden md:block text-left">
                <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
              </div>

              <!-- Dropdown Arrow -->
              <svg
                class="w-4 h-4 text-gray-400 transition-transform"
                :class="{ 'rotate-180': showUserMenu }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            <!-- User Dropdown Menu -->
            <Transition
              enter-active-class="transition ease-out duration-200"
              enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-75"
              leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95"
            >
              <div
                v-if="showUserMenu"
                class="absolute right-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
              >
                <!-- User Info Header -->
                <div class="px-3 py-2 border-b border-gray-100">
                  <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
                  <p class="text-xs text-gray-500">{{ userRole }}</p>
                </div>

                <!-- Menu Items -->
                <div class="py-1">
                  <button
                    @click="goToProfile"
                    class="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    Hồ sơ
                  </button>

                  <button
                    @click="goToSettings"
                    class="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Cài đặt
                  </button>
                </div>

                <!-- Logout -->
                <div class="border-t border-gray-100 py-1">
                  <button
                    @click="handleLogout"
                    class="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    Đăng xuất
                  </button>
                </div>
              </div>
            </Transition>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

// Props
interface Props {
  showSearch?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: true
});

// Emits
const emit = defineEmits<{
  search: [query: string];
}>();

// Composables
const { logout } = useAuth();
const authStore = useAuthStore();
const router = useRouter();

// State
const showUserMenu = ref(false);
const searchQuery = ref('');
const userMenuRef = ref<HTMLElement>();

// Computed
const user = computed(() => authStore?.user);
const userName = computed(() => user.value?.name || user.value?.fullName || 'Người dùng');
const userEmail = computed(() => user.value?.email || '');
const userInitials = computed(() => {
  const name = userName.value;
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
});

const userRole = computed(() => {
  const roles = user.value?.roles || [];
  if (roles.includes('ORG_ADMIN')) return 'Quản trị viên';
  if (roles.includes('SALE_ADMIN')) return 'Quản lý bán hàng';
  if (roles.includes('SALE')) return 'Nhân viên bán hàng';
  return 'Nhân viên';
});

// Methods
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value;
};

const closeUserMenu = () => {
  showUserMenu.value = false;
};

const goToProfile = () => {
  closeUserMenu();
  router.push('/profile');
};

const goToSettings = () => {
  closeUserMenu();
  router.push('/settings');
};

const handleLogout = async () => {
  closeUserMenu();
  try {
    await logout();
    await router.push('/login');
  } catch (error) {
    console.error('Logout error:', error);
  }
};

// Click outside handler
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    closeUserMenu();
  }
};

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>
