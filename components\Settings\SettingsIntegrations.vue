<template>
  <div class="space-y-6">
    <!-- <PERSON>er -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">T<PERSON>ch hợp</h2>
      <p class="mt-1 text-sm text-gray-600">
        Kết nối với các <PERSON>ng dụng và dịch vụ bên ngo<PERSON>i
      </p>
    </div>

    <!-- Connected Integrations -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Đ<PERSON> kế<PERSON> nối</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between p-4 bg-white rounded-lg border border-green-200">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900">Google Analytics</h4>
              <p class="text-xs text-gray-500">Theo dõi và phân tích dữ liệu website</p>
              <p class="text-xs text-green-600 font-medium">Đã kết nối</p>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-sm text-blue-600 hover:text-blue-800">Cấu hình</button>
            <button class="text-sm text-red-600 hover:text-red-800">Ngắt kết nối</button>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-white rounded-lg border border-green-200">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900">Shopee API</h4>
              <p class="text-xs text-gray-500">Đồng bộ sản phẩm và đơn hàng</p>
              <p class="text-xs text-green-600 font-medium">Đã kết nối</p>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-sm text-blue-600 hover:text-blue-800">Cấu hình</button>
            <button class="text-sm text-red-600 hover:text-red-800">Ngắt kết nối</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Integrations -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Tích hợp có sẵn</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="p-4 bg-white rounded-lg border">
          <div class="flex items-center space-x-4 mb-3">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
              </svg>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900">Lazada API</h4>
              <p class="text-xs text-gray-500">Quản lý sản phẩm trên Lazada</p>
            </div>
          </div>
          <button
            type="button"
            class="w-full bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
          >
            Kết nối
          </button>
        </div>
        
        <div class="p-4 bg-white rounded-lg border">
          <div class="flex items-center space-x-4 mb-3">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
              </svg>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900">Tiki API</h4>
              <p class="text-xs text-gray-500">Đồng bộ với Tiki marketplace</p>
            </div>
          </div>
          <button
            type="button"
            class="w-full bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
          >
            Kết nối
          </button>
        </div>
        
        <div class="p-4 bg-white rounded-lg border">
          <div class="flex items-center space-x-4 mb-3">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
              </svg>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900">Facebook Pixel</h4>
              <p class="text-xs text-gray-500">Theo dõi chuyển đổi quảng cáo</p>
            </div>
          </div>
          <button
            type="button"
            class="w-full bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
          >
            Kết nối
          </button>
        </div>
        
        <div class="p-4 bg-white rounded-lg border">
          <div class="flex items-center space-x-4 mb-3">
            <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-indigo-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
              </svg>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900">Zalo OA</h4>
              <p class="text-xs text-gray-500">Gửi thông báo qua Zalo</p>
            </div>
          </div>
          <button
            type="button"
            class="w-full bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
          >
            Kết nối
          </button>
        </div>
      </div>
    </div>

    <!-- Webhook Settings -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-base font-medium text-gray-900 mb-4">Webhook</h3>
      <p class="text-sm text-gray-600 mb-4">
        Cấu hình webhook để nhận thông báo real-time về các sự kiện trong hệ thống
      </p>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            URL Webhook
          </label>
          <input
            type="url"
            placeholder="https://your-domain.com/webhook"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Secret Key
          </label>
          <div class="flex space-x-2">
            <input
              type="password"
              value="sk_test_123456789"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            />
            <button
              type="button"
              class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
            >
              Tạo mới
            </button>
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Sự kiện
          </label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input type="checkbox" checked class="rounded border-gray-300 text-primary focus:ring-primary" />
              <span class="ml-2 text-sm text-gray-700">Đơn hàng mới</span>
            </label>
            <label class="flex items-center">
              <input type="checkbox" checked class="rounded border-gray-300 text-primary focus:ring-primary" />
              <span class="ml-2 text-sm text-gray-700">Thanh toán thành công</span>
            </label>
            <label class="flex items-center">
              <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary" />
              <span class="ml-2 text-sm text-gray-700">Hủy đơn hàng</span>
            </label>
            <label class="flex items-center">
              <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary" />
              <span class="ml-2 text-sm text-gray-700">Cập nhật sản phẩm</span>
            </label>
          </div>
        </div>
      </div>
      
      <div class="mt-4 flex space-x-3">
        <button
          type="button"
          class="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
        >
          Lưu webhook
        </button>
        <button
          type="button"
          class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
        >
          Test webhook
        </button>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Component logic for integration settings
</script>
