<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <!-- Main Container -->
    <div class="relative w-full max-w-md mx-auto">
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8">
        <!-- Logo -->
        <div class="flex items-center justify-center space-x-3 mb-8">
          <div class="w-10 h-10 bg-primary rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
            </svg>
          </div>
          <h1 class="text-2xl font-bold text-primary">POS Online</h1>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">Tạo mật khẩu</h2>
          <p class="text-gray-600">
            Tạo mật khẩu cho tài khoản
            <span class="font-semibold text-primary">{{ phoneNumber }}</span>
          </p>
        </div>

        <!-- Password Form -->
        <form @submit.prevent="handleCreatePassword" class="space-y-6">
          <!-- Password Field -->
          <div class="space-y-2">
            <label for="password" class="block text-sm font-semibold text-gray-700">
              Mật khẩu
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <input
                id="password"
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                name="password"
                autocomplete="new-password"
                @blur="validatePassword"
                :class="[
                  'block w-full pl-10 pr-12 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                  errors.password
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 bg-white hover:border-gray-400',
                ]"
                placeholder="Nhập mật khẩu mới"
              />
              <button
                v-if="password"
                type="button"
                @click="togglePasswordVisibility"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <svg v-if="showPassword" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                </svg>
                <svg v-else class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
            <Transition name="slide-down">
              <span v-if="errors.password" class="text-sm text-red-600 flex items-center space-x-1">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ errors.password }}</span>
              </span>
            </Transition>
          </div>

          <!-- Confirm Password Field -->
          <div class="space-y-2">
            <label for="confirmPassword" class="block text-sm font-semibold text-gray-700">
              Xác nhận mật khẩu
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <input
                id="confirmPassword"
                v-model="confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                name="confirmPassword"
                autocomplete="new-password"
                @blur="validateConfirmPassword"
                :class="[
                  'block w-full pl-10 pr-12 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                  errors.confirmPassword
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 bg-white hover:border-gray-400',
                ]"
                placeholder="Nhập lại mật khẩu"
              />
              <button
                v-if="confirmPassword"
                type="button"
                @click="toggleConfirmPasswordVisibility"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <svg v-if="showConfirmPassword" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                </svg>
                <svg v-else class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
            <Transition name="slide-down">
              <span v-if="errors.confirmPassword" class="text-sm text-red-600 flex items-center space-x-1">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ errors.confirmPassword }}</span>
              </span>
            </Transition>
          </div>

          <!-- Password Requirements -->
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-semibold text-gray-700 mb-2">Yêu cầu mật khẩu:</h4>
            <ul class="text-xs text-gray-600 space-y-1">
              <li class="flex items-center space-x-2">
                <span :class="password.length >= 6 ? 'text-green-500' : 'text-gray-400'">✓</span>
                <span>Ít nhất 6 ký tự</span>
              </li>
            </ul>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
          >
            <span v-if="loading" class="flex items-center space-x-2">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Đang xử lý...</span>
            </span>
            <span v-else class="flex items-center space-x-2">
              <span>Hoàn tất đăng ký</span>
              <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </span>
          </button>
        </form>

        <!-- Back Link -->
        <div class="text-center mt-6">
          <button
            @click="goBack"
            class="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200 hover:underline"
          >
            ← Quay lại xác thực OTP
          </button>
        </div>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div v-if="loading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Tạo mật khẩu",
  meta: [
    { name: "description", content: "Tạo mật khẩu để hoàn tất đăng ký tài khoản" }
  ]
});

const route = useRoute();
const router = useRouter();

// Extract params from route
const phoneNumber = ref((route.query.phone as string) || "");
const customerName = ref((route.query.customerName as string) || "");
const userLoginId = ref((route.query.userLoginId as string) || "");
const serviceType = ref((route.query.serviceType as string) || "POS");

// Form state
const password = ref("");
const confirmPassword = ref("");
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const loading = ref(false);

// Error states
const errors = ref({
  password: "",
  confirmPassword: "",
});

// Computed form validation
const isFormValid = computed(() => {
  return password.value.length >= 6 &&
         password.value === confirmPassword.value &&
         !errors.value.password &&
         !errors.value.confirmPassword;
});

// Password visibility toggles
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

// Validation functions
const validatePassword = () => {
  if (!password.value) {
    errors.value.password = "Vui lòng nhập mật khẩu";
    return false;
  }
  if (password.value.length < 6) {
    errors.value.password = "Mật khẩu phải có ít nhất 6 ký tự";
    return false;
  }
  errors.value.password = "";

  // Re-validate confirm password if it exists
  if (confirmPassword.value) {
    validateConfirmPassword();
  }

  return true;
};

const validateConfirmPassword = () => {
  if (!confirmPassword.value) {
    errors.value.confirmPassword = "Vui lòng xác nhận mật khẩu";
    return false;
  }
  if (password.value !== confirmPassword.value) {
    errors.value.confirmPassword = "Mật khẩu xác nhận không khớp";
    return false;
  }
  errors.value.confirmPassword = "";
  return true;
};

// Use register composable for password creation
const {
  phoneNumber: composablePhone,
  customerName: composableCustomerName,
  userLoginId: composableUserLoginId,
  registrationService: composableRegistrationService,
  handlePasswordCreation,
  loading: composableLoading
} = useRegister();

// Create password handler
const handleCreatePassword = async () => {
  if (!validatePassword() || !validateConfirmPassword()) return;

  // Sync values with composable before calling handlePasswordCreation
  composablePhone.value = phoneNumber.value;
  composableCustomerName.value = customerName.value;
  composableUserLoginId.value = userLoginId.value;
  composableRegistrationService.value = serviceType.value;

  console.log('🔍 Synced values to composable:', {
    phone: composablePhone.value,
    customerName: composableCustomerName.value,
    userLoginId: composableUserLoginId.value,
    serviceType: composableRegistrationService.value
  });

  loading.value = true;

  const success = await handlePasswordCreation(password.value, confirmPassword.value);

  loading.value = false;
};



// Go back to OTP verification
const goBack = () => {
  router.push({
    path: '/register/otp-verification',
    query: {
      phone: phoneNumber.value,
      customerName: customerName.value,
      userLoginId: userLoginId.value,
      serviceType: serviceType.value
    }
  });
};

// Validate required params on mount
onMounted(() => {
  if (!phoneNumber.value || !userLoginId.value) {
    console.error('❌ Missing required params for password creation');
    useNuxtApp().$toast.error("Thông tin không hợp lệ");
    router.push('/register');
    return;
  }

  console.log('✅ Password creation page initialized with params:', {
    phone: phoneNumber.value,
    customerName: customerName.value,
    userLoginId: userLoginId.value,
    serviceType: serviceType.value
  });

  // Pre-sync values with composable
  composablePhone.value = phoneNumber.value;
  composableCustomerName.value = customerName.value;
  composableUserLoginId.value = userLoginId.value;
  composableRegistrationService.value = serviceType.value;
});
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
