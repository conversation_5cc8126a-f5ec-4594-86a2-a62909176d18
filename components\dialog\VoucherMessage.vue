<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg max-w-4xl w-full animate-popup overflow-hidden"
    >
      <header
        class="flex items-center justify-between px-6 py-4 border-b bg-white text-primary"
      >
        <h1 class="text-lg font-semibold text-primary">Thông tin mã giảm giá</h1>
        <button
          @click="cancel"
          class="p-2 text-primary hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-200"
          aria-label="Đóng dialog"
          type="button"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </header>
      <!-- <PERSON>ần nội dung -->
      <div class="max-h-[70vh] overflow-y-auto p-4">
        <div v-for="campaign in campaigns">
          <ListCampaign
            :campaign="campaign"
            :dataVoucher="dataVoucher"
            @get-campaign="handleSuggestVoucher"
          ></ListCampaign>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const { searchVoucher, getVoucherAvailableForCustomer, suggestVoucher } =
  useCampaign();
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const orderStore = useOrderStore();
const campaigns = computed(() =>
  orderStore?.campaign?.filter((campaign: any) =>
    [
      "PROMOTION_VOUCHER",
      "PROMOTION_BIRTH_DAY",
      "PROMOTION_POINT_TO_VOUCHER",
    ].includes(campaign.type)
  )
);

const dataVoucher = ref([]);
const customer = computed(() => orderStore.customerInOrder);

const handleSuggestVoucher = async () => {
  try {
    const response = await suggestVoucher(customer.value?.id,"","");
    dataVoucher.value = response?.content;
    return response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleSuggestVoucher();
});
</script>

<style scoped></style>
