import type { ModuleDefinition } from '~/types/onboarding';

// Simplified onboarding modules - chỉ 3-4 bước cốt lõi
export const ONBOARDING_MODULES: ModuleDefinition = {
  // Module cốt lõi - bắt buộc cho tất cả
  organization: {
    id: 'organization',
    name: 'Tạo Tổ Chức',
    description: '<PERSON>hiết lập thông tin tổ chức cơ bản',
    icon: 'building-office',
    color: 'blue',
    category: 'core',
    serviceTypes: ['POS', 'CRM', 'WAREHOUSE', 'TELESALE', 'SALEPOINT', 'MARKETING'],
    steps: [
      {
        id: 'create-organization',
        title: 'Tạo Tổ Chức',
        description: 'Nhập thông tin cơ bản về tổ chức của bạn',
        component: 'CreateOrganization',
        module: 'organization',
        required: true,
        order: 1,
        estimatedTime: 3,
        validation: [
          { field: 'orgName', type: 'required', message: '<PERSON><PERSON><PERSON> tổ chức không được để trống' },
          { field: 'industry', type: 'required', message: '<PERSON><PERSON> lòng chọn ngành nghề' }
        ]
      }
    ]
  },

  // Module POS - Đơn giản hóa
  pos: {
    id: 'pos',
    name: 'Thiết Lập POS',
    description: 'Cấu hình hệ thống bán hàng cơ bản',
    icon: 'shopping-cart',
    color: 'green',
    category: 'business',
    serviceTypes: ['POS', 'SALEPOINT'],
    steps: [
      {
        id: 'create-store',
        title: 'Tạo Cửa Hàng',
        description: 'Tạo cửa hàng để bắt đầu bán hàng',
        component: 'CreateStore',
        module: 'pos',
        required: true,
        order: 1,
        estimatedTime: 5,
        validation: [
          { field: 'storeName', type: 'required', message: 'Tên cửa hàng không được để trống' },
          { field: 'address', type: 'required', message: 'Địa chỉ không được để trống' }
        ]
      },

    ]
  },

  // Module TELESALE - Chỉ import khách hàng
  telesale: {
    id: 'telesale',
    name: 'Thiết Lập Telesale',
    description: 'Import dữ liệu khách hàng cho telesale',
    icon: 'phone',
    color: 'purple',
    category: 'business',
    serviceTypes: ['TELESALE'],
    steps: [
      {
        id: 'import-customers',
        title: 'Import Khách Hàng',
        description: 'Import danh sách khách hàng để bắt đầu telesale',
        component: 'ImportCustomers',
        module: 'telesale',
        required: false,
        order: 1,
        estimatedTime: 5,
        validation: []
      }
    ]
  }
};

// Component mapping cho modules
export const COMPONENT_MAP = {
  // Organization components
  "CreateOrganization": () => import("~/components/onboarding/modules/organization/CreateOrganization.vue"),

  // POS components - simplified
  "CreateStore": () => import("~/components/onboarding/modules/pos/CreateStore.vue"),

  // TELESALE components
  "ImportCustomers": () => import("~/components/onboarding/modules/crm/ImportCustomers.vue"),
};

// Service type to modules mapping - simplified
export const SERVICE_MODULE_MAP = {
  'POS': ['organization', 'pos'],
  'SALEPOINT': ['organization', 'pos'],
  'TELESALE': ['organization', 'telesale']
};

// Generate steps for service types - simplified
export const generateSteps = (serviceTypes: string[]) => {
  const allModules = new Set<string>();

  // Collect modules for all service types
  serviceTypes.forEach(serviceType => {
    const modules = SERVICE_MODULE_MAP[serviceType as keyof typeof SERVICE_MODULE_MAP] || [];
    modules.forEach(module => allModules.add(module));
  });

  // Generate steps from modules
  const steps: any[] = [];
  let globalOrder = 1;

  Array.from(allModules).forEach(moduleId => {
    const module = ONBOARDING_MODULES[moduleId as keyof typeof ONBOARDING_MODULES];

    if (module) {
      module.steps.forEach(step => {
        // Include all steps (both required and optional)
        steps.push({
          ...step,
          order: globalOrder++,
          completed: false,
          data: null
        });
      });
    }
  });

  // Sort by order
  return steps.sort((a, b) => a.order - b.order);
};
