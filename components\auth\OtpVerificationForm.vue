<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <!-- Main Container -->
    <div class="relative w-full max-w-md mx-auto">
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8">
        <!-- Logo -->
        <div class="flex items-center justify-center space-x-3 mb-8">
          <div class="w-10 h-10 bg-primary rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <!-- Shield/Security icon for OTP verification -->
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <h1 class="text-2xl font-bold text-primary">POS Online</h1>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">{{ title }}</h2>
          <p class="text-gray-600" v-html="description"></p>
        </div>

        <!-- OTP Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- OTP Field -->
          <div class="space-y-2">
            <label class="block text-sm font-semibold text-gray-700 text-center">
              Mã OTP
            </label>
            <div class="flex justify-center space-x-3">
              <input
                v-for="(digit, index) in otpDigits"
                :key="index"
                :ref="(el) => setOtpInputRef(el as HTMLInputElement, index)"
                v-model="otpDigits[index]"
                type="text"
                inputmode="numeric"
                pattern="[0-9]*"
                maxlength="1"
                autocomplete="one-time-code"
                :class="getOtpInputClasses(index)"
                @input="handleOtpInput(index, $event)"
                @keydown="handleOtpKeydown(index, $event)"
                @keypress="handleOtpKeypress($event)"
                @paste="handleOtpPaste($event)"
              />
            </div>
            <Transition name="slide-down">
              <div v-if="error" class="text-sm text-red-600 text-center">
                {{ error }}
              </div>
            </Transition>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="loading || otp.length !== 6"
            class="w-full bg-primary text-white py-3 px-4 rounded-xl font-medium hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loadingText }}
            </span>
            <span v-else>{{ submitText }}</span>
          </button>

          <!-- OTP Expiry & Resend -->
          <div class="text-center space-y-2">
            <!-- OTP Expiry Countdown -->
            <div v-if="otpExpiry > 0" class="flex items-center justify-center text-xs text-gray-600 bg-gray-50 rounded-lg px-3 py-2">
              <svg
                class="w-4 h-4 mr-2 text-orange-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="font-medium">
                Mã OTP còn hiệu lực {{ Math.floor(otpExpiry / 60) }}:{{ String(otpExpiry % 60).padStart(2, '0') }}
              </span>
            </div>

            <!-- Resend Options -->
            <div v-if="showResend" class="space-y-2">
              <p class="text-sm text-gray-600">Không nhận được mã?</p>
              <div class="flex justify-center space-x-4">
                <!-- Resend SMS -->
                <button
                  type="button"
                  @click="$emit('resend')"
                  :disabled="smsCountdown > 0"
                  class="text-sm text-primary hover:text-primary/80 font-medium transition-colors duration-200 hover:underline disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {{ smsCountdown > 0 ? `Gửi lại sau ${smsCountdown}s` : 'Gửi lại SMS' }}
                </button>

                <span class="text-gray-300">|</span>

                <!-- Send via Zalo -->
                <button
                  type="button"
                  @click="$emit('resend-zalo')"
                  :disabled="zaloCountdown > 0"
                  class="text-sm text-primary hover:text-primary/80 font-medium transition-colors duration-200 hover:underline disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {{ zaloCountdown > 0 ? `Gửi qua Zalo sau ${zaloCountdown}s` : 'Gửi qua Zalo' }}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  description: string;
  phoneNumber: string;
  loading?: boolean;
  error?: string;
  loadingText?: string;
  submitText?: string;
  showResend?: boolean;
  smsCountdown?: number;
  zaloCountdown?: number;
  otpExpiry?: number;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: '',
  loadingText: 'Đang xác thực...',
  submitText: 'Xác thực OTP',
  showResend: true,
  smsCountdown: 0,
  zaloCountdown: 0,
  otpExpiry: 0
});

const emit = defineEmits<{
  submit: [otp: string];
  resend: [];
  'resend-zalo': [];
}>();

// Use shared OTP logic
const {
  otpDigits,
  otp,
  setOtpInputRef,
  handleOtpInput,
  handleOtpKeydown,
  handleOtpKeypress,
  handleOtpPaste,
  focusFirstInput
} = useOtpInput();

// OTP input classes
const getOtpInputClasses = (index: number) => [
  'w-12 h-12 text-center text-xl font-bold border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
  props.error
    ? 'border-red-300 bg-red-50'
    : 'border-gray-300 bg-white hover:border-gray-400',
  otpDigits.value[index] ? 'border-primary bg-primary/5' : ''
];

// Handle form submit
const handleSubmit = () => {
  if (otp.value.length === 6) {
    emit('submit', otp.value);
  }
};

// Focus first input on mount
onMounted(() => {
  focusFirstInput();
});
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
