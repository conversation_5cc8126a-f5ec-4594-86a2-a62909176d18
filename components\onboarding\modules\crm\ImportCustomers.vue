<template>
  <div class="import-customers-step">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h2 class="text-2xl font-bold text-gray-800 mb-2">Nhập <PERSON><PERSON> Sách Khách Hàng</h2>
      <p class="text-gray-600 max-w-md mx-auto">
        Import danh sách khách hàng từ Excel hoặc thêm khách hàng mẫu để bắt đầu
      </p>
    </div>

    <!-- Options -->
    <div class="max-w-4xl mx-auto space-y-6">
      <!-- Import Method Selection -->
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900">Ch<PERSON><PERSON> cách thêm khách hàng</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Excel Import -->
          <div
            class="relative p-6 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 hover:border-purple-300 hover:bg-purple-50"
            :class="selectedMethod === 'excel' ? 'border-purple-500 bg-purple-50' : 'border-gray-300'"
            @click="selectedMethod = 'excel'"
          >
            <div class="text-center">
              <div class="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
              <h4 class="font-medium text-gray-900 mb-2">Import từ Excel</h4>
              <p class="text-sm text-gray-600">Tải lên file Excel với danh sách khách hàng</p>
            </div>
            <div
              v-if="selectedMethod === 'excel'"
              class="absolute top-2 right-2 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center"
            >
              <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

          <!-- Sample Data -->
          <div
            class="relative p-6 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 hover:border-purple-300 hover:bg-purple-50"
            :class="selectedMethod === 'sample' ? 'border-purple-500 bg-purple-50' : 'border-gray-300'"
            @click="selectedMethod = 'sample'"
          >
            <div class="text-center">
              <div class="w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 class="font-medium text-gray-900 mb-2">Dữ liệu mẫu</h4>
              <p class="text-sm text-gray-600">Thêm một số khách hàng mẫu để test</p>
            </div>
            <div
              v-if="selectedMethod === 'sample'"
              class="absolute top-2 right-2 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center"
            >
              <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

       
        </div>
      </div>

      <!-- Excel Import Section -->
      <div v-if="selectedMethod === 'excel'" class="space-y-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-medium text-blue-900 mb-2">Hướng dẫn import Excel</h4>
          <ul class="text-sm text-blue-800 space-y-1">
            <li>• File Excel phải có các cột: Tên, Số điện thoại, Email, Địa chỉ</li>
            <li>• Dòng đầu tiên là tiêu đề cột</li>
            <li>• Định dạng file: .xlsx hoặc .xls</li>
            <li>• Tối đa 1000 khách hàng mỗi lần import</li>
          </ul>
          <div class="mt-3">
            <button
              @click="downloadTemplate"
              class="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Tải xuống file mẫu
            </button>
          </div>
        </div>

        <!-- File Upload -->
        <div
          class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors duration-200"
          :class="{ 'border-purple-500 bg-purple-50': isDragOver }"
          @dragover.prevent="isDragOver = true"
          @dragleave.prevent="isDragOver = false"
          @drop.prevent="handleFileDrop"
        >
          <input
            ref="fileInput"
            type="file"
            accept=".xlsx,.xls"
            class="hidden"
            @change="handleFileSelect"
          />
          
          <div v-if="!selectedFile">
            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
            <p class="text-lg font-medium text-gray-900 mb-2">Kéo thả file Excel vào đây</p>
            <p class="text-gray-600 mb-4">hoặc</p>
            <button
              @click="$refs.fileInput.click()"
              class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors duration-200"
            >
              Chọn file
            </button>
          </div>

          <div v-else class="space-y-4">
            <div class="flex items-center justify-center space-x-3">
              <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <div>
                <p class="font-medium text-gray-900">{{ selectedFile.name }}</p>
                <p class="text-sm text-gray-600">{{ formatFileSize(selectedFile.size) }}</p>
              </div>
            </div>
            <div class="flex justify-center">
              <button
                @click="selectedFile = null"
                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
              >
                Chọn file khác
              </button>
            </div>
            <p class="text-sm text-green-700 font-medium mt-3">
              ✓ File đã sẵn sàng. Nhấn "Tiếp tục" bên dưới để import.
            </p>
          </div>
        </div>

        <!-- Import Results -->
        <div v-if="importResults" class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 class="font-medium text-green-900 mb-2">Kết quả import</h4>
          <div class="text-sm text-green-800">
            <p>✅ Đã import thành công {{ importResults.success }} khách hàng</p>
            <p v-if="importResults.errors > 0" class="text-orange-700">
              ⚠️ {{ importResults.errors }} dòng có lỗi đã bị bỏ qua
            </p>
          </div>
        </div>
      </div>

      <!-- Sample Data Section -->
      <div v-if="selectedMethod === 'sample'" class="space-y-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-medium text-blue-900 mb-2">Dữ liệu mẫu</h4>
          <p class="text-sm text-blue-800 mb-3">
            Chúng tôi sẽ thêm {{ sampleCustomers.length }} khách hàng mẫu để bạn có thể test hệ thống ngay lập tức.
          </p>
          
          <!-- Sample customers preview -->
          <div class="bg-white rounded border overflow-hidden">
            <div class="grid grid-cols-4 gap-4 p-3 bg-gray-50 text-xs font-medium text-gray-700 border-b">
              <div>Tên khách hàng</div>
              <div>Số điện thoại</div>
              <div>Email</div>
              <div>Địa chỉ</div>
            </div>
            <div class="max-h-40 overflow-y-auto">
              <div
                v-for="customer in sampleCustomers.slice(0, 5)"
                :key="customer.phone"
                class="grid grid-cols-4 gap-4 p-3 text-xs border-b border-gray-100"
              >
                <div class="font-medium">{{ customer.name }}</div>
                <div>{{ customer.phone }}</div>
                <div>{{ customer.email }}</div>
                <div>{{ customer.address }}</div>
              </div>
            </div>
            <div v-if="sampleCustomers.length > 5" class="p-2 text-center text-xs text-gray-500 bg-gray-50">
              ... và {{ sampleCustomers.length - 5 }} khách hàng khác
            </div>
          </div>

          <p class="mt-3 text-sm text-blue-700 font-medium">
            ✓ Sẵn sàng thêm dữ liệu mẫu. Nhấn "Tiếp tục" bên dưới để bắt đầu.
          </p>
        </div>
      </div>



      <!-- Progress -->
      <div 
        v-if="loading"
        class="p-4 bg-purple-50 rounded-lg border border-purple-200"
      >
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600"></div>
          <span class="text-sm font-medium text-purple-800">
            {{ loadingMessage }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { StepComponentProps, StepResult } from '~/types/onboarding';

// Props
const props = defineProps<StepComponentProps>();

// State
const selectedMethod = ref<'excel' | 'sample' | 'skip'>('sample');
const selectedFile = ref<File | null>(null);
const isDragOver = ref(false);
const loading = ref(false);
const loadingMessage = ref('');
const importResults = ref<{ success: number; errors: number } | null>(null);

// Sample customers data
const sampleCustomers = [
  { name: 'Nguyễn Văn An', phone: '0901234567', email: '<EMAIL>', address: '123 Nguyễn Huệ, Q1, TP.HCM' },
  { name: 'Trần Thị Bình', phone: '0912345678', email: '<EMAIL>', address: '456 Lê Lợi, Q1, TP.HCM' },
  { name: 'Lê Văn Cường', phone: '0923456789', email: '<EMAIL>', address: '789 Hai Bà Trưng, Q3, TP.HCM' },
  { name: 'Phạm Thị Dung', phone: '0934567890', email: '<EMAIL>', address: '321 Võ Văn Tần, Q3, TP.HCM' },
  { name: 'Hoàng Văn Em', phone: '0945678901', email: '<EMAIL>', address: '654 Cách Mạng Tháng 8, Q10, TP.HCM' },
  { name: 'Vũ Thị Phương', phone: '0956789012', email: '<EMAIL>', address: '987 Nguyễn Thị Minh Khai, Q1, TP.HCM' },
  { name: 'Đặng Văn Giang', phone: '0967890123', email: '<EMAIL>', address: '147 Pasteur, Q1, TP.HCM' },
  { name: 'Bùi Thị Hoa', phone: '0978901234', email: '<EMAIL>', address: '258 Điện Biên Phủ, Q3, TP.HCM' }
];

// Computed property to check if step can proceed
const canProceed = computed(() => {
  if (loading.value) return false;

  switch (selectedMethod.value) {
    case 'sample':
      return true; // Can proceed with sample data
    case 'excel':
      return selectedFile.value !== null; // Can proceed when file is selected
    case 'skip':
      return true; // Can always skip
    default:
      return false;
  }
});

// Emit canProceed status to parent
const emit = defineEmits<{
  'update:can-proceed': [value: boolean];
}>();

// Watch canProceed and emit to parent
watch(canProceed, (newValue) => {
  emit('update:can-proceed', newValue);
}, { immediate: true });

// Also watch selectedMethod to trigger canProceed update
watch(selectedMethod, () => {
  // Trigger canProceed recalculation
  nextTick(() => {
    emit('update:can-proceed', canProceed.value);
  });
});

// Watch selectedFile to update canProceed when file is selected
watch(selectedFile, () => {
  nextTick(() => {
    emit('update:can-proceed', canProceed.value);
  });
});

// Handle continue action from main button
const handleContinue = async () => {
  if (!canProceed.value) return;

  switch (selectedMethod.value) {
    case 'sample':
      await importSampleData();
      break;
    case 'skip':
      skipStep();
      break;
    case 'excel':
      await processExcelFile();
      break;
  }
};

// Expose handleContinue to parent
defineExpose({
  handleContinue,
  canProceed
});

// Methods
const handleFileDrop = (event: DragEvent) => {
  isDragOver.value = false;
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    selectedFile.value = files[0];
  }
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0];
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const downloadTemplate = () => {
  // Create and download Excel template
  const csvContent = "Tên,Số điện thoại,Email,Địa chỉ\nNguyễn Văn A,0901234567,<EMAIL>,123 ABC Street";
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'mau-danh-sach-khach-hang.csv';
  link.click();
};

const processExcelFile = async () => {
  if (!selectedFile.value) return;

  loading.value = true;
  loadingMessage.value = 'Đang xử lý file Excel...';

  try {
    // TODO: Implement actual Excel processing
    // For now, simulate processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate import results
    importResults.value = {
      success: Math.floor(Math.random() * 50) + 10,
      errors: Math.floor(Math.random() * 5)
    };

    loadingMessage.value = 'Import hoàn tất!';

    // Complete step automatically after import
    const result: StepResult = {
      stepId: props.step.id,
      success: true,
      data: {
        method: 'excel',
        fileName: selectedFile.value.name,
        importResults: importResults.value
      }
    };

    useNuxtApp().$toast.success(`Import thành công ${importResults.value.success} khách hàng!`);
    props.onComplete(result);

  } catch (error: any) {
    console.error('❌ Error processing Excel file:', error);
    useNuxtApp().$toast.error('Có lỗi xảy ra khi xử lý file Excel');
  } finally {
    loading.value = false;
  }
};

const importSampleData = async () => {
  loading.value = true;
  loadingMessage.value = 'Đang thêm dữ liệu mẫu...';

  try {
    const $sdk = useNuxtApp().$sdk;
    let successCount = 0;
    let errorCount = 0;

    console.log('📋 Adding sample customers...');

    // Add each sample customer via SDK
    for (const customer of sampleCustomers) {
      try {
        await $sdk.user.createCustomerV2({
          name: customer.name,
          phone: customer.phone,
          email: customer.email,
          birthDate: new Date()
        }, ''); // createdBy parameter
        successCount++;
        loadingMessage.value = `Đã thêm ${successCount}/${sampleCustomers.length} khách hàng...`;
      } catch (error) {
        console.error(`❌ Error adding customer ${customer.name}:`, error);
        errorCount++;
      }
    }

    console.log(`✅ Added ${successCount} customers, ${errorCount} errors`);

    // Complete step
    const result: StepResult = {
      stepId: props.step.id,
      success: true,
      data: {
        method: 'sample',
        customersAdded: successCount,
        errors: errorCount,
        customers: sampleCustomers
      }
    };

    useNuxtApp().$toast.success(`Đã thêm ${successCount} khách hàng mẫu!`);
    props.onComplete(result);

  } catch (error: any) {
    console.error('❌ Error importing sample data:', error);
    useNuxtApp().$toast.error('Có lỗi xảy ra khi thêm dữ liệu mẫu');
  } finally {
    loading.value = false;
  }
};

const skipStep = () => {
  const result: StepResult = {
    stepId: props.step.id,
    success: true,
    data: {
      method: 'skip',
      skipped: true
    }
  };

  props.onComplete(result);
};

// Initialize
onMounted(() => {
  // Load existing data if available
  if (props.step.data?.method) {
    selectedMethod.value = props.step.data.method;
  }
});
</script>

<style scoped>
/* Custom animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth transitions */
.import-customers-step * {
  transition: all 0.2s ease-in-out;
}
</style>
