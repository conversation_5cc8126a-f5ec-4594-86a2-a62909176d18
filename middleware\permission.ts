// middleware/auth.ts
import type { User } from "~/stores/auth";
import type { Roles } from "~/types";
interface AuthData {
  token: string;
  user: User;
}
export default defineNuxtRouteMiddleware(async (to, _from) => {
  const { isFeatureAccessible } = usePermission();

  // Try multiple ways to get auth data
  const authCookie = useCookie<AuthData>("auth").value;
  const authStore = useAuthStore();
  const authStoreUser = authStore.user;

  const rolesPath = to.meta.permission as Roles;

  // Debug logs
  console.log("🔒 Permission middleware check for:", to.path);
  console.log("🍪 Auth cookie:", authCookie);
  console.log("🏪 Auth store user:", authStoreUser);
  console.log("🔑 Cookie user roles:", authCookie?.user?.roles);
  console.log("🔑 Store user roles:", authStoreUser?.roles);
  console.log("📋 Required roles:", rolesPath);

  // Use store data as primary, cookie as fallback
  const userRoles = authStoreUser?.roles || authCookie?.user?.roles || [];

  const isAccess = isFeatureAccessible(userRoles, rolesPath);
  console.log("✅ Access granted:", isAccess, "with roles:", userRoles);

  if (!isAccess && rolesPath) {
    // If user has no roles or undefined roles, redirect to org-store to select organization
    if (!userRoles || userRoles.length === 0) {
      console.log("⚠️ User has no roles - redirecting to org-store to select organization");
      return navigateTo("/org-store");
    }
    console.log("❌ Access denied - redirecting to /access-denied");
    return navigateTo("/access-denied");
  }
});
