<template>
  <div
    class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4"
  >
    <!-- Background Pattern -->
    <div
      class="absolute inset-0 opacity-40"
      style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%239C92AC&quot; fill-opacity=&quot;0.05&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;4&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"
    ></div>

    <!-- Main Container -->
    <div class="relative w-full max-w-md mx-auto">
      <div
        class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8"
      >
        <!-- Logo -->
        <div class="flex items-center justify-center space-x-3 mb-8">
          <div
            class="w-10 h-10 bg-primary rounded-xl flex items-center justify-center"
          >
            <svg
              class="w-6 h-6 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
              ></path>
            </svg>
          </div>
          <h1 class="text-2xl font-bold text-primary">POS Online</h1>
        </div>

        <!-- Form Header -->
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">Xác nhận OTP</h2>
          <p class="text-gray-600 mb-2">
            Mã OTP đã được gửi đến số điện thoại
          </p>
          <p class="text-primary font-semibold">{{ route.query.phone }}</p>
        </div>

        <!-- OTP and Password Form -->
        <form @submit.prevent="handleOtpVerification" class="space-y-6">
          <!-- OTP Field -->
          <div class="space-y-2">
            <label class="block text-sm font-semibold text-gray-700 text-center">
              Mã OTP
            </label>
            <div class="flex justify-center space-x-3">
              <input
                v-for="(digit, index) in otpDigits"
                :key="index"
                :ref="(el) => setOtpInputRef(el as HTMLInputElement, index)"
                v-model="otpDigits[index]"
                type="text"
                inputmode="numeric"
                pattern="[0-9]*"
                maxlength="1"
                autocomplete="one-time-code"
                :class="[
                  'w-12 h-12 text-center text-xl font-bold border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                  errors.otp
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 bg-white hover:border-gray-400',
                  otpDigits[index] ? 'border-primary bg-primary/5' : ''
                ]"
                @input="handleOtpInput(index, $event)"
                @keydown="handleOtpKeydown(index, $event)"
                @keypress="handleOtpKeypress($event)"
                @paste="handleOtpPaste($event)"
              />
            </div>
            <Transition name="slide-down">
              <span
                v-if="errors.otp"
                class="text-sm text-red-600 flex items-center justify-center space-x-1"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>{{ errors.otp }}</span>
              </span>
            </Transition>
          </div>

          <!-- Password Field -->
          <div class="space-y-2">
            <label
              for="password"
              class="block text-sm font-semibold text-gray-700"
            >
              Mật khẩu mới
            </label>
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <svg
                  class="h-5 w-5 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <input
                id="password"
                v-model="password"
                :type="hiddenPassword ? 'text' : 'password'"
                name="password"
                autocomplete="new-password"
                @blur="validateFieldOnBlur('password')"
                :class="[
                  'block w-full pl-10 pr-12 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                  errors.password
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 bg-white hover:border-gray-400',
                ]"
                placeholder="Nhập mật khẩu mới"
              />
              <button
                v-if="password"
                type="button"
                @click="togglePasswordVisibility"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <svg
                  v-if="hiddenPassword"
                  class="h-5 w-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <svg
                  v-else
                  class="h-5 w-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z"
                    clip-rule="evenodd"
                  ></path>
                  <path
                    d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"
                  ></path>
                </svg>
              </button>
            </div>
            <Transition name="slide-down">
              <span
                v-if="errors.password"
                class="text-sm text-red-600 flex items-center space-x-1"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>{{ errors.password }}</span>
              </span>
            </Transition>
          </div>

          <!-- Confirm Password Field -->
          <div class="space-y-2">
            <label
              for="confirmPassword"
              class="block text-sm font-semibold text-gray-700"
            >
              Xác nhận mật khẩu
            </label>
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <svg
                  class="h-5 w-5 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <input
                id="confirmPassword"
                v-model="confirmPassword"
                :type="hiddenConfirmPassword ? 'text' : 'password'"
                name="confirmPassword"
                autocomplete="new-password"
                @blur="validateFieldOnBlur('confirmPassword')"
                :class="[
                  'block w-full pl-10 pr-12 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                  errors.confirmPassword
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 bg-white hover:border-gray-400',
                ]"
                placeholder="Nhập lại mật khẩu"
              />
              <button
                v-if="confirmPassword"
                type="button"
                @click="toggleConfirmPasswordVisibility"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <svg
                  v-if="hiddenConfirmPassword"
                  class="h-5 w-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <svg
                  v-else
                  class="h-5 w-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z"
                    clip-rule="evenodd"
                  ></path>
                  <path
                    d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"
                  ></path>
                </svg>
              </button>
            </div>
            <Transition name="slide-down">
              <span
                v-if="errors.confirmPassword"
                class="text-sm text-red-600 flex items-center space-x-1"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>{{ errors.confirmPassword }}</span>
              </span>
            </Transition>
          </div>

          <!-- Submit Button -->
          <div class="space-y-4">
            <button
              type="submit"
              :disabled="loading"
              class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg
                  class="h-5 w-5 text-primary-light group-hover:text-white transition-colors duration-200"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </span>
              {{ loading ? "Đang xử lý..." : "Hoàn tất đăng ký" }}
            </button>

            <!-- Resend OTP Options -->
            <div class="space-y-3">
              <p class="text-center text-sm text-gray-600">Chưa nhận được mã?</p>

              <div class="grid grid-cols-2 gap-3">
                <!-- Resend via SMS -->
                <button
                  type="button"
                  @click="resendOtpViaSMS"
                  :disabled="loading"
                  class="flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                  </svg>
                  <span>Gửi qua SMS</span>
                </button>

                <!-- Send via Zalo -->
                <button
                  type="button"
                  @click="resendOtpViaZalo"
                  :disabled="loading"
                  class="flex items-center justify-center space-x-2 py-2 px-4 border border-primary rounded-lg text-sm font-medium text-primary bg-primary/5 hover:bg-primary/10 hover:border-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path>
                    <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"></path>
                  </svg>
                  <span>Gửi qua Zalo</span>
                </button>
              </div>
            </div>

            <!-- Back to Register -->
            <div class="text-center">
              <NuxtLink
                to="/register"
                class="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200 hover:underline"
              >
                ← Quay lại đăng ký
              </NuxtLink>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div v-if="loading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Xác nhận OTP",
});

const route = useRoute();

const {
  phoneNumber,
  storeName,
  customerName,
  userLoginId,
  otpMethod,
  otp,
  password,
  confirmPassword,
  hiddenPassword,
  hiddenConfirmPassword,
  errors,
  loading,
  registrationService,  // ✅ Add registrationService
  validateFieldOnBlur,
  handleOtpVerification,
  resendOtp,
  resendOtpViaSMS,
  resendOtpViaZalo,
  togglePasswordVisibility,
  toggleConfirmPasswordVisibility,
} = useRegister();

// OTP input handling
const otpDigits = ref(['', '', '', '', '', '']);
const otpInputRefs = ref<HTMLInputElement[]>([]);

const setOtpInputRef = (el: HTMLInputElement | null, index: number) => {
  if (el) {
    otpInputRefs.value[index] = el;
  }
};

const handleOtpInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value;

  // Only allow numbers
  if (!/^\d*$/.test(value)) {
    target.value = otpDigits.value[index];
    return;
  }

  otpDigits.value[index] = value;

  // Update the main otp value
  otp.value = otpDigits.value.join('');

  // Auto focus next input
  if (value && index < 5) {
    otpInputRefs.value[index + 1]?.focus();
  }

  // Validate OTP when all 6 digits are entered
  if (otp.value.length === 6) {
    validateFieldOnBlur('otp');
  }
};

const handleOtpKeydown = (index: number, event: KeyboardEvent) => {
  // Handle backspace
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    otpInputRefs.value[index - 1]?.focus();
  }

  // Handle arrow keys
  if (event.key === 'ArrowLeft' && index > 0) {
    otpInputRefs.value[index - 1]?.focus();
  }
  if (event.key === 'ArrowRight' && index < 5) {
    otpInputRefs.value[index + 1]?.focus();
  }
};

// Prevent non-numeric input from being typed
const handleOtpKeypress = (event: KeyboardEvent) => {
  // Allow special keys (backspace, delete, tab, escape, enter, etc.)
  if (event.key.length > 1) {
    return; // Allow special keys
  }

  // Only allow numeric characters
  if (!/[0-9]/.test(event.key)) {
    event.preventDefault();
  }
};

const handleOtpPaste = (event: ClipboardEvent) => {
  event.preventDefault();
  const pastedData = event.clipboardData?.getData('text') || '';
  const digits = pastedData.replace(/\D/g, '').slice(0, 6).split('');

  digits.forEach((digit, index) => {
    if (index < 6) {
      otpDigits.value[index] = digit;
    }
  });

  otp.value = otpDigits.value.join('');

  // Focus the next empty input or the last input
  const nextEmptyIndex = otpDigits.value.findIndex(digit => !digit);
  const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : 5;
  otpInputRefs.value[focusIndex]?.focus();
};

// Set data from query params
onMounted(() => {
  if (route.query.phone) {
    phoneNumber.value = route.query.phone as string;
  }

  // Khôi phục storeName và customerName từ query params
  if (route.query.storeName) {
    storeName.value = route.query.storeName as string;
    console.log('📝 Restored storeName from query:', storeName.value);
  }

  if (route.query.customerName) {
    customerName.value = route.query.customerName as string;
    console.log('📝 Restored customerName from query:', customerName.value);
  }

  // Khôi phục userLoginId từ query params
  if (route.query.userLoginId) {
    userLoginId.value = route.query.userLoginId as string;
    console.log('📝 Restored userLoginId from query:', userLoginId.value);
  }

  // Khôi phục registrationService từ query params
  if (route.query.serviceType || route.query.service) {
    registrationService.value = (route.query.serviceType as string) || (route.query.service as string);
    console.log('📝 Restored registrationService from query:', registrationService.value);
  }



  // Focus first OTP input
  nextTick(() => {
    otpInputRefs.value[0]?.focus();
  });
});

// Watch for otp changes to update individual digits
watch(otp, (newOtp) => {
  if (newOtp.length <= 6) {
    const digits = newOtp.split('');
    for (let i = 0; i < 6; i++) {
      otpDigits.value[i] = digits[i] || '';
    }
  }
});
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-xl {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

/* Enhanced focus styles */
.focus\:ring-primary:focus {
  --tw-ring-color: #3f51b5;
}

/* Smooth transitions for all interactive elements */
button,
input {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* OTP Input animations */
input[type="text"]:focus {
  transform: scale(1.05);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input[type="text"]:not(:placeholder-shown) {
  animation: pulse-success 0.3s ease-in-out;
}

@keyframes pulse-success {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    background-color: rgba(34, 197, 94, 0.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Responsive OTP inputs */
@media (max-width: 640px) {
  input[type="text"] {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
}
</style>
