/**
 * Onboarding System Types
 * 
 * Đ<PERSON><PERSON> nghĩa các types và interfaces cho hệ thống onboarding modular
 */

// Validation rule cho từng step
export interface ValidationRule {
  field: string;
  type: 'required' | 'email' | 'phone' | 'minLength' | 'maxLength' | 'pattern';
  value?: string | number;
  message: string;
}

// Định nghĩa một bước trong quá trình onboarding
export interface OnboardingStep {
  id: string;
  title: string;
  description?: string;
  component: string;
  module: string;
  required: boolean;
  completed: boolean;
  order: number;
  estimatedTime?: number; // phút
  validation?: ValidationRule[];
  data?: Record<string, any>; // Dữ liệu của step
  dependencies?: string[]; // Các step phụ thuộc
  skipCondition?: (data: any) => boolean; // Điều kiện để skip step
}

// Định nghĩa một module (POS, CRM, Warehouse, etc.)
export interface OnboardingModule {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  steps: OnboardingStep[];
  dependencies?: string[]; // Module phụ thuộc
  estimatedTime?: number; // Tổng thời gian ước tính
  category: 'core' | 'business' | 'optional';
  enabled: boolean;
}

// Cấu hình onboarding cho user
export interface OnboardingConfig {
  userId: string;
  orgId?: string;
  modules: string[]; // Danh sách module ID đã chọn
  steps: OnboardingStep[];
  currentStepId?: string;
  progress: {
    totalSteps: number;
    completedSteps: number;
    percentage: number;
    estimatedTimeRemaining: number; // phút
  };
  startedAt: Date;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

// Kết quả của một step
export interface StepResult {
  stepId: string;
  success: boolean;
  data?: Record<string, any>;
  errors?: string[];
  nextStepId?: string;
  skipToStepId?: string;
}

// Context cho onboarding
export interface OnboardingContext {
  config: OnboardingConfig;
  currentStep?: OnboardingStep;
  availableModules: OnboardingModule[];
  userData: Record<string, any>;
  organizationData: Record<string, any>;
}

// Events cho onboarding
export interface OnboardingEvent {
  type: 'step_started' | 'step_completed' | 'step_skipped' | 'module_completed' | 'onboarding_completed';
  stepId?: string;
  moduleId?: string;
  data?: Record<string, any>;
  timestamp: Date;
}

// Navigation options
export interface NavigationOptions {
  canGoBack: boolean;
  canGoNext: boolean;
  canSkip: boolean;
  showProgress: boolean;
  showEstimatedTime: boolean;
}

// Module categories
export type ModuleCategory = 'core' | 'business' | 'optional';

// Step status
export type StepStatus = 'not_started' | 'in_progress' | 'completed' | 'skipped' | 'error';

// Onboarding status
export type OnboardingStatus = 'not_started' | 'in_progress' | 'completed' | 'paused';

// Service types (từ hệ thống hiện tại)
export type ServiceType = 'POS' | 'CRM' | 'WAREHOUSE' | 'TELESALE' | 'SALEPOINT' | 'MARKETING';

// Predefined modules configuration
export interface ModuleDefinition {
  [key: string]: {
    id: string;
    name: string;
    description: string;
    icon: string;
    color: string;
    category: ModuleCategory;
    serviceTypes: ServiceType[];
    steps: Omit<OnboardingStep, 'completed' | 'data'>[];
  };
}

// Analytics data
export interface OnboardingAnalytics {
  userId: string;
  sessionId: string;
  events: OnboardingEvent[];
  completionRate: number;
  timeSpent: number; // seconds
  dropOffStep?: string;
  deviceInfo?: {
    userAgent: string;
    screenSize: string;
    isMobile: boolean;
  };
}

// API responses
export interface OnboardingApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SaveProgressRequest {
  userId: string;
  stepId: string;
  data: Record<string, any>;
  completed: boolean;
}

export interface GetConfigRequest {
  userId: string;
  serviceTypes?: ServiceType[];
  includeOptional?: boolean;
}

// Component props interfaces
export interface StepComponentProps {
  step: OnboardingStep;
  context: OnboardingContext;
  onComplete: (result: StepResult) => void;
  onSkip: () => void;
  onBack: () => void;
}

export interface ProgressBarProps {
  current: number;
  total: number;
  showPercentage?: boolean;
  showStepNumbers?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export interface StepperNavigationProps {
  steps: OnboardingStep[];
  currentStepId: string;
  onStepClick?: (stepId: string) => void;
  showLabels?: boolean;
  orientation?: 'horizontal' | 'vertical';
}

// Error types
export class OnboardingError extends Error {
  constructor(
    message: string,
    public code: string,
    public stepId?: string,
    public moduleId?: string
  ) {
    super(message);
    this.name = 'OnboardingError';
  }
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Export all types
export type {
  ValidationRule,
  OnboardingStep,
  OnboardingModule,
  OnboardingConfig,
  StepResult,
  OnboardingContext,
  OnboardingEvent,
  NavigationOptions,
  ModuleDefinition,
  OnboardingAnalytics,
  OnboardingApiResponse,
  SaveProgressRequest,
  GetConfigRequest,
  StepComponentProps,
  ProgressBarProps,
  StepperNavigationProps
};
