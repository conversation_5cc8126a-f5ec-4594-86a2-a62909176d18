<template>
  <div class="flex items-center justify-between">
    <!-- Label Section -->
    <div>
      <label class="text-sm font-medium text-gray-700">{{ label }}</label>
      <p v-if="description" class="text-xs text-gray-500">{{ description }}</p>
    </div>

    <!-- Toggle Switch -->
    <button
      type="button"
      @click="toggle"
      class="relative inline-flex flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2"
      :class="[
        sizeClasses,
        modelValue 
          ? `bg-${color} focus:ring-${color}` 
          : 'bg-gray-200 focus:ring-primary',
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      ]"
      role="switch"
      :aria-checked="modelValue"
      :disabled="disabled"
    >
      <span
        class="pointer-events-none inline-block transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
        :class="[
          switchClasses,
          modelValue ? translateClasses.on : translateClasses.off
        ]"
      ></span>
    </button>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  modelValue: boolean;
  label: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  color: 'primary',
  disabled: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'change': [value: boolean];
}>();

// Size configurations
const sizeConfigs = {
  sm: {
    container: 'h-5 w-9',
    switch: 'h-4 w-4',
    translateOn: 'translate-x-4',
    translateOff: 'translate-x-0'
  },
  md: {
    container: 'h-6 w-11',
    switch: 'h-5 w-5',
    translateOn: 'translate-x-5',
    translateOff: 'translate-x-0'
  },
  lg: {
    container: 'h-7 w-14',
    switch: 'h-6 w-6',
    translateOn: 'translate-x-7',
    translateOff: 'translate-x-0'
  }
};

// Computed classes
const sizeClasses = computed(() => sizeConfigs[props.size].container);
const switchClasses = computed(() => sizeConfigs[props.size].switch);
const translateClasses = computed(() => ({
  on: sizeConfigs[props.size].translateOn,
  off: sizeConfigs[props.size].translateOff
}));

// Methods
const toggle = () => {
  if (props.disabled) return;
  
  const newValue = !props.modelValue;
  emit('update:modelValue', newValue);
  emit('change', newValue);
};
</script>

<style scoped>
/* Dynamic color classes - ensure they're included in build */
.focus\:ring-primary:focus { @apply ring-blue-500; }
.bg-green { @apply bg-green-600; }
.focus\:ring-green:focus { @apply ring-green-300; }
.bg-red { @apply bg-red-600; }
.focus\:ring-red:focus { @apply ring-red-300; }
.bg-yellow { @apply bg-yellow-600; }
.focus\:ring-yellow:focus { @apply ring-yellow-300; }
.bg-purple { @apply bg-purple-600; }
.focus\:ring-purple:focus { @apply ring-purple-300; }
.bg-indigo { @apply bg-indigo-600; }
.focus\:ring-indigo:focus { @apply ring-indigo-300; }
</style>
