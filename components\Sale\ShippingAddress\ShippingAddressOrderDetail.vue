<template>
  <div
    @click="handleEdit"
    class="rounded-md border cursor-pointer py-2 bg-primary/10 flex justify-between items-center relative mb-1"
  >
    <div class="ml-3">
      <div class="flex gap-2">
        <div>
          <span class="text-sm">Tên: </span>
          <span class="text-sm"> {{ shippingAddress?.name }}</span>
        </div>
        <div>
          <span class="text-sm">SDT: </span>
          <span class="text-sm">{{ shippingAddress?.phone }}</span>
        </div>
      </div>
      <div>
        <div class="text-sm text-wrap">
          Địa chỉ:
          {{
            `${shippingAddress?.address || shippingAddress?.address1}, ${
              shippingAddress?.ward
            },  ${shippingAddress?.province}`
          }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const ModalEditShippingAddress = defineAsyncComponent(
  () => import("~/components/Modal/ModalEditShippingAddress.vue")
);
const props = defineProps(["shippingAddress", "customer", "isPageSale"]);
const emit = defineEmits(["toogleModal"]);

const handleEdit = () => {
  emit("toogleModal");
};
</script>
