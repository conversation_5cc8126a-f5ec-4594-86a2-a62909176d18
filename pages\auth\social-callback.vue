<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8">
        <!-- Loading State -->
        <div class="text-center">
          <div class="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white animate-spin" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-2">Đang xử lý đăng nhập</h2>
          <p class="text-gray-600 mb-4">
            Vui lòng đợi trong giây lát...
          </p>
          
          <!-- Progress Steps -->
          <div class="space-y-2 text-sm text-left">
            <div class="flex items-center space-x-2">
              <div :class="step >= 1 ? 'bg-green-500' : 'bg-gray-300'" class="w-2 h-2 rounded-full"></div>
              <span :class="step >= 1 ? 'text-green-600' : 'text-gray-500'">Xác thực thông tin</span>
            </div>
            <div class="flex items-center space-x-2">
              <div :class="step >= 2 ? 'bg-green-500' : 'bg-gray-300'" class="w-2 h-2 rounded-full"></div>
              <span :class="step >= 2 ? 'text-green-600' : 'text-gray-500'">Kiểm tra trạng thái tài khoản</span>
            </div>
            <div class="flex items-center space-x-2">
              <div :class="step >= 3 ? 'bg-green-500' : 'bg-gray-300'" class="w-2 h-2 rounded-full"></div>
              <span :class="step >= 3 ? 'text-green-600' : 'text-gray-500'">Thiết lập phiên đăng nhập</span>
            </div>
            <div class="flex items-center space-x-2">
              <div :class="step >= 4 ? 'bg-green-500' : 'bg-gray-300'" class="w-2 h-2 rounded-full"></div>
              <span :class="step >= 4 ? 'text-green-600' : 'text-gray-500'">Chuyển hướng</span>
            </div>
          </div>
          
          <!-- Error State -->
          <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-red-600 text-sm">{{ error }}</p>
            <button 
              @click="retryLogin"
              class="mt-2 text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Thử lại
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Đang xử lý đăng nhập",
});

const route = useRoute();
const router = useRouter();

// State
const step = ref(0);
const error = ref("");

// Extract params
const accessToken = route.query.accessToken as string;
const status = route.query.status as string;
const orgId = route.query.orgId as string;
const serviceType = (route.query.serviceType as string) || "POS";

// Process social login callback
const processSocialCallback = async () => {
  try {
    // Step 1: Validate params
    step.value = 1;
    console.log("🔍 Processing social login callback...");
    console.log("📋 Callback params:", { accessToken, status, orgId, serviceType });
    
    if (!accessToken) {
      throw new Error("Thiếu thông tin xác thực");
    }
    
    // Setup SDK context
    const $sdk = useNuxtApp().$sdk;
    if (!$sdk.orgId || $sdk.orgId === 'N/A') {
      console.log('🔧 Setting default orgId context...');
      $sdk.setOrgId('LONGVAN');
      useCookie("orgId").value = 'LONGVAN';
    }
    $sdk.setToken(accessToken);
    
    // Step 2: Check user status
    step.value = 2;
    console.log("🔍 Checking user status with getUserLoginByToken...");
    const userLoginInfo = await $sdk.auth.getUserLoginByToken(accessToken);
    console.log("👤 UserLogin info:", userLoginInfo);
    
    if (userLoginInfo && userLoginInfo.status === "VERIFIED") {
      console.log("✅ User status is VERIFIED - proceeding to onboarding");
      
      // Get complete userDetail
      const userDetail = await $sdk.auth.getUserDetail(accessToken);
      console.log("📦 UserDetail:", userDetail);
      
      if (userDetail) {
        // Step 3: Check if user already has organizations
        step.value = 3;
        console.log("🏢 Checking user organizations...");

        const hasOrganizations = userDetail.orgPositionsMap && Object.keys(userDetail.orgPositionsMap).length > 0;
        console.log("📋 User organizations:", {
          orgPositionsMap: userDetail.orgPositionsMap,
          hasOrganizations,
          orgCount: hasOrganizations ? Object.keys(userDetail.orgPositionsMap).length : 0
        });

        const authStore = useAuthStore();
        const userId = userDetail.partyId || userDetail.id || userDetail.userId;

        if (!userId) {
          throw new Error("Không thể lấy ID người dùng");
        }

        const user = {
          id: userId,
          name: userDetail.fullName,
          email: userDetail.email || "",
          phone: userDetail.phone || "",
          avatar: userDetail.avatarUrl || "",
          birthDate: userDetail.birthDate || "",
        };
        authStore.setUser(user);

        // Set cookies
        useCookie('token').value = accessToken;
        if (userDetail.orgPositionsMap) {
          useCookie("dataOrg").value = JSON.stringify(userDetail.orgPositionsMap);
        }

        console.log("✅ Auth state set successfully");

        // Step 4: Redirect based on organization status
        step.value = 4;

        if (hasOrganizations) {
          console.log("🏢 User already has organizations - redirecting to org-store");
          await new Promise(resolve => setTimeout(resolve, 1000));
          router.push("/org-store"); // Redirect to unified org-store page
        } else {
          console.log("🚀 User has no organizations - redirecting to onboarding");
          await new Promise(resolve => setTimeout(resolve, 1000));
          router.push(`/onboarding?userId=${userId}&serviceType=${serviceType}`);
        }
        return;
      }
    }
    
    // If not VERIFIED or missing info, redirect to update profile
    console.log("⚠️ User needs to complete profile - redirecting to update-info");
    router.push({
      path: "/register/update-info",
      query: {
        accessToken,
        type: "google",
        serviceType
      }
    });
    
  } catch (err: any) {
    console.error("❌ Error processing social callback:", err);
    error.value = err.message || "Có lỗi xảy ra khi xử lý đăng nhập";
  }
};

// Retry function
const retryLogin = () => {
  error.value = "";
  step.value = 0;
  processSocialCallback();
};

// Process on mount
onMounted(() => {
  processSocialCallback();
});
</script>
