import { ref, computed, nextTick } from 'vue';

export const useOtpInput = () => {
  // OTP state
  const otpDigits = ref(['', '', '', '', '', '']);
  const otpInputRefs = ref<HTMLInputElement[]>([]);

  // Computed OTP value
  const otp = computed(() => otpDigits.value.join(''));

  // Set input ref
  const setOtpInputRef = (el: HTMLInputElement | null, index: number) => {
    if (el) {
      otpInputRefs.value[index] = el;
    }
  };

  // Handle OTP input
  const handleOtpInput = (index: number, event: Event) => {
    const target = event.target as HTMLInputElement;
    const value = target.value;

    // Only allow numbers
    if (!/^\d*$/.test(value)) {
      target.value = otpDigits.value[index];
      return;
    }

    // Update digit
    otpDigits.value[index] = value;

    // Auto focus to next input
    if (value && index < 5) {
      nextTick(() => {
        otpInputRefs.value[index + 1]?.focus();
      });
    }
  };

  // Handle keydown events
  const handleOtpKeydown = (index: number, event: KeyboardEvent) => {
    // Handle backspace
    if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
      nextTick(() => {
        otpInputRefs.value[index - 1]?.focus();
      });
    }
  };

  // Handle keypress (prevent non-numeric)
  const handleOtpKeypress = (event: KeyboardEvent) => {
    if (!/\d/.test(event.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(event.key)) {
      event.preventDefault();
    }
  };

  // Handle paste
  const handleOtpPaste = (event: ClipboardEvent) => {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    const digits = pastedData.replace(/\D/g, '').slice(0, 6).split('');
    
    // Fill digits
    digits.forEach((digit, index) => {
      if (index < 6) {
        otpDigits.value[index] = digit;
      }
    });

    // Focus on next empty input or last input
    const nextEmptyIndex = otpDigits.value.findIndex(digit => !digit);
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : 5;
    nextTick(() => {
      otpInputRefs.value[focusIndex]?.focus();
    });
  };

  // Validate OTP format
  const validateOtpFormat = (otpValue: string): boolean => {
    return otpValue.length === 6 && /^\d{6}$/.test(otpValue);
  };

  // Clear OTP
  const clearOtp = () => {
    otpDigits.value = ['', '', '', '', '', ''];
    nextTick(() => {
      otpInputRefs.value[0]?.focus();
    });
  };

  // Focus first input
  const focusFirstInput = () => {
    nextTick(() => {
      otpInputRefs.value[0]?.focus();
    });
  };

  return {
    otpDigits,
    otpInputRefs,
    otp,
    setOtpInputRef,
    handleOtpInput,
    handleOtpKeydown,
    handleOtpKeypress,
    handleOtpPaste,
    validateOtpFormat,
    clearOtp,
    focusFirstInput
  };
};
