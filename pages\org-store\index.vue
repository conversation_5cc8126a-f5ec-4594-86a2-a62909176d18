<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Enhanced Header -->
    <HeaderOrgStore
      :show-search="false"
      @search="handleSearch"
    />

    <div class="flex h-[calc(100vh-60px)]">
      <!-- Mobile Sidebar Overlay -->
      <div
        v-if="isMobileSidebarOpen"
        class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
        @click="isMobileSidebarOpen = false"
      ></div>

      <!-- Optimized Sidebar - Organizations -->
      <div
        class="w-72 bg-white shadow-sm border-r border-gray-200 flex flex-col transition-transform duration-300 ease-in-out md:relative md:translate-x-0"
        :class="{
          'fixed inset-y-0 left-0 z-50 transform translate-x-0': isMobileSidebarOpen,
          'fixed inset-y-0 left-0 z-50 transform -translate-x-full': !isMobileSidebarOpen
        }"
      >
        <!-- Sidebar Header -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-4">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-lg font-semibold text-white">Tổ chức</h2>
              <p class="text-blue-100 text-sm mt-1">{{ Object.keys(sortedGroupedStores).length }} tổ chức</p>
            </div>
            <div class="flex space-x-2">
              <!-- Mobile Close Button -->
              <button
                v-if="isMobileSidebarOpen"
                @click="isMobileSidebarOpen = false"
                class="p-1.5 text-white/80 hover:text-white hover:bg-white/20 rounded-md transition-colors md:hidden"
                title="Đóng"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <button
                @click="loadAllStores"
                class="p-1.5 text-white/80 hover:text-white hover:bg-white/20 rounded-md transition-colors"
                title="Tải lại"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Organizations List -->
        <div class="flex-1 overflow-y-auto p-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
          <!-- Loading State -->
          <div v-if="isLoadingStores" class="flex items-center justify-center py-12">
            <div class="text-center">
              <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p class="text-sm text-gray-500">Đang tải tổ chức...</p>
            </div>
          </div>

          <!-- Organizations List -->
          <div v-else-if="Object.keys(sortedGroupedStores).length > 0" class="space-y-2">
            <div
              v-for="(stores, orgName) in sortedGroupedStores"
              :key="`org-${orgName}`"
              class="group"
            >
              <button
                @click="handleSelectOrg(String(orgName))"
                class="w-full text-left p-3 rounded-xl border transition-all duration-200 hover:shadow-md group-hover:scale-[1.02]"
                :class="{
                  'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-sm ring-1 ring-blue-200': selectedOrg === orgName,
                  'bg-white border-gray-200 hover:border-blue-300 hover:bg-gray-50': selectedOrg !== orgName
                }"
              >
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-3">
                      <!-- Organization Icon -->
                      <div
                        class="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0"
                        :class="{
                          'bg-blue-100 text-blue-600': selectedOrg === orgName,
                          'bg-gray-100 text-gray-600': selectedOrg !== orgName
                        }"
                      >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>

                      <!-- Organization Info -->
                      <div class="flex-1 min-w-0">
                        <h3 class="font-semibold text-gray-900 truncate">{{ orgName }}</h3>
                        <p class="text-sm text-gray-500 mt-0.5">
                          {{ stores.length }} cửa hàng
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- Selection Indicator -->
                  <div class="flex items-center space-x-2">
                    <div v-if="selectedOrg === orgName" class="text-blue-600">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <svg class="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </button>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Không có tổ chức</h3>
            <p class="text-gray-500 mb-4">Bạn chưa có quyền truy cập tổ chức nào</p>
            <button
              v-if="!auth?.user?.id"
              @click="$router.push('/login')"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Đăng nhập
            </button>
            <button
              v-else
              @click="loadAllStores"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Tải lại
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content - Stores -->
      <div class="flex-1 flex flex-col bg-white">
        <!-- Compact Header -->
        <div class="border-b border-gray-200 px-4 md:px-6 py-4">
          <div class="flex items-center justify-between">
            <!-- Mobile Menu Button -->
            <button
              @click="isMobileSidebarOpen = true"
              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg md:hidden"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <div class="flex-1 md:ml-0 ml-4">
              <h1 class="text-lg md:text-xl font-bold text-gray-900">
                {{ selectedOrg ? `Cửa hàng - ${selectedOrg}` : 'Chọn tổ chức để bắt đầu' }}
              </h1>
              <p class="text-xs md:text-sm text-gray-500 mt-1">
                {{ selectedOrg ? 'Chọn cửa hàng để tiếp tục làm việc' : 'Vui lòng chọn tổ chức từ danh sách bên trái' }}
              </p>
            </div>

            <!-- Search, Count and Create Button -->
            <div v-if="selectedOrg && currentStores?.length > 0" class="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-4">
              <!-- Search Input -->
              <div class="relative w-full md:w-80">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Tìm kiếm theo tên cửa hàng hoặc loại..."
                  class="block w-full pl-9 pr-8 py-2 text-sm border border-gray-300 rounded-lg bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  @keydown.escape="searchQuery = ''"
                  @input="console.log('🔍 Input event:', ($event.target as HTMLInputElement)?.value)"
                />
                <!-- Clear Search -->
                <button
                  v-if="searchQuery"
                  @click="searchQuery = ''"
                  class="absolute inset-y-0 right-0 pr-2 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <!-- Create Store Button -->
              <button
                @click="handleCreateStore"
                class="inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 whitespace-nowrap"
              >
                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Tạo cửa hàng
              </button>

              <!-- Store Count -->
              <div class="text-right md:ml-auto">
                <p class="text-xs text-gray-500">Tổng số</p>
                <p class="text-lg font-bold text-blue-600">{{ filteredCurrentStores?.length || 0 }}/{{ currentStores?.length || 0 }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Store Type Tabs -->
        <div v-if="selectedOrg && currentStores?.length > 0" class="border-b border-gray-200 px-4 md:px-6">
          <div class="flex space-x-1 overflow-x-auto scrollbar-hide">
            <button
              @click="filterStoreType = 'all'"
              class="flex-shrink-0 px-4 py-2 text-sm font-medium rounded-t-lg transition-colors whitespace-nowrap"
              :class="{
                'bg-blue-50 text-blue-700 border-b-2 border-blue-500': filterStoreType === 'all',
                'text-gray-500 hover:text-gray-700 hover:bg-gray-50': filterStoreType !== 'all'
              }"
            >
              Tất cả
              <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-600">
                {{ currentStores?.length || 0 }}
              </span>
            </button>
            <button
              v-for="storeType in availableStoreTypes"
              :key="storeType"
              @click="filterStoreType = storeType"
              class="flex-shrink-0 px-4 py-2 text-sm font-medium rounded-t-lg transition-colors whitespace-nowrap"
              :class="{
                'bg-blue-50 text-blue-700 border-b-2 border-blue-500': filterStoreType === storeType,
                'text-gray-500 hover:text-gray-700 hover:bg-gray-50': filterStoreType !== storeType
              }"
            >
              {{ getStoreTypeLabel(storeType) }}
              <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-600">
                {{ currentStores?.filter((s: any) => (s.type || 'POS') === storeType).length || 0 }}
              </span>
            </button>
          </div>
        </div>



        <!-- Stores Content -->
        <div class="flex-1 overflow-y-auto p-4 md:p-6">
          <!-- Loading State -->
          <div v-if="isLoadingStores" class="flex items-center justify-center py-16">
            <div class="text-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p class="text-gray-600">Đang tải danh sách cửa hàng...</p>
            </div>
          </div>

          <!-- Auto-selecting State -->
          <div v-else-if="isAutoSelecting" class="flex items-center justify-center py-16">
            <div class="text-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p class="text-gray-600">Đang tự động chọn cửa hàng...</p>
              <p class="text-sm text-gray-500 mt-2">Chỉ có 1 cửa hàng, đang chuyển hướng...</p>
            </div>
          </div>

          <!-- No Organization Selected -->
          <div v-if="!selectedOrg" class="flex items-center justify-center py-16">
            <div class="text-center">
              <div class="text-gray-400 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Chọn tổ chức</h3>
              <p class="text-gray-600">Vui lòng chọn một tổ chức từ sidebar để xem danh sách cửa hàng</p>
            </div>
          </div>

          <!-- Stores Grid -->
          <div v-else-if="filteredCurrentStores?.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6">
            <div
              v-for="store in filteredCurrentStores"
              :key="`store-${store.id}`"
              class="group"
            >
              <div
                @click="handleSelectStore(store)"
                class="relative bg-white rounded-2xl p-4 md:p-6 border border-gray-200 shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-[1.02] hover:-translate-y-1"
                :class="{
                  'ring-2 ring-blue-500 ring-opacity-50 shadow-xl bg-gradient-to-br from-blue-50 to-indigo-50': selectedStore === store.id,
                }"
              >
                <!-- Store Header -->
                <div class="flex items-center justify-between mb-3 md:mb-4">
                  <div class="relative">
                    <div
                      class="w-10 h-10 md:w-12 md:h-12 rounded-xl flex items-center justify-center shadow-lg"
                      :class="{
                        'bg-gradient-to-br from-blue-500 to-indigo-600': selectedStore !== store.id,
                        'bg-gradient-to-br from-blue-600 to-indigo-700': selectedStore === store.id
                      }"
                    >
                      <!-- Dynamic Store Icon based on type -->
                      <svg v-if="store.type === 'website'" class="w-5 h-5 md:w-6 md:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9a9 9 0 01-9-9m9 9c0 5-4 9-9 9s-9-4-9-9m9 9c0-5 4-9 9-9s9 4 9 9" />
                      </svg>
                      <svg v-else class="w-5 h-5 md:w-6 md:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <!-- Store Status Indicator -->
                    <div
                      class="absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
                      :class="{
                        'bg-green-500': store.enable !== false,
                        'bg-red-500': store.enable === false
                      }"
                    ></div>
                  </div>

                  <!-- Selection Indicator -->
                  <div v-if="selectedStore === store.id" class="text-blue-600">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>

                <!-- Store Info -->
                <div class="space-y-2">
                  <h3 class="font-semibold text-gray-900 text-base md:text-lg truncate">{{ store.name }}</h3>
                  <p class="text-xs md:text-sm text-gray-600 line-clamp-2">{{ getStoreDescription(store) }}</p>

                  <!-- Store Stats -->
                  <div class="flex items-center justify-between pt-2">
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {{ store.type || 'POS' }}
                    </span>
                    <span class="text-xs text-gray-500">
                      {{ store.warehouses?.length || 0 }} kho
                    </span>
                  </div>
                </div>

                <!-- Loading Overlay -->
                <div v-if="isLoading && selectedStore === store.id" class="absolute inset-0 bg-white bg-opacity-75 rounded-2xl flex items-center justify-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              </div>
            </div>


          </div>

          <!-- No Stores Found -->
          <div v-else-if="selectedOrg && currentStores?.length > 0 && filteredCurrentStores?.length === 0" class="flex items-center justify-center py-16">
            <div class="text-center">
              <div class="text-gray-400 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Không tìm thấy cửa hàng</h3>
              <p class="text-gray-600">Không có cửa hàng nào phù hợp với từ khóa "{{ searchQuery }}"</p>
            </div>
          </div>

          <!-- No Auth -->
          <div v-else-if="!auth?.user?.id" class="flex items-center justify-center py-16">
            <div class="text-center">
              <div class="text-gray-400 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa đăng nhập</h3>
              <p class="text-gray-600 mb-4">Vui lòng đăng nhập để xem danh sách cửa hàng</p>
              <button
                @click="$router.push('/login')"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Đăng nhập
              </button>
            </div>
          </div>

          <!-- No Stores -->
          <div v-else class="flex items-center justify-center py-16">
            <div class="text-center">
              <div class="text-gray-400 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-2.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Không có cửa hàng</h3>
              <p class="text-gray-600 mb-4">Bạn chưa có quyền truy cập vào cửa hàng nào</p>

              <!-- Create Store Button for empty state -->
              <button
                v-if="selectedOrg"
                @click="handleCreateStore"
                class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Tạo cửa hàng mới
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Store Modal -->
  <div
    v-if="showCreateStoreModal"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    @click.self="handleModalClose"
  >
    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900">Tạo Cửa Hàng Mới</h3>
        <button
          @click="handleModalClose"
          class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="p-6">
        <CreateStore
          :step="createStoreStep"
          :context="createStoreContext"
          @complete="handleStoreCreated"
          @skip="handleStoreSkip"
          @back="handleStoreBack"
          @update:canProceed="canProceedCreateStore = $event"
          ref="createStoreComponent"
        />
      </div>

      <!-- Modal Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
        <button
          @click="handleModalClose"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          Hủy
        </button>
        <button
          @click="handleCreateStoreSubmit"
          :disabled="!canProceedCreateStore || isCreatingStore"
          class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <span v-if="isCreatingStore" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Đang tạo...
          </span>
          <span v-else>Tạo cửa hàng</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import CreateStore from '~/components/onboarding/modules/pos/CreateStore.vue';
import type { OnboardingStep, OnboardingContext } from '~/types/onboarding';

// Composables and stores
const orderStore = useOrderStore();
const authStore = useAuthStore();
const { setStore, setOrgId } = usePermission();
const { getAvailableStoreChannels } = useStore();
const auth = useCookie("auth") as any;

// Use tab-isolated context

// Reactive state
const storeList = ref<any>([]);
const selectedStore = ref(null);
const selectedOrg = ref<string | number | null>(null);
const isLoadingStores = ref(false);
const isLoading = ref(false);
const isAutoSelecting = ref(false);
const searchQuery = ref('');
const filterStoreType = ref('all'); // Default to all tab - will be set to priority type when org is selected
const isMobileSidebarOpen = ref(false);

// Utility functions
const getStoreDescription = (store: any) => {
  const warehouseCount = store.warehouses?.length || 0;
  return `Cửa hàng ${store.type || "POS"} với ${warehouseCount} kho hàng`;
};

const getStoreTypeLabel = (storeType: string) => {
  const labels: { [key: string]: string } = {
    'POS': 'POS',
    'pos': 'POS',
    'website': 'Website',
    'Website': 'Website',
    // Tạm thời comment các loại khác
    // 'shopee': 'Shopee',
    // 'Shopee': 'Shopee',
    // 'lazada': 'Lazada',
    // 'Lazada': 'Lazada',
    // 'tiki': 'Tiki',
    // 'Tiki': 'Tiki',
    // 'nhanh': 'Nhanh.vn',
    // 'Nhanh': 'Nhanh.vn',
    // 'tiktok': 'TikTok',
    // 'TikTok': 'TikTok',
    // 'Tiktok': 'TikTok',
    // 'pancake': 'Pancake',
    // 'Pancake': 'Pancake',
    'TELESALE': 'Telesale',
    'SALEPONT': 'Salepont',
    'ECOMMERCE': 'E-commerce'
  };
  return labels[storeType] || storeType;
};

// Handle search from header
const handleSearch = (query: string) => {
  console.log("🔍 Search query from header:", query);
  searchQuery.value = query;
};

// Watch search query changes for debugging
watch(searchQuery, (newQuery, oldQuery) => {
  console.log("🔍 Search query changed:", oldQuery, "→", newQuery);
}, { immediate: true });



// Group stores by organization and collect org info
const groupedStores = computed(() => {
  if (!storeList.value || !Array.isArray(storeList.value)) return {};

  const groups: { [key: string]: any[] } = {};

  storeList.value.forEach((store: any) => {
    // Try different possible field names for organization
    const orgName = store.orgName ||
                   store.party_id ||
                   store.orgId ||
                   store.organizationId ||
                   store.organization?.name ||
                   store.org?.name ||
                   'Không xác định';

    if (!groups[orgName]) {
      groups[orgName] = [];
    }
    groups[orgName].push(store);
  });

  return groups;
});

// Sorted grouped stores by organization name
const sortedGroupedStores = computed(() => {
  const groups = groupedStores.value;
  const sortedKeys = Object.keys(groups).sort((a, b) => {
    // Put "Không xác định" at the end
    if (a === 'Không xác định') return 1;
    if (b === 'Không xác định') return -1;
    return a.localeCompare(b, 'vi', { sensitivity: 'base' });
  });

  const sortedGroups: { [key: string]: any[] } = {};
  sortedKeys.forEach(key => {
    sortedGroups[key] = groups[key];
  });

  return sortedGroups;
});

// Get available store types from current stores with priority order
const availableStoreTypes = computed(() => {
  if (!currentStores.value) return [];

  const types = new Set<string>();
  currentStores.value.forEach((store: any) => {
    const type = store.type || 'POS';
    types.add(type);
  });

  // Define priority order: POS and Website only
  const priorityOrder = [
    'POS', 'pos',
    'website', 'Website',
    // Tạm thời comment các loại khác
    // 'shopee', 'Shopee',
    // 'lazada', 'Lazada',
    // 'tiki', 'Tiki',
    // 'nhanh', 'Nhanh',
    // 'tiktok', 'TikTok', 'Tiktok',
    // 'pancake', 'Pancake'
  ];

  const availableTypes = Array.from(types);

  // Sort by priority order
  return availableTypes.sort((a, b) => {
    const aIndex = priorityOrder.findIndex(p => p.toLowerCase() === a.toLowerCase());
    const bIndex = priorityOrder.findIndex(p => p.toLowerCase() === b.toLowerCase());

    // If both are in priority list, sort by priority
    if (aIndex !== -1 && bIndex !== -1) {
      return aIndex - bIndex;
    }

    // If only one is in priority list, prioritize it
    if (aIndex !== -1) return -1;
    if (bIndex !== -1) return 1;

    // If neither is in priority list, sort alphabetically
    return a.localeCompare(b, 'vi', { sensitivity: 'base' });
  });
});

// Get org info including roles
const orgInfo = computed(() => {
  const dataOrgCookie = useCookie("dataOrg");
  let dataOrg = dataOrgCookie.value as any;

  console.log("🔍 Raw dataOrg cookie:", dataOrg);
  console.log("🔍 dataOrg type:", typeof dataOrg);

  // If dataOrg is a string, try to parse it
  if (typeof dataOrg === 'string') {
    try {
      dataOrg = JSON.parse(dataOrg);
      console.log("🔍 Parsed dataOrg:", dataOrg);
    } catch (error) {
      console.error("❌ Error parsing dataOrg:", error);
      return {};
    }
  }

  // Handle both array format (old) and object format (new)
  let orgMap: { [key: string]: any } = {};

  if (Array.isArray(dataOrg)) {
    // Old format: array of org objects
    dataOrg.forEach((org: any) => {
      orgMap[org.name] = org;
    });
  } else if (dataOrg && typeof dataOrg === 'object') {
    // New format: orgPositionsMap object
    Object.keys(dataOrg).forEach(orgName => {
      orgMap[orgName] = {
        name: orgName,
        role: dataOrg[orgName]
      };
    });
  }

  console.log("🔍 Final orgMap:", orgMap);
  return orgMap;
});

// Get current stores for selected organization
const currentStores = computed(() => {
  if (!selectedOrg.value || !sortedGroupedStores.value[selectedOrg.value]) return [];
  const stores = sortedGroupedStores.value[selectedOrg.value];
  console.log("🏪 Current stores for org", selectedOrg.value, ":", stores.map(s => ({ name: s.name, type: s.type })));
  return stores;
});

// Search and filter functionality for current stores
const filteredCurrentStores = computed(() => {
  if (!currentStores.value) return [];

  let filtered = [...currentStores.value]; // Create a copy to avoid mutation

  console.log("🔍 Starting filter with stores:", filtered.length);
  console.log("🔍 Filter type:", filterStoreType.value);
  console.log("🔍 Search query:", searchQuery.value);

  // Filter by store type (skip if 'all' is selected)
  if (filterStoreType.value && filterStoreType.value !== 'all') {
    const beforeCount = filtered.length;
    filtered = filtered.filter((store: any) => {
      const storeType = store.type || 'POS';
      const matches = storeType.toLowerCase() === filterStoreType.value.toLowerCase();
      return matches;
    });
    console.log("🔍 After type filter:", filtered.length, "from", beforeCount);
  }

  // Filter by search query
  if (searchQuery.value && searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    const beforeCount = filtered.length;

    filtered = filtered.filter((store: any) => {
      const name = store.name?.toLowerCase() || '';
      const type = (store.type || 'POS').toLowerCase();

      const nameMatch = name.includes(query);
      const typeMatch = type.includes(query);

      const matches = nameMatch || typeMatch;

      if (matches) {
        console.log("🔍 Match found:", store.name, "query:", query);
      }

      return matches;
    });

    console.log("🔍 After search filter:", filtered.length, "from", beforeCount);
  }

  console.log("🔍 Final filtered stores:", filtered.length);
  return filtered;
});



// Handle organization selection (UI only - no context setting)
const handleSelectOrg = async (orgName: string) => {
  selectedOrg.value = orgName;
  console.log("🏢 Organization selected (UI only):", orgName);

  // Reset to POS tab when selecting new org
  await nextTick();
  const orgStores = sortedGroupedStores.value[orgName];

  // Set active tab based on priority order
  if (orgStores && orgStores.length > 0) {
    // Get available store types for this org
    const orgStoreTypes = new Set<string>();
    orgStores.forEach((store: any) => {
      const type = store.type || 'POS';
      orgStoreTypes.add(type);
    });

    // Priority order: POS → Website only
    const priorityOrder = [
      'POS', 'pos',
      'website', 'Website',
      // Tạm thời comment các loại khác
      // 'shopee', 'Shopee',
      // 'lazada', 'Lazada',
      // 'tiki', 'Tiki',
      // 'nhanh', 'Nhanh',
      // 'tiktok', 'TikTok', 'Tiktok',
      // 'pancake', 'Pancake'
    ];

    // Find the highest priority store type available
    let selectedType = 'POS'; // fallback
    for (const priorityType of priorityOrder) {
      if (Array.from(orgStoreTypes).some(type => type.toLowerCase() === priorityType.toLowerCase())) {
        selectedType = Array.from(orgStoreTypes).find(type => type.toLowerCase() === priorityType.toLowerCase()) || 'POS';
        break;
      }
    }

    filterStoreType.value = selectedType;
    console.log("🏪 Set active tab based on priority:", selectedType, "for org:", orgName);
    console.log("🏪 Available types:", Array.from(orgStoreTypes));
  }

  // Show info about single store but don't auto-select
  if (orgStores && orgStores.length === 1) {
    console.log("ℹ️ Single store in org:", orgStores[0].name);
    useNuxtApp().$toast.info(`Cửa hàng có sẵn: ${orgStores[0].name}. Click để chọn hoặc tạo cửa hàng mới.`);
    // Don't auto-select - let user choose
  }

  // Note: Actual org context will be set when user selects a store
};

// Create store modal state
const showCreateStoreModal = ref(false);
const canProceedCreateStore = ref(false);
const isCreatingStore = ref(false);
const createStoreComponent = ref(null);

// Create store step configuration
const createStoreStep = computed((): OnboardingStep => ({
  id: 'create-store',
  title: 'Tạo Cửa Hàng',
  description: 'Tạo cửa hàng mới cho tổ chức',
  component: 'CreateStore',
  module: 'pos',
  required: true,
  completed: false,
  order: 1,
  estimatedTime: 5,
  data: undefined
}));

// Create store context
const createStoreContext = computed((): OnboardingContext => ({
  config: {
    userId: auth.value?.user?.id || '',
    orgId: selectedOrg.value as string,
    modules: ['pos'],
    steps: [createStoreStep.value],
    progress: {
      totalSteps: 1,
      completedSteps: 0,
      percentage: 0,
      estimatedTimeRemaining: 5
    },
    startedAt: new Date(),
    metadata: {
      serviceTypes: ['POS']
    }
  },
  currentStep: createStoreStep.value,
  availableModules: [],
  userData: auth.value?.user || {},
  organizationData: {
    orgName: selectedOrg.value,
    orgId: selectedOrg.value
  }
}));

// Handle create new store
const handleCreateStore = () => {
  if (!selectedOrg.value) {
    useNuxtApp().$toast.error("Vui lòng chọn tổ chức trước");
    return;
  }

  showCreateStoreModal.value = true;
};

// Handle store creation success
const handleStoreCreated = async (result: any) => {
  console.log('✅ Store created successfully:', result);

  // Reset loading state
  isCreatingStore.value = false;

  // Close modal
  showCreateStoreModal.value = false;

  // Show success message
  useNuxtApp().$toast.success('Cửa hàng đã được tạo thành công!');

  // Reload stores to show new store
  await loadAllStores();

  // Switch to appropriate tab based on created store type
  const createdStoreType = result.data?.formData?.storeType || result.data?.storeType;
  console.log('🏪 Created store type:', createdStoreType);

  if (createdStoreType) {
    // Map store type to filter value
    const typeMapping: { [key: string]: string } = {
      'pos': 'pos',
      'POS': 'pos',
      'website': 'website',
      'Website': 'website',
      'shopee': 'shopee',
      'lazada': 'lazada',
      'tiki': 'tiki',
      'tiktok': 'tiktok',
      'nhanh': 'nhanh',
      'pancake': 'pancake'
    };

    const targetTab = typeMapping[createdStoreType];
    if (targetTab) {
      console.log('🎯 Switching to tab:', targetTab);
      filterStoreType.value = targetTab;
    }
  }

  // Just show the new store in the list - no auto-redirect
  // Auto-select and redirect removed per user request
};

// Handle modal close
const handleModalClose = () => {
  showCreateStoreModal.value = false;
};

// Handle skip (not used but required by component)
const handleStoreSkip = () => {
  showCreateStoreModal.value = false;
};

// Handle back (not used but required by component)
const handleStoreBack = () => {
  showCreateStoreModal.value = false;
};

// Handle create store submit from modal button
const handleCreateStoreSubmit = async () => {
  if (!canProceedCreateStore.value || isCreatingStore.value) {
    return;
  }

  isCreatingStore.value = true;

  try {
    // Find and trigger the form submit in CreateStore component
    await nextTick();
    const form = document.querySelector('.create-store-step form');
    if (form) {
      // Trigger form submit event
      const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
      form.dispatchEvent(submitEvent);
    } else {
      console.error('CreateStore form not found');
      useNuxtApp().$toast.error('Có lỗi xảy ra khi tạo cửa hàng');
      isCreatingStore.value = false;
    }
  } catch (error: any) {
    console.error('Error submitting create store:', error);
    useNuxtApp().$toast.error(error?.message || 'Có lỗi xảy ra khi tạo cửa hàng');
    isCreatingStore.value = false;
  }
};

// Load all stores for user
const loadAllStores = async () => {
  isLoadingStores.value = true;
  try {
    const userId = auth.value?.user?.id || authStore.user?.id;
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Set token before API call
    const $sdk = useNuxtApp().$sdk;
    const token = useCookie("token").value;
    if (token) {
      $sdk.setToken(token);
      console.log("🔑 Token set for API call");
    } else {
      throw new Error('No token available');
    }

    console.log("📞 Calling getAvailableStoreChannels with userId:", userId);

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), 10000); // 10 second timeout
    });

    // Race between API call and timeout
    const response = await Promise.race([
      getAvailableStoreChannels(userId),
      timeoutPromise
    ]);
    console.log("🏪 API Response:", response);

    // Check if response has the expected structure
    if (response && response.status === 1 && response.data) {
      const availableStores = response.data;
      console.log("📋 Available stores data:", availableStores);

      if (Array.isArray(availableStores) && availableStores.length > 0) {
        // Debug: Log first store structure
        console.log("🔍 First store structure:", availableStores[0]);
        console.log("🔍 Store keys:", Object.keys(availableStores[0] || {}));

        // Filter enabled stores
        const validStores = availableStores.filter((store: any) => store && store.enable !== false);

        console.log("✅ Valid stores:", validStores);
        storeList.value = validStores;

        // Auto-select logic
        await nextTick();
        const orgKeys = Object.keys(sortedGroupedStores.value);

        // If only 1 organization
        if (orgKeys.length === 1) {
          const firstOrg = orgKeys[0];
          const orgStores = sortedGroupedStores.value[firstOrg];

          // Auto-select the organization (this will also set POS tab)
          if (!selectedOrg.value) {
            await handleSelectOrg(firstOrg);
          }

          // Show notification about single org but don't auto-redirect store
          if (orgStores.length === 1) {
            console.log("ℹ️ Single store available:", orgStores[0].name);
            useNuxtApp().$toast.info(`Cửa hàng có sẵn: ${orgStores[0].name}. Bạn có thể chọn hoặc tạo cửa hàng mới.`);
            // Don't auto-redirect - let user choose
          }
        } else if (orgKeys.length > 1) {
          // Multiple orgs, just select the first one (this will also set POS tab)
          const firstOrg = orgKeys[0];
          if (!selectedOrg.value) {
            await handleSelectOrg(firstOrg);
          }
        }

        if (validStores.length === 0) {
          useNuxtApp().$toast.warning("Tất cả cửa hàng đều bị vô hiệu hóa");
        }
      } else {
        storeList.value = [];
        useNuxtApp().$toast.warning("Dữ liệu cửa hàng không hợp lệ");
      }
    } else {
      storeList.value = [];
      useNuxtApp().$toast.warning("Bạn chưa có quyền truy cập vào cửa hàng nào");
    }

  } catch (error: any) {
    console.error("Error loading stores:", error);
    storeList.value = [];

    // Show specific error messages
    if (error.message === 'Request timeout') {
      useNuxtApp().$toast.error("Yêu cầu quá thời gian. Vui lòng thử lại.");
    } else if (error.message === 'User not authenticated') {
      useNuxtApp().$toast.error("Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.");
      await navigateTo('/login');
    } else if (error.message === 'No token available') {
      useNuxtApp().$toast.error("Không tìm thấy token xác thực. Vui lòng đăng nhập lại.");
      await navigateTo('/login');
    } else {
      useNuxtApp().$toast.error("Có lỗi xảy ra khi tải danh sách cửa hàng. Vui lòng thử lại.");
    }
  } finally {
    isLoadingStores.value = false;
  }
};

// Store selection
const handleSelectStore = async (store: any) => {
  if (isLoading.value) return;

  // Ensure org is selected first
  if (!selectedOrg.value) {
    useNuxtApp().$toast.error("Vui lòng chọn tổ chức trước");
    return;
  }

  selectedStore.value = store.id;
  isLoading.value = true;

  try {
    console.log("� Setting BOTH ORG and STORE when store is selected");
    console.log("🏢 Selected org:", selectedOrg.value);
    console.log("🏪 Selected store:", store.name, "ID:", store.id);

    // STEP 1: Set ORG context and user roles first
    const orgId = selectedOrg.value || store.orgId || store.party_id || 'N/A';

    if (orgId !== 'N/A') {
      // Set org in both contexts
      const { setOrgId: setTabOrgId } = useTabContext();
      if (setTabOrgId) {
        setTabOrgId(orgId);
      }
      await setOrgId(orgId);

      // Set user roles from org data
      const orgData = orgInfo.value[selectedOrg.value];
      if (orgData && orgData.role) {
        const user = {
          id: auth.value?.user?.id,
          name: auth.value?.user?.name,
          email: auth.value?.user?.email,
          phone: auth.value?.user?.phone,
          avatar: auth.value?.user?.avatar,
          birthDate: auth.value?.user?.birthDate,
          roles: orgData.role, // Set roles from org data
        };
        authStore.setUser(user);

        console.log("✅ ORG SET: Organization context and user roles set:", {
          orgId,
          roles: orgData.role
        });
      } else {
        console.log("⚠️ No role data found for org:", selectedOrg.value);
      }
    }

    // STEP 2: Set STORE context
    // Clear previous order data
    orderStore.listOrder = [];
    if ("listOrderTerm" in orderStore) {
      orderStore.listOrderTerm = [];
    }

    // Set store data
    useCookie("warehouse").value = store.warehouses;
    await setStore(store?.id);
    useCookie("warehouseId").value = store.warehouseIdDefault
      ? store.warehouseIdDefault
      : store?.warehouses?.[0];

    // Note: orderStore.setStoreId method not available in current store implementation

    // Set store cookie for compatibility
    useCookie("store").value = store;

    // Ensure SDK has token set
    const $sdk = useNuxtApp().$sdk;
    const token = useCookie("token").value;
    if (token && $sdk.setToken) {
      $sdk.setToken(token);
      console.log("🔑 SDK token refreshed");
    }

    console.log("✅ STEP 2 COMPLETE: Store context set:", {
      storeId: store.id,
      storeName: store.name,
      warehouses: store.warehouses?.length || 0,
      warehouseId: useCookie("warehouseId").value,
      hasToken: !!token
    });

    console.log("👤 Final user auth data:", auth);
    console.log("🔑 Final user roles:", auth?.user?.roles);
    console.log("🚀 Navigating with orgId:", orgId, "storeId:", store.id);

    await navigateTo(`/feature?orgId=${orgId}&storeId=${store.id}`);
  } catch (error) {
    console.error("Error selecting store:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi chọn cửa hàng");
  } finally {
    isLoading.value = false;
    selectedStore.value = null;
  }
};

// Initialize data
onMounted(async () => {
  try {
    // Check if user is authenticated
    console.log("🔍 Auth cookie:", auth.value);
    console.log("🔍 Auth store user:", authStore.user);
    console.log("🔍 Auth cookie user ID:", auth.value?.user?.id);
    console.log("🔍 Auth store user ID:", authStore.user?.id);

    const userId = auth.value?.user?.id || authStore.user?.id;

    if (!userId) {
      console.log("User not authenticated, redirecting to login");
      // await navigateTo('/login');
      return;
    }

    console.log("User authenticated, loading stores for:", userId);

    // Load all stores for user
    await loadAllStores();

    // Auto-select organization from cookie if available
    const orgIdCookie = useCookie("orgId");
    console.log("🍪 Checking orgId cookie:", orgIdCookie.value);

    if (orgIdCookie.value && orgIdCookie.value !== 'N/A') {
      // Find organization name from orgId
      const orgNames = Object.keys(sortedGroupedStores.value);
      console.log("🏢 Available organizations:", orgNames);

      // For now, if we have orgId cookie, select the first available org
      // TODO: Map orgId to orgName properly
      if (orgNames.length > 0) {
        const orgToSelect = orgNames[0]; // Temporary - should map orgId to name
        console.log("🎯 Auto-selecting organization:", orgToSelect);
        selectedOrg.value = orgToSelect;

        // Set filter to POS tab
        await nextTick();
        const orgStores = sortedGroupedStores.value[orgToSelect];
        if (orgStores && orgStores.length > 0) {
          const storeTypes = [...new Set(orgStores.map((store: any) => store.type))];
          if (storeTypes.includes('pos') || storeTypes.includes('POS')) {
            filterStoreType.value = 'pos';
          } else if (storeTypes.includes('website') || storeTypes.includes('Website')) {
            filterStoreType.value = 'website';
          }
        }
      }
    }
  } catch (error) {
    console.error("Error loading stores:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi tải danh sách cửa hàng");
  }
});

// Page metadata
definePageMeta({
  middleware: ["auth"], // Require authentication
});
</script>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Custom scrollbar for sidebar */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Card hover effects */
.group:hover .group-hover\:scale-\[1\.02\] {
  transform: scale(1.02);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .w-72 {
    width: 100%;
    max-width: 320px;
  }

  /* Ensure proper mobile scrolling */
  .scrollbar-hide {
    -webkit-overflow-scrolling: touch;
  }

  /* Better mobile grid spacing */
  .grid {
    gap: 1rem;
  }

  /* Mobile-optimized store cards */
  .store-card {
    min-height: 140px;
  }
}

/* Line clamp utility for mobile */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
